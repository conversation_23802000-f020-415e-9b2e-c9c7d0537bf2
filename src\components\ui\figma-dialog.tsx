'use client'

import { forwardRef } from 'react'
import { X } from 'lucide-react'
import { cn } from '@/lib/utils'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'

export interface FigmaDialogProps {
  /** 是否打开 */
  open?: boolean
  /** 打开状态变化回调 */
  onOpenChange?: (open: boolean) => void
  /** 触发器内容 */
  trigger?: React.ReactNode
  /** 标题文本 */
  title?: string
  /** 输入占位符文本 */
  placeholder?: string
  /** 用户头像URL */
  avatarUrl?: string
  /** 用户头像fallback */
  avatarFallback?: string
  /** 推荐标签列表 */
  tags?: string[]
  /** 猜你喜欢标题 */
  suggestionsTitle?: string
  /** 输入值 */
  value?: string
  /** 输入变化回调 */
  onValueChange?: (value: string) => void
  /** 提交回调 */
  onSubmit?: (value: string) => void
  /** 标签点击回调 */
  onTagClick?: (tag: string) => void
  /** 自定义样式 */
  className?: string
}

const FigmaDialogComponent = forwardRef<HTMLDivElement, FigmaDialogProps>(
  (
    {
      open,
      onOpenChange,
      trigger,
      title = '今天您对什么感兴趣？',
      placeholder = '输入您想挖掘的资讯',
      avatarUrl,
      avatarFallback = 'U',
      tags = ['外汇市场', '中国新闻', '人工智能'],
      suggestionsTitle = '猜你喜欢',
      value = '',
      onValueChange,
      onSubmit,
      onTagClick,
      className,
    },
    ref
  ) => {
    const handleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === 'Enter' && value.trim()) {
        onSubmit?.(value.trim())
      }
    }

    const handleTagClick = (tag: string) => {
      onTagClick?.(tag)
      onValueChange?.(tag)
    }

    return (
      <Dialog open={open ?? false} onOpenChange={onOpenChange || (() => {})}>
        {trigger && <DialogTrigger asChild>{trigger}</DialogTrigger>}

        <DialogContent
          ref={ref}
          className={cn('figma-dialog-content', className)}
          showCloseButton={false}
        >
          {/* 自定义关闭按钮 */}
          <Button
            variant="ghost"
            size="sm"
            className="figma-dialog-close"
            onClick={() => onOpenChange?.(false)}
          >
            <X className="w-3 h-3 text-[#9CA3AF]" strokeWidth={2} />
          </Button>

          {/* 用户头像 */}
          <div className="figma-dialog-avatar">
            <Avatar className="w-[53px] h-[53px]">
              {avatarUrl && <AvatarImage src={avatarUrl} />}
              <AvatarFallback className="bg-[#DBEAFE] text-[#252525] text-sm font-normal">
                {avatarFallback}
              </AvatarFallback>
            </Avatar>
          </div>

          {/* 装饰性图标 */}
          <div className="figma-dialog-decoration" />

          <DialogHeader className="figma-dialog-header">
            <DialogTitle className="figma-dialog-title">{title}</DialogTitle>
          </DialogHeader>

          {/* 输入区域 */}
          <div className="figma-dialog-input-container">
            <input
              type="text"
              value={value}
              onChange={e => onValueChange?.(e.target.value)}
              onKeyDown={handleInputKeyDown}
              placeholder={placeholder}
              className="figma-dialog-input"
            />

            {/* 搜索图标 */}
            <div className="figma-dialog-search-icon">
              <div className="w-[37px] h-[37px] rounded-full bg-[#5D5D5D] border border-white flex items-center justify-center">
                <div className="w-1 h-2 bg-white rounded-sm" />
              </div>
            </div>
          </div>

          {/* 推荐标签区域 */}
          <div className="figma-dialog-suggestions">
            <h3 className="figma-dialog-suggestions-title">{suggestionsTitle}</h3>

            <div className="figma-dialog-tags">
              {tags.map((tag, index) => (
                <button
                  key={index}
                  onClick={() => handleTagClick(tag)}
                  className="figma-dialog-tag"
                >
                  {tag}
                </button>
              ))}
            </div>
          </div>
        </DialogContent>
      </Dialog>
    )
  }
)

FigmaDialogComponent.displayName = 'FigmaDialog'

export const FigmaDialog = FigmaDialogComponent
