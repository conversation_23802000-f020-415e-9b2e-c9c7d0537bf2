'use client'

/**
 * useMobileOptimization - 移动端优化Hook
 *
 * 提供移动端特定的优化功能：
 * - 设备检测
 * - 触摸交互优化
 * - 视口管理
 * - 键盘处理
 */

import { useEffect, useState, useCallback } from 'react'

interface MobileOptimizationOptions {
  /** 是否启用视口锁定（防止缩放） */
  enableViewportLock?: boolean
  /** 是否启用键盘感知 */
  enableKeyboardAware?: boolean
  /** 是否启用触摸反馈 */
  enableTouchFeedback?: boolean
  /** 调试模式 */
  debug?: boolean
}

interface MobileOptimizationResult {
  /** 是否为移动设备 */
  isMobile: boolean
  /** 是否为触摸设备 */
  isTouchDevice: boolean
  /** 屏幕尺寸类别 */
  screenSize: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  /** 设备方向 */
  orientation: 'portrait' | 'landscape'
  /** 键盘是否可见（仅移动端） */
  isKeyboardVisible: boolean
  /** 视口高度（考虑键盘影响） */
  viewportHeight: number
  /** 安全区域信息 */
  safeArea: {
    top: number
    bottom: number
    left: number
    right: number
  }
}

export const useMobileOptimization = ({
  enableViewportLock = true,
  enableKeyboardAware = true,
  enableTouchFeedback = true,
  debug = false,
}: MobileOptimizationOptions = {}): MobileOptimizationResult => {
  // 设备检测状态
  const [isMobile, setIsMobile] = useState(false)
  const [isTouchDevice, setIsTouchDevice] = useState(false)
  const [screenSize, setScreenSize] = useState<'xs' | 'sm' | 'md' | 'lg' | 'xl'>('md')
  const [orientation, setOrientation] = useState<'portrait' | 'landscape'>('portrait')

  // 键盘和视口状态
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false)
  const [viewportHeight, setViewportHeight] = useState(0)
  const [safeArea, setSafeArea] = useState({
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
  })

  // 🎯 设备检测
  const detectDevice = useCallback(() => {
    if (typeof window === 'undefined') return

    // 移动设备检测
    const userAgent = navigator.userAgent || navigator.vendor
    const mobileRegex = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i
    const isMobileDevice = mobileRegex.test(userAgent) || window.innerWidth < 768

    // 触摸设备检测
    const touchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0

    // 屏幕尺寸检测
    const width = window.innerWidth
    let size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md'
    if (width < 475) size = 'xs'
    else if (width < 640) size = 'sm'
    else if (width < 768) size = 'md'
    else if (width < 1024) size = 'lg'
    else size = 'xl'

    // 方向检测
    const orient = window.innerHeight > window.innerWidth ? 'portrait' : 'landscape'

    setIsMobile(isMobileDevice)
    setIsTouchDevice(touchDevice)
    setScreenSize(size)
    setOrientation(orient)

    if (debug) {
      // eslint-disable-next-line no-console
      console.log('📱 Device Detection:', {
        isMobile: isMobileDevice,
        isTouch: touchDevice,
        screenSize: size,
        orientation: orient,
        dimensions: { width, height: window.innerHeight },
      })
    }
  }, [debug])

  // 🎯 安全区域检测
  const detectSafeArea = useCallback(() => {
    if (typeof window === 'undefined') return

    const computedStyle = getComputedStyle(document.documentElement)
    const safeAreaData = {
      top: parseInt(computedStyle.getPropertyValue('env(safe-area-inset-top)') || '0'),
      bottom: parseInt(computedStyle.getPropertyValue('env(safe-area-inset-bottom)') || '0'),
      left: parseInt(computedStyle.getPropertyValue('env(safe-area-inset-left)') || '0'),
      right: parseInt(computedStyle.getPropertyValue('env(safe-area-inset-right)') || '0'),
    }

    setSafeArea(safeAreaData)

    if (debug) {
      // eslint-disable-next-line no-console
      console.log('📱 Safe Area:', safeAreaData)
    }
  }, [debug])

  // 🎯 键盘感知（仅移动端）
  const handleViewportChange = useCallback(() => {
    if (typeof window === 'undefined' || !enableKeyboardAware) return

    const currentHeight = window.visualViewport?.height ?? window.innerHeight
    const windowHeight = window.innerHeight

    // 当视口高度显著减少时，认为键盘弹出
    const heightDifference = windowHeight - currentHeight
    const keyboardVisible = heightDifference > 150 // 键盘高度阈值

    setViewportHeight(currentHeight)
    setIsKeyboardVisible(keyboardVisible)

    if (debug) {
      // eslint-disable-next-line no-console
      console.log('📱 Viewport Change:', {
        windowHeight,
        viewportHeight: currentHeight,
        heightDifference,
        keyboardVisible,
      })
    }
  }, [enableKeyboardAware, debug])

  // 🎯 视口锁定
  const setupViewportLock = useCallback(() => {
    if (!enableViewportLock || typeof document === 'undefined') return

    // 添加viewport meta标签防止缩放
    let viewportMeta = document.querySelector('meta[name="viewport"]')
    if (!viewportMeta) {
      viewportMeta = document.createElement('meta')
      viewportMeta.setAttribute('name', 'viewport')
      document.head.appendChild(viewportMeta)
    }

    const currentContent = viewportMeta.getAttribute('content') || ''
    if (!currentContent.includes('user-scalable=no')) {
      const newContent = currentContent
        ? `${currentContent}, user-scalable=no, maximum-scale=1.0`
        : 'width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0'

      viewportMeta.setAttribute('content', newContent)
    }
  }, [enableViewportLock])

  // 🎯 触摸反馈
  const setupTouchFeedback = useCallback(() => {
    if (!enableTouchFeedback || typeof document === 'undefined') return

    // 添加全局触摸样式
    const style = document.createElement('style')
    style.textContent = `
      * {
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        user-select: none;
      }
      
      input, textarea, [contenteditable] {
        -webkit-user-select: text;
        user-select: text;
      }
      
      button, [role="button"], a {
        touch-action: manipulation;
      }
    `
    document.head.appendChild(style)

    return () => {
      document.head.removeChild(style)
    }
  }, [enableTouchFeedback])

  // 初始化
  useEffect(() => {
    detectDevice()
    detectSafeArea()
    handleViewportChange()
    setupViewportLock()
    const touchCleanup = setupTouchFeedback()

    // 事件监听器
    window.addEventListener('resize', detectDevice)
    window.addEventListener('resize', handleViewportChange)
    window.addEventListener('orientationchange', detectDevice)

    if (window.visualViewport) {
      window.visualViewport.addEventListener('resize', handleViewportChange)
    }

    return () => {
      window.removeEventListener('resize', detectDevice)
      window.removeEventListener('resize', handleViewportChange)
      window.removeEventListener('orientationchange', detectDevice)

      if (window.visualViewport) {
        window.visualViewport.removeEventListener('resize', handleViewportChange)
      }

      touchCleanup?.()
    }
  }, [detectDevice, detectSafeArea, handleViewportChange, setupViewportLock, setupTouchFeedback])

  return {
    isMobile,
    isTouchDevice,
    screenSize,
    orientation,
    isKeyboardVisible,
    viewportHeight: viewportHeight || (typeof window !== 'undefined' ? window.innerHeight : 0),
    safeArea,
  }
}
