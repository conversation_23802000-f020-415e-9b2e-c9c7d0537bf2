/**
 * 连接管理相关类型定义
 * 提供统一的连接接口抽象
 */

import type { BaseWebSocketMessage } from '@/types/websocket-event-type'

// ============================================================================
// 连接状态枚举
// ============================================================================

export enum ConnectionStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error',
}

export enum ConnectionType {
  SOCKET = 'socket',
  MOCK = 'mock',
}

// ============================================================================
// 连接结果类型
// ============================================================================

export interface ConnectionResult {
  success: boolean
  type: ConnectionType
  message?: string
  error?: string
}

// ============================================================================
// 消息处理器类型
// ============================================================================

export type MessageHandler = (message: BaseWebSocketMessage) => void
export type StatusHandler = (status: ConnectionStatus) => void
export type ErrorHandler = (error: Error) => void

// ============================================================================
// 连接配置类型
// ============================================================================

export interface ConnectionConfig {
  url?: string
  timeout?: number
  reconnection?: boolean
  reconnectionAttempts?: number
  reconnectionDelay?: number
  auth?: {
    token?: string
    user_id?: string
    group_chat_id?: string
    organization_id?: string
    // 保持向后兼容
    userId?: string
    organizationId?: string
  }
  // Mock模式配置
  forceMockMode?: boolean
  mockScenario?: string
  enableScenarioSwitching?: boolean
}

// ============================================================================
// 连接统计信息
// ============================================================================

export interface ConnectionStats {
  connectedAt?: Date
  lastMessageAt?: Date
  messagesSent: number
  messagesReceived: number
  reconnectCount: number
  errors: number
}

// ============================================================================
// 核心连接管理器接口
// ============================================================================

export interface IConnectionManager {
  // 基础连接方法
  connect(config?: ConnectionConfig): Promise<ConnectionResult>
  disconnect(): Promise<void>

  // 消息收发
  sendMessage(message: BaseWebSocketMessage): Promise<void>

  // 事件监听
  onMessage(handler: MessageHandler): () => void
  onStatus(handler: StatusHandler): () => void
  onError(handler: ErrorHandler): () => void

  // 状态查询
  getStatus(): ConnectionStatus
  getType(): ConnectionType
  getStats(): ConnectionStats
  isConnected(): boolean

  // Mock模式特有方法（可选）
  switchScenario?(scenarioId: string): boolean
  getCurrentScenario?(): any
  getAvailableScenarios?(): any[]
  setAutoReply?(enabled: boolean): void
  triggerScenario?(scenarioId?: string): void

  // 清理资源
  destroy(): void
}

// ============================================================================
// 环境检测结果
// ============================================================================

export interface EnvironmentInfo {
  isDevelopment: boolean
  hasBackendUrl: boolean
  preferredType: ConnectionType
  socketUrl: string | undefined
}
