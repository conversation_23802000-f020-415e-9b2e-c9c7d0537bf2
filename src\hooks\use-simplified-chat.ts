/**
 * 简化聊天Hook - 去除过度工程化设计
 *
 * 功能：基本的聊天状态管理，直接使用stores
 * 依赖：各种Store hooks，无复杂的数据流管理器
 * 性能：轻量级hook，专注核心聊天功能
 *
 * 从535行复杂实现简化为 ~100行，去除：
 * - 复杂的数据流管理器依赖
 * - 过度的性能监控和事件系统
 * - 复杂的配置和抽象接口
 * - 冗余的辅助hooks
 */

import { useCallback, useMemo } from 'react'
import { ConnectionStatus } from '@/lib/connection/types'

// Store Hooks
import { useConnection } from '@/stores/connection-store'
import { useSession } from '@/stores/session-store'
import { useMessages } from '@/stores/message-store'
import { useUserInput } from '@/stores/ui-store'

// 类型
import type { BaseWebSocketMessage } from '@/types/websocket-event-type'
import type { ChatSession } from '@/stores/session-store'

// ============================================================================
// 简化的聊天接口
// ============================================================================

export interface SimplifiedChatState {
  // 连接状态
  isConnected: boolean
  connectionStatus: ConnectionStatus
  connectionError: string | undefined

  // 会话状态
  currentSession: ChatSession | null
  hasActiveSession: boolean

  // 消息状态
  messages: BaseWebSocketMessage[]
  messageCount: number

  // 输入状态
  userInput: string
  isSubmitting: boolean
  canSend: boolean
}

export interface SimplifiedChatActions {
  // 消息操作
  sendMessage: (content: string) => Promise<void>
  deleteMessage: (messageId: string) => void

  // 会话操作
  switchToGroup: (groupId: string, userId: string, organizationId: string) => void

  // 输入操作
  updateInput: (content: string) => void
  clearInput: () => void

  // 连接操作
  connect: () => Promise<void>
  disconnect: () => void
}

// ============================================================================
// 主要Hook实现
// ============================================================================

export const useSimplifiedChat = (): SimplifiedChatState & SimplifiedChatActions => {
  // Store状态获取
  const { connectionStatus, isConnected, connect, disconnect, lastError } = useConnection()
  const { currentSession, ensureSessionForGroup } = useSession()
  const { messages, sendUserMessage, deleteMessage: deleteMsg } = useMessages()
  const { userInput, updateUserInput, clearUserInput } = useUserInput()

  // 计算状态
  const state = useMemo(
    (): SimplifiedChatState => ({
      // 连接状态
      isConnected,
      connectionStatus,
      connectionError: lastError,

      // 会话状态
      currentSession: currentSession || null,
      hasActiveSession: !!currentSession,

      // 消息状态
      messages,
      messageCount: messages.length,

      // 输入状态
      userInput: userInput.content,
      isSubmitting: userInput.isSubmitting,
      canSend: !!(userInput.content.trim() && isConnected && !userInput.isSubmitting),
    }),
    [isConnected, connectionStatus, lastError, currentSession, messages, userInput]
  )

  // 操作方法
  const sendMessage = useCallback(
    async (content: string) => {
      if (!content.trim() || !isConnected || userInput.isSubmitting || !currentSession) {
        return
      }

      try {
        await sendUserMessage(content.trim(), currentSession.sessionId)
        clearUserInput()
      } catch (error) {
        console.error('Failed to send message:', error)
        throw error
      }
    },
    [isConnected, userInput.isSubmitting, currentSession, sendUserMessage, clearUserInput]
  )

  const deleteMessage = useCallback(
    (messageId: string) => {
      deleteMsg(messageId)
    },
    [deleteMsg]
  )

  const switchToGroup = useCallback(
    (groupId: string, userId: string, organizationId: string) => {
      ensureSessionForGroup(groupId, userId, organizationId)
    },
    [ensureSessionForGroup]
  )

  const updateInput = useCallback(
    (content: string) => {
      updateUserInput(content)
    },
    [updateUserInput]
  )

  const clearInput = useCallback(() => {
    clearUserInput()
  }, [clearUserInput])

  return {
    // 状态
    ...state,

    // 操作
    sendMessage,
    deleteMessage,
    switchToGroup,
    updateInput,
    clearInput,
    connect,
    disconnect,
  }
}

export default useSimplifiedChat
