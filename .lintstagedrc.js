/**
 * Lint-staged 配置
 * 在git提交前对暂存文件进行自动检查和修复
 */
module.exports = {
  // TypeScript和JavaScript文件
  '*.{ts,tsx,js,jsx}': [
    // 1. ESLint检查并自动修复
    'eslint --fix',
    // 2. Prettier格式化
    'prettier --write',
    // 3. TypeScript类型检查（仅检查相关文件）
    () => 'tsc --noEmit --incremental',
  ],

  // 样式文件
  '*.{css,scss,less}': ['prettier --write'],

  // JSON, YAML, Markdown文件
  '*.{json,yaml,yml,md}': ['prettier --write'],

  // 配置文件
  '*.{config.js,config.ts}': ['eslint --fix', 'prettier --write'],
}
