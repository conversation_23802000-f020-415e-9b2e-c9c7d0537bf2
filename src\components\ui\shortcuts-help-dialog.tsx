'use client'

/**
 * ShortcutsHelpDialog - 快捷键帮助对话框
 *
 * 以优雅的方式展示所有可用的键盘快捷键
 */

import { motion } from 'framer-motion'
import { X, Keyboard } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'

interface ShortcutsHelpDialogProps {
  /** 是否显示对话框 */
  open: boolean
  /** 关闭对话框回调 */
  onOpenChange: (open: boolean) => void
  /** 快捷键列表 */
  shortcuts: Array<{
    key: string
    description: string
    action: string
  }>
  /** 自定义样式 */
  className?: string
}

export const ShortcutsHelpDialog = ({
  open: _open,
  onOpenChange,
  shortcuts,
  className,
}: ShortcutsHelpDialogProps) => {
  // 按类别组织快捷键
  const categorizedShortcuts = [
    {
      title: '消息操作',
      shortcuts: shortcuts.filter(s => ['send', 'newline', 'clear'].includes(s.action)),
    },
    {
      title: '导航操作',
      shortcuts: shortcuts.filter(s => ['focus', 'clearChat'].includes(s.action)),
    },
    {
      title: '帮助操作',
      shortcuts: shortcuts.filter(s => ['help'].includes(s.action)),
    },
  ]

  // 格式化快捷键显示
  const formatKey = (key: string) => {
    return key.split(' / ').map((part, index) => (
      <span key={index}>
        {index > 0 && <span className="mx-2 text-muted-foreground">或</span>}
        <Badge variant="outline" className="font-mono text-xs px-2 py-1">
          {part.split('+').map((subKey, subIndex) => (
            <span key={subIndex}>
              {subIndex > 0 && <span className="mx-1">+</span>}
              <kbd className="text-xs">{subKey}</kbd>
            </span>
          ))}
        </Badge>
      </span>
    ))
  }

  return (
    <Dialog open={_open} onOpenChange={onOpenChange}>
      <DialogContent className={cn('max-w-md', className)}>
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Keyboard className="w-5 h-5" />
            键盀快捷键
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {categorizedShortcuts.map((category, categoryIndex) => (
            <motion.div
              key={category.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: categoryIndex * 0.1 }}
            >
              <h3 className="text-sm font-semibold text-foreground mb-3">{category.title}</h3>

              <div className="space-y-3">
                {category.shortcuts.map((shortcut, index) => (
                  <motion.div
                    key={shortcut.action}
                    className="flex items-center justify-between py-2 px-3 rounded-lg bg-muted/50 hover:bg-muted transition-colors"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: categoryIndex * 0.1 + index * 0.05 }}
                  >
                    <span className="text-sm text-foreground">{shortcut.description}</span>
                    <div className="flex items-center gap-1">{formatKey(shortcut.key)}</div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          ))}

          {/* 额外提示 */}
          <motion.div
            className="pt-4 border-t"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
          >
            <div className="text-xs text-muted-foreground space-y-2">
              <p className="flex items-center gap-2">
                <span className="w-2 h-2 rounded-full bg-green-500"></span>
                在输入框中使用消息快捷键
              </p>
              <p className="flex items-center gap-2">
                <span className="w-2 h-2 rounded-full bg-blue-500"></span>
                在任何地方使用导航快捷键
              </p>
            </div>
          </motion.div>
        </div>

        {/* 关闭按钮 */}
        <div className="flex justify-end pt-4">
          <Button variant="outline" size="sm" onClick={() => onOpenChange(false)} className="gap-2">
            <X className="w-4 h-4" />
            关闭
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
