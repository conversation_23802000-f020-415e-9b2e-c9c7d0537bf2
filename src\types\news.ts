// 简化的新闻类型定义

// 话题列表API响应格式 {"1": "人工智能发展", "3": "新能源汽车"}
export interface TopicsResponse {
  [key: string]: string
}

// 新闻列表API请求参数
export interface NewsListRequest {
  topic_id: number
  page?: number
  page_size?: number
}

// 创建话题API请求参数
export interface CreateTopicRequest {
  description: string
  input_text: string
}

// 创建话题API响应格式
export interface CreateTopicResponse {
  topic_id: string
  topic_name: string
  message?: string
}

// 新闻项（API原始格式）
export interface NewsItem {
  _id: string
  url: string
  country: string
  title: string
  web_site: string
  language: string
  key_points: string[]
  summary: string
  content: string
  news_type: string
  first_level_industry: string
  second_level_industry: string
  publish_date: string
  create_date: string
  update_date: string
}

// 新闻列表API响应格式
export interface NewsListResponse {
  news_list: NewsItem[]
  total: number
  page: number
  page_size: number
  total_pages: number
}