/**
 * ErrorPayload处理器
 * 负责错误分类、展示、重试机制
 */

import type { ErrorMessage, ErrorPayload } from '@/types/websocket-event-type'

export enum ErrorCategory {
  NETWORK = 'network',
  AUTHENTICATION = 'authentication',
  VALIDATION = 'validation',
  BUSINESS = 'business',
  SYSTEM = 'system',
  TIMEOUT = 'timeout',
  UNKNOWN = 'unknown',
}

export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export interface ProcessedError {
  id?: string
  sessionId?: string
  code: string
  message: string
  category?: ErrorCategory
  severity?: ErrorSeverity
  timestamp?: Date
  isRetryable?: boolean
  retryCount?: number
  maxRetries?: number
  userMessage?: string
  technicalDetails?: string
  recoveryActions?: string[]
  recoverable?: boolean // 添加recoverable字段支持
}

export interface ErrorRecoveryAction {
  label: string
  action: () => Promise<void> | void
  isRetry?: boolean
}

export interface ErrorEvent {
  error: ProcessedError
  recoveryActions: ErrorRecoveryAction[]
}

export interface ErrorListener {
  (event: ErrorEvent): void
}

export interface ErrorRecoveryListener {
  (error: ProcessedError, success: boolean): void
}

export class ErrorProcessor {
  private errors: Map<string, ProcessedError> = new Map()
  private errorListeners: Set<ErrorListener> = new Set()
  private recoveryListeners: Set<ErrorRecoveryListener> = new Set()
  private retryTimeouts: Map<string, NodeJS.Timeout> = new Map()

  // 错误代码映射配置
  private readonly errorMappings: Record<
    string,
    {
      category: ErrorCategory
      severity: ErrorSeverity
      userMessage: string
      isRetryable: boolean
      maxRetries: number
      recoveryActions: string[]
    }
  > = {
    NETWORK_ERROR: {
      category: ErrorCategory.NETWORK,
      severity: ErrorSeverity.MEDIUM,
      userMessage: '网络连接异常，请检查网络连接',
      isRetryable: true,
      maxRetries: 3,
      recoveryActions: ['检查网络连接', '重新连接'],
    },
    AUTH_FAILED: {
      category: ErrorCategory.AUTHENTICATION,
      severity: ErrorSeverity.HIGH,
      userMessage: '认证失败，请重新登录',
      isRetryable: false,
      maxRetries: 0,
      recoveryActions: ['重新登录', '联系管理员'],
    },
    VALIDATION_ERROR: {
      category: ErrorCategory.VALIDATION,
      severity: ErrorSeverity.LOW,
      userMessage: '输入数据格式错误',
      isRetryable: false,
      maxRetries: 0,
      recoveryActions: ['检查输入格式', '修正数据后重试'],
    },
    TIMEOUT_ERROR: {
      category: ErrorCategory.TIMEOUT,
      severity: ErrorSeverity.MEDIUM,
      userMessage: '请求超时，请稍后重试',
      isRetryable: true,
      maxRetries: 2,
      recoveryActions: ['稍后重试', '检查网络连接'],
    },
    BUSINESS_ERROR: {
      category: ErrorCategory.BUSINESS,
      severity: ErrorSeverity.MEDIUM,
      userMessage: '业务处理异常',
      isRetryable: false,
      maxRetries: 0,
      recoveryActions: ['检查输入内容', '联系技术支持'],
    },
    SYSTEM_ERROR: {
      category: ErrorCategory.SYSTEM,
      severity: ErrorSeverity.CRITICAL,
      userMessage: '系统异常，请联系技术支持',
      isRetryable: true,
      maxRetries: 1,
      recoveryActions: ['联系技术支持', '稍后重试'],
    },
  }

  // 处理错误消息
  processErrorMessage(message: ErrorMessage): void {
    try {
      const processedError = this.processErrorPayload(
        message.sessionId,
        message.payload,
        message.timestamp
      )
      const recoveryActions = this.generateRecoveryActions(processedError)

      // 存储错误
      this.errors.set(processedError.id || '', processedError)

      // 通知监听器
      this.notifyError({
        error: processedError,
        recoveryActions,
      })

      // 如果支持自动重试，安排重试
      if (
        processedError.isRetryable &&
        processedError.retryCount &&
        processedError.maxRetries &&
        processedError.retryCount < processedError.maxRetries
      ) {
        this.scheduleRetry(processedError)
      }
    } catch (error) {
      console.error('Error processing error message:', error)
    }
  }

  // 处理ErrorPayload数据
  private processErrorPayload(
    sessionId: string,
    payload: ErrorPayload,
    timestamp: string
  ): ProcessedError {
    const errorId = this.generateErrorId(sessionId, timestamp)
    const mapping = this.errorMappings[payload.code] || this.getDefaultErrorMapping()

    return {
      id: errorId,
      sessionId,
      code: payload.code,
      message: payload.message,
      category: mapping.category,
      severity: mapping.severity,
      timestamp: new Date(timestamp),
      isRetryable: mapping.isRetryable,
      retryCount: 0,
      maxRetries: mapping.maxRetries,
      userMessage: mapping.userMessage,
      technicalDetails: payload.message,
      recoveryActions: mapping.recoveryActions,
    }
  }

  // 生成错误ID
  private generateErrorId(sessionId: string, timestamp: string): string {
    return `error-${sessionId}-${new Date(timestamp).getTime()}`
  }

  // 获取默认错误映射
  private getDefaultErrorMapping() {
    return {
      category: ErrorCategory.UNKNOWN,
      severity: ErrorSeverity.MEDIUM,
      userMessage: '发生未知错误',
      isRetryable: false,
      maxRetries: 0,
      recoveryActions: ['刷新页面', '联系技术支持'],
    }
  }

  // 生成恢复操作
  private generateRecoveryActions(error: ProcessedError): ErrorRecoveryAction[] {
    const actions: ErrorRecoveryAction[] = []

    // 重试操作
    if (
      error.isRetryable &&
      error.retryCount &&
      error.maxRetries &&
      error.retryCount < error.maxRetries
    ) {
      actions.push({
        label: '重试',
        action: () => this.retryError(error.id || ''),
        isRetry: true,
      })
    }

    // 通用恢复操作
    error.recoveryActions?.forEach(actionLabel => {
      actions.push({
        label: actionLabel,
        action: () => this.executeRecoveryAction(actionLabel, error),
      })
    })

    return actions
  }

  // 安排重试
  private scheduleRetry(error: ProcessedError): void {
    const retryDelay = this.calculateRetryDelay(error.retryCount || 0)

    const timeout = setTimeout(() => {
      this.retryError(error.id || '')
    }, retryDelay)

    this.retryTimeouts.set(error.id || '', timeout)
  }

  // 计算重试延迟（指数退避）
  private calculateRetryDelay(retryCount: number): number {
    return Math.min(1000 * Math.pow(2, retryCount), 30000) // 最大30秒
  }

  // 重试错误
  async retryError(errorId: string): Promise<void> {
    const error = this.errors.get(errorId)
    if (
      !error ||
      !error.isRetryable ||
      (error.retryCount && error.maxRetries && error.retryCount >= error.maxRetries)
    ) {
      return
    }

    // 清理重试超时
    const timeout = this.retryTimeouts.get(errorId)
    if (timeout) {
      clearTimeout(timeout)
      this.retryTimeouts.delete(errorId)
    }

    // 增加重试计数
    error.retryCount = (error.retryCount || 0) + 1

    try {
      // 这里应该触发原始操作的重试
      // 具体实现取决于上层的重试逻辑
      await this.executeRetry(error)

      // 重试成功
      this.notifyRecovery(error, true)
      this.errors.delete(errorId)
    } catch (retryError) {
      // 重试失败
      this.notifyRecovery(error, false)

      // 如果还有重试次数，继续安排重试
      if (error.retryCount && error.maxRetries && error.retryCount < error.maxRetries) {
        this.scheduleRetry(error)
      }
    }
  }

  // 执行重试（抽象方法，需要外部实现）
  private async executeRetry(error: ProcessedError): Promise<void> {
    // 这里需要与外部系统集成，执行具体的重试逻辑
    throw new Error('Retry execution not implemented')
  }

  // 执行恢复操作
  private executeRecoveryAction(actionLabel: string, error: ProcessedError): void {
    switch (actionLabel) {
      case '刷新页面':
        if (typeof window !== 'undefined') {
          window.location.reload()
        }
        break
      case '重新连接':
        // 触发重新连接逻辑
        break
      case '重新登录':
        // 触发重新登录逻辑
        break
      case '联系技术支持':
        // 显示技术支持联系方式
        break
      default:
        console.log(`执行恢复操作: ${actionLabel}`)
    }
  }

  // 获取错误信息
  getError(errorId: string): ProcessedError | null {
    return this.errors.get(errorId) || null
  }

  // 获取会话的所有错误
  getSessionErrors(sessionId: string): ProcessedError[] {
    const errors: ProcessedError[] = []
    this.errors.forEach(error => {
      if (error.sessionId === sessionId) {
        errors.push({ ...error })
      }
    })
    return errors.sort((a, b) => (b.timestamp?.getTime() || 0) - (a.timestamp?.getTime() || 0))
  }

  // 清除错误
  clearError(errorId: string): void {
    const timeout = this.retryTimeouts.get(errorId)
    if (timeout) {
      clearTimeout(timeout)
      this.retryTimeouts.delete(errorId)
    }
    this.errors.delete(errorId)
  }

  // 清除会话的所有错误
  clearSessionErrors(sessionId: string): void {
    const errorIds: string[] = []
    this.errors.forEach((error, id) => {
      if (error.sessionId === sessionId) {
        errorIds.push(id)
      }
    })
    errorIds.forEach(id => this.clearError(id))
  }

  // 清理过期错误
  cleanupOldErrors(maxAge: number = 10 * 60 * 1000): void {
    // 10分钟
    const now = new Date()
    const expiredErrors: string[] = []

    this.errors.forEach((error, id) => {
      const age = now.getTime() - (error.timestamp?.getTime() || 0)
      if (age > maxAge) {
        expiredErrors.push(id)
      }
    })

    expiredErrors.forEach(id => this.clearError(id))
  }

  // 事件监听器管理
  addErrorListener(listener: ErrorListener): void {
    this.errorListeners.add(listener)
  }

  removeErrorListener(listener: ErrorListener): void {
    this.errorListeners.delete(listener)
  }

  addRecoveryListener(listener: ErrorRecoveryListener): void {
    this.recoveryListeners.add(listener)
  }

  removeRecoveryListener(listener: ErrorRecoveryListener): void {
    this.recoveryListeners.delete(listener)
  }

  // 清理所有监听器
  clearAllListeners(): void {
    this.errorListeners.clear()
    this.recoveryListeners.clear()
  }

  // 获取统计信息
  getStats(): {
    totalErrors: number
    errorsByCategory: Record<ErrorCategory, number>
    errorsBySeverity: Record<ErrorSeverity, number>
    retriableErrors: number
    pendingRetries: number
  } {
    const stats = {
      totalErrors: this.errors.size,
      errorsByCategory: {} as Record<ErrorCategory, number>,
      errorsBySeverity: {} as Record<ErrorSeverity, number>,
      retriableErrors: 0,
      pendingRetries: this.retryTimeouts.size,
    }

    // 初始化计数器
    Object.values(ErrorCategory).forEach(category => {
      stats.errorsByCategory[category] = 0
    })
    Object.values(ErrorSeverity).forEach(severity => {
      stats.errorsBySeverity[severity] = 0
    })

    // 统计错误
    this.errors.forEach(error => {
      stats.errorsByCategory[error.category || ErrorCategory.UNKNOWN] =
        (stats.errorsByCategory[error.category || ErrorCategory.UNKNOWN] || 0) + 1
      stats.errorsBySeverity[error.severity || ErrorSeverity.MEDIUM] =
        (stats.errorsBySeverity[error.severity || ErrorSeverity.MEDIUM] || 0) + 1
      if (error.isRetryable) {
        stats.retriableErrors++
      }
    })

    return stats
  }

  // 私有方法：通知错误
  private notifyError(event: ErrorEvent): void {
    this.errorListeners.forEach(listener => {
      try {
        listener(event)
      } catch (error) {
        console.error('Error in error listener:', error)
      }
    })
  }

  // 私有方法：通知恢复
  private notifyRecovery(error: ProcessedError, success: boolean): void {
    this.recoveryListeners.forEach(listener => {
      try {
        listener(error, success)
      } catch (error) {
        console.error('Error in recovery listener:', error)
      }
    })
  }
}
