/**
 * 群聊状态管理 Store
 * 使用 Zustand 和 Immer 实现群聊选择、偏好设置和状态持久化
 */

import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'
import { persist } from 'zustand/middleware'
import { shallow } from 'zustand/shallow'
import { useMemo } from 'react'

// 群聊优先级类型
export type ChatGroupPriority = 'normal' | 'high' | 'highest' | 'pinned'

// 群聊信息接口
export interface ChatGroupInfo {
  id: string
  title: string
  time: string
  description: string
  avatarBorderColor: string
  hasNotification: boolean
  lastReadMessageId?: string
  unreadCount?: number
  priority: ChatGroupPriority
  memberCount?: number
  createdAt?: Date
  updatedAt?: Date
}

// 群聊偏好设置
export interface ChatGroupPreferences {
  notificationsEnabled: boolean
  soundEnabled: boolean
  autoMarkAsRead: boolean
  showTimestamps: boolean
  compactMode: boolean
  theme: 'light' | 'dark' | 'system'
}

// Store 状态接口
interface ChatGroupState {
  // 群聊数据
  groups: ChatGroupInfo[]
  selectedGroupId: string | null

  // UI 状态
  isLoading: boolean
  error: string | null

  // 偏好设置
  preferences: ChatGroupPreferences

  // 搜索和过滤
  searchQuery: string
  filteredGroups: ChatGroupInfo[]

  // 缓存的计算值
  _cachedUnreadCount: number
  _cachedHasNotifications: boolean

  // 操作方法
  setGroups: (groups: ChatGroupInfo[]) => void
  selectGroup: (groupId: string | null) => void
  updateGroup: (groupId: string, updates: Partial<ChatGroupInfo>) => void
  addGroup: (group: ChatGroupInfo) => void
  removeGroup: (groupId: string) => void

  // 通知相关
  markAsRead: (groupId: string, messageId?: string) => void
  incrementUnreadCount: (groupId: string) => void
  
  // 优先级相关
  setPriority: (groupId: string, priority: ChatGroupPriority) => void

  // 搜索和过滤
  setSearchQuery: (query: string) => void
  filterGroups: () => void

  // 偏好设置
  updatePreferences: (updates: Partial<ChatGroupPreferences>) => void

  // 状态管理
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  clearError: () => void

  // 工具方法
  getGroupById: (groupId: string) => ChatGroupInfo | undefined

  // 内部方法：重新计算缓存值
  _recalculateCache: () => void
}

// 默认偏好设置
const defaultPreferences: ChatGroupPreferences = {
  notificationsEnabled: true,
  soundEnabled: true,
  autoMarkAsRead: true,
  showTimestamps: true,
  compactMode: false,
  theme: 'system',
}

// 辅助函数：更新缓存
const updateCache = (state: any) => {
  state._cachedUnreadCount = state.groups.reduce(
    (total: number, group: any) => total + (group.unreadCount || 0),
    0
  )
  state._cachedHasNotifications = state.groups.some((g: any) => g.hasNotification)
}

// 创建 Zustand Store
export const useChatGroupStore = create<ChatGroupState>()(
  persist(
    immer((set, get) => ({
      // 初始状态
      groups: [],
      selectedGroupId: null,
      isLoading: false,
      error: null,
      preferences: defaultPreferences,
      searchQuery: '',
      filteredGroups: [],
      _cachedUnreadCount: 0,
      _cachedHasNotifications: false,

      // 群聊数据管理
      setGroups: groups =>
        set(state => {
          state.groups = groups
          state.filteredGroups = groups
          updateCache(state)
        }),

      selectGroup: groupId =>
        set(state => {
          state.selectedGroupId = groupId
          // 选择群聊时自动标记为已读
          if (groupId && state.preferences.autoMarkAsRead) {
            const group = state.groups.find(g => g.id === groupId)
            if (group) {
              group.unreadCount = 0
              group.hasNotification = false
            }
          }
          updateCache(state)
        }),

      updateGroup: (groupId, updates) =>
        set(state => {
          const group = state.groups.find(g => g.id === groupId)
          if (group) {
            Object.assign(group, updates)
            group.updatedAt = new Date()
          }
          updateCache(state)
        }),

      addGroup: group =>
        set(state => {
          state.groups.push({
            ...group,
            createdAt: new Date(),
            updatedAt: new Date(),
          })
          updateCache(state)
        }),

      removeGroup: groupId =>
        set(state => {
          state.groups = state.groups.filter(g => g.id !== groupId)
          state.filteredGroups = state.filteredGroups.filter(g => g.id !== groupId)
          if (state.selectedGroupId === groupId) {
            state.selectedGroupId = null
          }
          updateCache(state)
        }),

      // 通知相关
      markAsRead: (groupId, messageId) =>
        set(state => {
          const group = state.groups.find(g => g.id === groupId)
          if (group) {
            group.unreadCount = 0
            group.hasNotification = false
            if (messageId) {
              group.lastReadMessageId = messageId
            }
          }
          updateCache(state)
        }),

      incrementUnreadCount: groupId =>
        set(state => {
          const group = state.groups.find(g => g.id === groupId)
          if (group) {
            group.unreadCount = (group.unreadCount || 0) + 1
            group.hasNotification = true
          }
          updateCache(state)
        }),

      // 优先级相关
      setPriority: (groupId, priority) =>
        set(state => {
          const group = state.groups.find(g => g.id === groupId)
          if (group) {
            group.priority = priority
            group.updatedAt = new Date()
          }
          updateCache(state)
        }),

      // 搜索和过滤
      setSearchQuery: query =>
        set(state => {
          state.searchQuery = query
        }),

      filterGroups: () =>
        set(state => {
          const { searchQuery, groups } = state
          if (!searchQuery.trim()) {
            state.filteredGroups = groups
          } else {
            state.filteredGroups = groups.filter(
              group =>
                group.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                group.description.toLowerCase().includes(searchQuery.toLowerCase())
            )
          }
        }),

      // 偏好设置
      updatePreferences: updates =>
        set(state => {
          Object.assign(state.preferences, updates)
        }),

      // 状态管理
      setLoading: loading =>
        set(state => {
          state.isLoading = loading
        }),

      setError: error =>
        set(state => {
          state.error = error
        }),

      clearError: () =>
        set(state => {
          state.error = null
        }),

      // 工具方法
      getGroupById: groupId => {
        return get().groups.find(g => g.id === groupId)
      },

      // 内部方法：重新计算缓存值
      _recalculateCache: () => {
        set(state => {
          state._cachedUnreadCount = state.groups.reduce(
            (total, group) => total + (group.unreadCount || 0),
            0
          )
          state._cachedHasNotifications = state.groups.some(g => g.hasNotification)
        })
      },
    })),
    {
      name: 'chat-group-store',
      // 只持久化部分状态
      partialize: state => ({
        selectedGroupId: state.selectedGroupId,
        preferences: state.preferences,
        groups: state.groups.map(group => ({
          ...group,
          // 不持久化瞬时状态
          hasNotification: false,
          isLoading: false,
        })),
      }),
    }
  )
)

// 创建选择器钩子以优化性能
export const useSelectedGroup = () => {
  const selectedGroupId = useChatGroupStore(state => state.selectedGroupId)
  const groups = useChatGroupStore(state => state.groups)

  return useMemo(() => {
    return groups.find(g => g.id === selectedGroupId) || null
  }, [groups, selectedGroupId])
}

export const useGroupNotifications = () => {
  // 直接使用缓存的值，避免重复计算
  const unreadCount = useChatGroupStore(state => state._cachedUnreadCount)
  const hasNotifications = useChatGroupStore(state => state._cachedHasNotifications)

  // 使用 useMemo 来确保返回的对象引用稳定
  return useMemo(
    () => ({
      unreadCount,
      hasNotifications,
    }),
    [unreadCount, hasNotifications]
  )
}

export const useGroupSearch = () => {
  return useChatGroupStore(state => ({
    searchQuery: state.searchQuery,
    filteredGroups: state.filteredGroups,
    setSearchQuery: state.setSearchQuery,
    filterGroups: state.filterGroups,
  }))
}

export const useGroupPreferences = () => {
  return useChatGroupStore(state => ({
    preferences: state.preferences,
    updatePreferences: state.updatePreferences,
  }))
}

// 其他有用的选择器钩子
export const useGroupById = (groupId: string) => {
  const groups = useChatGroupStore(state => state.groups)

  return useMemo(() => {
    return groups.find(g => g.id === groupId) || null
  }, [groups, groupId])
}

// 根据优先级排序的群组
export const useSortedGroups = () => {
  const groups = useChatGroupStore(state => state.groups)

  return useMemo(() => {
    const priorityOrder: Record<ChatGroupPriority, number> = {
      pinned: 4,
      highest: 3,
      high: 2,
      normal: 1,
    }

    return groups
      .slice() // 创建浅拷贝，避免修改原数组
      .sort((a, b) => {
        // 首先按优先级排序
        const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority]
        if (priorityDiff !== 0) return priorityDiff
        
        // 相同优先级按更新时间排序
        const aTime = a.updatedAt instanceof Date ? a.updatedAt.getTime() : (a.updatedAt ? new Date(a.updatedAt).getTime() : 0)
        const bTime = b.updatedAt instanceof Date ? b.updatedAt.getTime() : (b.updatedAt ? new Date(b.updatedAt).getTime() : 0)
        return bTime - aTime
      })
  }, [groups])
}

export const useGroupsByPriority = (priority: ChatGroupPriority) => {
  const groups = useChatGroupStore(state => state.groups)

  return useMemo(() => {
    return groups
      .filter(g => g.priority === priority)
      .slice() // 创建浅拷贝，避免修改原数组
      .sort((a, b) => {
        const aTime = a.updatedAt instanceof Date ? a.updatedAt.getTime() : (a.updatedAt ? new Date(a.updatedAt).getTime() : 0)
        const bTime = b.updatedAt instanceof Date ? b.updatedAt.getTime() : (b.updatedAt ? new Date(b.updatedAt).getTime() : 0)
        return bTime - aTime
      })
  }, [groups, priority])
}
