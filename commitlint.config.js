/**
 * Commitlint 配置
 * 强制使用 Conventional Commits 规范
 *
 * 格式: type(scope): description
 * 例如: feat(auth): add login validation
 *
 * 允许的类型:
 * - feat: 新功能
 * - fix: 修复bug
 * - docs: 文档变更
 * - style: 代码格式变更(不影响代码逻辑)
 * - refactor: 重构代码
 * - perf: 性能优化
 * - test: 测试相关
 * - chore: 构建过程或辅助工具变更
 * - ci: CI配置文件和脚本变更
 * - revert: 回滚提交
 */
module.exports = {
  extends: ['@commitlint/config-conventional'],
  rules: {
    // 类型必须是小写
    'type-case': [2, 'always', 'lower-case'],
    // 类型不能为空
    'type-empty': [2, 'never'],
    // 类型枚举
    'type-enum': [
      2,
      'always',
      [
        'feat', // 新功能
        'fix', // 修复bug
        'docs', // 文档变更
        'style', // 代码格式变更
        'refactor', // 重构
        'perf', // 性能优化
        'test', // 测试
        'chore', // 构建工具
        'ci', // CI配置
        'revert', // 回滚
        'build', // 构建相关
      ],
    ],
    // 描述不能为空
    'subject-empty': [2, 'never'],
    // 描述不能以句号结尾
    'subject-full-stop': [2, 'never', '.'],
    // 描述首字母小写
    'subject-case': [2, 'always', 'lower-case'],
    // header最大长度
    'header-max-length': [2, 'always', 100],
    // body每行最大长度
    'body-max-line-length': [1, 'always', 100],
    // footer每行最大长度
    'footer-max-line-length': [1, 'always', 100],
  },
}
