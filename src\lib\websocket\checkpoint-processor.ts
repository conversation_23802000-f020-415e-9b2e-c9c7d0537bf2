/**
 * CheckpointPayload处理器
 * 负责动态表单生成、验证、提交逻辑
 */

import type {
  CheckpointMessage,
  CheckpointPayload,
  FormField,
  FormFieldOption,
} from '@/types/websocket-event-type'

export interface FormFieldValue {
  fieldId: string
  value: any
  isValid: boolean
  errors: string[]
}

export interface FormState {
  id: string
  sessionId: string
  title?: string
  description?: string
  fields: FormField[]
  values: Record<string, FormFieldValue>
  isValid: boolean
  isSubmitting: boolean
  isSubmitted?: boolean // 添加isSubmitted字段支持
  errors: string[]
  createdAt: Date
  updatedAt: Date
}

export interface FormSubmission {
  formId: string
  sessionId: string
  values: Record<string, any>
  timestamp: Date
}

export interface FormEvent {
  type: 'created' | 'updated' | 'validated' | 'submitted' | 'error'
  form: FormState
  fieldId?: string
  value?: any
  error?: string
}

export interface FormEventListener {
  (event: FormEvent): void
}

export interface FormSubmissionListener {
  (submission: FormSubmission): void
}

export class CheckpointProcessor {
  private forms: Record<string, FormState> = {}
  private eventListeners: FormEventListener[] = []
  private submissionListeners: FormSubmissionListener[] = []

  // 处理检查点消息
  processCheckpointMessage(message: CheckpointMessage): void {
    try {
      const form = this.createFormFromPayload(message.sessionId, message.payload, message.timestamp)
      this.forms[form.id] = form

      this.notifyEvent({
        type: 'created',
        form: { ...form },
      })
    } catch (error) {
      console.error('Error processing checkpoint message:', error)
    }
  }

  // 从CheckpointPayload创建表单
  private createFormFromPayload(
    sessionId: string,
    payload: CheckpointPayload,
    timestamp: string
  ): FormState {
    const formId = this.generateFormId(sessionId, timestamp)
    const now = new Date()

    const values: Record<string, FormFieldValue> = {}

    // 初始化表单字段值
    payload.fields.forEach(field => {
      const fieldValue: FormFieldValue = {
        fieldId: field.id,
        value: field.defaultValue || this.getDefaultValueForType(field.type),
        isValid: !field.required || !!field.defaultValue,
        errors: [],
      }
      values[field.id] = fieldValue
    })

    return {
      id: formId,
      sessionId,
      fields: payload.fields,
      values,
      isValid: this.validateForm(payload.fields, values),
      isSubmitting: false,
      errors: [],
      createdAt: now,
      updatedAt: now,
    }
  }

  // 生成表单ID
  private generateFormId(sessionId: string, timestamp: string): string {
    return `form-${sessionId}-${new Date(timestamp).getTime()}`
  }

  // 根据字段类型获取默认值
  private getDefaultValueForType(type: FormField['type']): any {
    switch (type) {
      case 'text':
      case 'textarea':
        return ''
      case 'number':
        return 0
      case 'checkbox':
        return false
      case 'multiselect':
        return []
      case 'date':
        return null
      case 'select':
      case 'radio':
        return null
      case 'file':
        return null
      default:
        return null
    }
  }

  // 更新字段值
  updateFieldValue(formId: string, fieldId: string, value: any): void {
    const form = this.forms[formId]
    if (!form) {
      throw new Error(`Form not found: ${formId}`)
    }

    const field = form.fields.find(f => f.id === fieldId)
    if (!field) {
      throw new Error(`Field not found: ${fieldId}`)
    }

    // 验证字段值
    const validation = this.validateField(field, value)

    // 更新字段值
    const fieldValue: FormFieldValue = {
      fieldId,
      value,
      isValid: validation.isValid,
      errors: validation.errors,
    }

    form.values[fieldId] = fieldValue
    form.updatedAt = new Date()

    // 重新验证整个表单
    form.isValid = this.validateForm(form.fields, form.values)

    this.notifyEvent({
      type: 'updated',
      form: { ...form },
      fieldId,
      value,
    })

    this.notifyEvent({
      type: 'validated',
      form: { ...form },
      fieldId,
    })
  }

  // 验证字段
  private validateField(field: FormField, value: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = []

    // 必填验证
    if (field.required && this.isEmpty(value)) {
      errors.push(`${field.label}是必填项`)
    }

    // 类型特定验证
    if (!this.isEmpty(value)) {
      switch (field.type) {
        case 'text':
        case 'textarea':
          if (typeof value !== 'string') {
            errors.push(`${field.label}必须是文本`)
          }
          break
        case 'number':
          if (typeof value !== 'number' || isNaN(value)) {
            errors.push(`${field.label}必须是有效数字`)
          }
          break
        case 'checkbox':
          if (typeof value !== 'boolean') {
            errors.push(`${field.label}必须是是/否选择`)
          }
          break
        case 'select':
        case 'radio':
          if (!this.isValidOption(value, field.options)) {
            errors.push(`${field.label}选择的值无效`)
          }
          break
        case 'multiselect':
          if (!Array.isArray(value) || !this.areValidOptions(value, field.options)) {
            errors.push(`${field.label}选择的值无效`)
          }
          break
        case 'date':
          if (!(value instanceof Date) && !this.isValidDateString(value)) {
            errors.push(`${field.label}必须是有效日期`)
          }
          break
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    }
  }

  // 检查值是否为空
  private isEmpty(value: any): boolean {
    if (value === null || value === undefined) return true
    if (typeof value === 'string') return value.trim() === ''
    if (Array.isArray(value)) return value.length === 0
    return false
  }

  // 验证选项值
  private isValidOption(value: any, options?: FormFieldOption[]): boolean {
    if (!options) return true
    return options.some(option => option.value === value)
  }

  // 验证多选选项值
  private areValidOptions(values: any[], options?: FormFieldOption[]): boolean {
    if (!options) return true
    return values.every(value => this.isValidOption(value, options))
  }

  // 验证日期字符串
  private isValidDateString(value: any): boolean {
    if (typeof value !== 'string') return false
    const date = new Date(value)
    return !isNaN(date.getTime())
  }

  // 验证整个表单
  private validateForm(fields: FormField[], values: Record<string, FormFieldValue>): boolean {
    return fields.every(field => {
      const fieldValue = values[field.id]
      return fieldValue ? fieldValue.isValid : false
    })
  }

  // 提交表单
  async submitForm(formId: string): Promise<void> {
    const form = this.forms[formId]
    if (!form) {
      throw new Error(`Form not found: ${formId}`)
    }

    if (!form.isValid) {
      throw new Error('Form validation failed')
    }

    if (form.isSubmitting) {
      throw new Error('Form is already being submitted')
    }

    form.isSubmitting = true
    form.updatedAt = new Date()

    try {
      // 准备提交数据
      const submissionData: Record<string, any> = {}
      Object.entries(form.values).forEach(([fieldId, fieldValue]) => {
        submissionData[fieldId] = fieldValue.value
      })

      const submission: FormSubmission = {
        formId,
        sessionId: form.sessionId,
        values: submissionData,
        timestamp: new Date(),
      }

      // 通知提交
      this.notifySubmission(submission)

      this.notifyEvent({
        type: 'submitted',
        form: { ...form },
      })

      // 移除已提交的表单
      delete this.forms[formId]
    } catch (error) {
      form.isSubmitting = false
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      form.errors.push(errorMessage)

      this.notifyEvent({
        type: 'error',
        form: { ...form },
        error: errorMessage,
      })

      throw error
    }
  }

  // 获取表单状态
  getForm(formId: string): FormState | null {
    const form = this.forms[formId]
    return form ? { ...form } : null
  }

  // 获取会话的所有表单
  getSessionForms(sessionId: string): FormState[] {
    const forms: FormState[] = []
    Object.values(this.forms).forEach(form => {
      if (form.sessionId === sessionId) {
        forms.push({ ...form })
      }
    })
    return forms.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
  }

  // 获取字段值
  getFieldValue(formId: string, fieldId: string): FormFieldValue | null {
    const form = this.forms[formId]
    if (!form) return null

    const fieldValue = form.values?.[fieldId]
    return fieldValue ? { ...fieldValue } : null
  }

  // 获取表单数据
  getFormData(formId: string): Record<string, any> | null {
    const form = this.forms[formId]
    if (!form) return null

    const data: Record<string, any> = {}
    Object.entries(form.values || {}).forEach(([fieldId, fieldValue]) => {
      data[fieldId] = fieldValue.value
    })
    return data
  }

  // 清理表单
  clearForm(formId: string): void {
    delete this.forms[formId]
  }

  // 清理会话的所有表单
  clearSessionForms(sessionId: string): void {
    const formIds: string[] = []
    Object.entries(this.forms).forEach(([id, form]) => {
      if (form.sessionId === sessionId) {
        formIds.push(id)
      }
    })
    formIds.forEach(id => this.clearForm(id))
  }

  // 清理过期表单
  cleanupOldForms(maxAge: number = 30 * 60 * 1000): void {
    // 30分钟
    const now = new Date()
    const expiredForms: string[] = []

    Object.entries(this.forms).forEach(([id, form]) => {
      const age = now.getTime() - form.updatedAt.getTime()
      if (age > maxAge) {
        expiredForms.push(id)
      }
    })

    expiredForms.forEach(id => this.clearForm(id))
  }

  // 事件监听器管理
  addEventListener(listener: FormEventListener): void {
    this.eventListeners.push(listener)
  }

  removeEventListener(listener: FormEventListener): void {
    const index = this.eventListeners.indexOf(listener)
    if (index > -1) {
      this.eventListeners.splice(index, 1)
    }
  }

  addSubmissionListener(listener: FormSubmissionListener): void {
    this.submissionListeners.push(listener)
  }

  removeSubmissionListener(listener: FormSubmissionListener): void {
    const index = this.submissionListeners.indexOf(listener)
    if (index > -1) {
      this.submissionListeners.splice(index, 1)
    }
  }

  // 清理所有监听器
  clearAllListeners(): void {
    this.eventListeners.length = 0
    this.submissionListeners.length = 0
  }

  // 获取统计信息
  getStats(): {
    totalForms: number
    activeForms: number
    submittingForms: number
    validForms: number
    totalListeners: number
  } {
    let submittingForms = 0
    let validForms = 0

    Object.values(this.forms).forEach(form => {
      if (form.isSubmitting) submittingForms++
      if (form.isValid) validForms++
    })

    const totalForms = Object.keys(this.forms).length

    return {
      totalForms,
      activeForms: totalForms - submittingForms,
      submittingForms,
      validForms,
      totalListeners: this.eventListeners.length + this.submissionListeners.length,
    }
  }

  // 私有方法：通知事件
  private notifyEvent(event: FormEvent): void {
    this.eventListeners.forEach(listener => {
      try {
        listener(event)
      } catch (error) {
        console.error('Error in form event listener:', error)
      }
    })
  }

  // 私有方法：通知提交
  private notifySubmission(submission: FormSubmission): void {
    this.submissionListeners.forEach(listener => {
      try {
        listener(submission)
      } catch (error) {
        console.error('Error in form submission listener:', error)
      }
    })
  }
}
