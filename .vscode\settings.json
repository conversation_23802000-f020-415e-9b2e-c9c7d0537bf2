{"i18n-ally.localesPaths": ["src/lib/i18n", "src/lib/i18n/messages"], "oxc.enable": true, "typescript.preferences.includePackageJsonAutoImports": "auto", "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "eslint.workingDirectories": ["./src"], "emmet.includeLanguages": {"typescript": "html", "typescriptreact": "html"}, "debug.javascript.autoAttachFilter": "onlyWithFlag", "debug.node.autoAttach": "on", "files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/CVS": true, "**/.DS_Store": true, "**/Thumbs.db": true, "**/.next": true, "**/out": true, "**/build": true, "**/dist": true}, "search.exclude": {"**/.next": true, "**/node_modules": true, "**/out": true, "**/build": true, "**/dist": true}, "typescript.preferences.importModuleSpecifier": "relative"}