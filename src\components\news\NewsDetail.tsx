"use client"

import { useState } from "react"
// 简化导入
import { NewsItem } from "@/types/news"
import { But<PERSON> } from "@/components/ui/button"
import ProjectCreationDialog from "@/components/common/project-creation-dialog"

interface NewsDetailProps {
  articleId: string
  articles: NewsItem[]
}

export default function NewsDetail({ articleId, articles }: NewsDetailProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  
  // 找到匹配的文章
  const article = articles.find(a => a._id === articleId)
  
  if (!article) {
    return <div>Article not found</div>
  }

  // 处理按钮点击事件
  const handleButton1Click = () => {
    console.log('按钮1点击:', articleId)
    // 在这里添加按钮1的逻辑
  }

  const handleButton2Click = () => {
    console.log('启动分析按钮点击:', articleId)
    setIsDialogOpen(true)
  }

  // 渲染新闻详情（API原始格式）
  return (
    <div className="space-y-4">
         
      {/* 关键点 */}
      <div className="bg-gray-100 rounded-lg p-4">
        <h4 className="text-sm font-semibold text-gray-900 mb-3">关键点</h4>
        <div className="text-sm text-gray-700 leading-relaxed space-y-2">
          {article.key_points.map((point: string, index: number) => (
            <div key={index} className="flex items-start">
              <span className="text-orange-500 mr-2 mt-1 flex-shrink-0">•</span>
              <span>{point}</span>
            </div>
          ))}
        </div>
        
      </div>
      {/* 按钮区域 */}
      <div className="w-full flex justify-center">
        <div className="flex" style={{ gap: '1.5rem', marginTop: '10px' }}>
          <button
            className="bg-blue-300 hover:bg-blue-400 text-white px-6 py-2.5 rounded-full text-sm font-medium transition-colors shadow-sm min-w-0 whitespace-nowrap"
            onClick={handleButton1Click}
          >
            保持关注
          </button>
          <button
            className="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2.5 rounded-full text-sm font-medium transition-colors shadow-sm min-w-0 whitespace-nowrap"
            onClick={handleButton2Click}
          >
            很有意思，立刻启动分析
          </button>
        </div>
      </div>
      
      {/* Project Dialog Box */}
      <ProjectCreationDialog 
        isOpen={isDialogOpen} 
        onClose={() => setIsDialogOpen(false)} 
      />
    </div>
  )
}