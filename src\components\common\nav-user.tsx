'use client'

import { BadgeCheck, Bell, ChevronsUpDown, CreditCard, LogOut, Globe, Settings } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { signOut } from '@/lib/auth/auth-client'
import { useLanguage } from '@/hooks/use-language'

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from '@/components/ui/sidebar'

// 语言选项配置
const languageOptions = [
  { value: 'chinese', label: '简体中文', icon: '🇨🇳' },
  { value: 'english', label: 'English', icon: '🇺🇸' },
  { value: 'japanese', label: '日本語', icon: '🇯🇵' },
]

export function NavUser({
  user,
}: {
  user: {
    name: string
    email: string
    avatar: string
  }
}) {
  const { isMobile } = useSidebar()
  const router = useRouter()
  const { currentLanguage, isChanging, changeLanguage } = useLanguage()

  // 语言选项
  const languageOptions = [
    { value: 'chinese', label: '简体中文', icon: '🇨🇳' },
    { value: 'english', label: 'English', icon: '🇺🇸' },
    { value: 'japanese', label: '日本語', icon: '🇯🇵' },
  ]

  // 处理语言切换
  const handleLanguageChange = async (language: string) => {
    if (language === currentLanguage || isChanging) {
      return
    }
    try {
      await changeLanguage(language)
    } catch (err) {
      console.error('语言切换失败:', err)
    }
  }

  // 处理退出登录
  const handleSignOut = async () => {
    try {
      console.log('🚪 用户点击退出登录')
      await signOut()
      console.log('✅ 退出登录成功，重定向到首页')
      router.push('login') // 重定向到landing页面
    } catch (error) {
      console.error('❌ 退出登录失败:', error)
      // 即使失败也尝试跳转
      router.push('login')
    }
  }

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <Avatar className="h-8 w-8 rounded-lg">
                <AvatarImage src={user.avatar} alt={user.name} />
                <AvatarFallback className="rounded-lg">CN</AvatarFallback>
              </Avatar>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-medium">{user.name}</span>
                <span className="truncate text-xs">{user.email}</span>
              </div>
              <ChevronsUpDown className="ml-auto size-4" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg"
            side={isMobile ? 'bottom' : 'right'}
            align="end"
            sideOffset={4}
          >
            <DropdownMenuLabel className="p-0 font-normal">
              <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                <Avatar className="h-8 w-8 rounded-lg">
                  <AvatarImage src={user.avatar} alt={user.name} />
                  <AvatarFallback className="rounded-lg">CN</AvatarFallback>
                </Avatar>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-medium">{user.name}</span>
                  <span className="truncate text-xs">{user.email}</span>
                </div>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            
            {/* 语言切换子菜单 */}
            <DropdownMenuSub>
              <DropdownMenuSubTrigger>
                <Globe className="mr-2 h-4 w-4" />
                <span>语言设置</span>
              </DropdownMenuSubTrigger>
              <DropdownMenuSubContent>
                {languageOptions.map(lang => (
                  <DropdownMenuItem
                    key={lang.value}
                    onSelect={() => handleLanguageChange(lang.value)}
                    disabled={isChanging}
                    className={`flex cursor-pointer items-center gap-2 ${
                      currentLanguage === lang.value ? 'bg-accent text-accent-foreground' : ''
                    }`}
                  >
                    <span className="text-lg" role="img" aria-label={lang.label}>
                      {lang.icon}
                    </span>
                    <span className="text-sm">{lang.label}</span>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuSubContent>
            </DropdownMenuSub>
            
            <DropdownMenuSeparator />
            
            {/* 设置菜单项 */}
            <DropdownMenuItem onClick={() => router.push('/user-info')}>
              <Settings className="mr-2 h-4 w-4" />
              <span>设置</span>
            </DropdownMenuItem>
            
            <DropdownMenuSeparator />
            
            <DropdownMenuItem onClick={handleSignOut}>
              <LogOut />
              退出登录
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  )
}
