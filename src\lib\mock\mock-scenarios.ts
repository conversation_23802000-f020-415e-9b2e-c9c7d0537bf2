/**
 * Mock场景定义
 * 预定义的测试场景和数据集
 */

import { MockDataFactory } from './mock-data-factory'
import type { BaseWebSocketMessage } from '@/types/websocket-event-type'

// ============================================================================
// 场景类型定义
// ============================================================================

export interface MockScenario {
  id: string
  name: string
  description: string
  type: 'streaming' | 'checkpoint' | 'report' | 'error' | 'mixed'
  delayMs: number
  data?: any
  shouldFail?: boolean
}

// ============================================================================
// 预定义场景
// ============================================================================

export const MockScenarios: Record<string, MockScenario> = {
  // 流式响应场景
  STREAMING_FAST: {
    id: 'streaming-fast',
    name: '快速流式响应',
    description: '模拟快速流式文本输出',
    type: 'streaming',
    delayMs: 50,
    data: {
      text: '🌊 这是一个快速流式响应演示！\n\n我会快速地逐字符出现在您的屏幕上，展示实时文本输出的效果。\n\n✨ 特点：响应迅速、体验流畅！',
    },
  },

  STREAMING_SLOW: {
    id: 'streaming-slow',
    name: '缓慢流式响应',
    description: '模拟缓慢流式文本输出',
    type: 'streaming',
    delayMs: 200,
    data: {
      text: '🐌 这是一个缓慢流式响应演示...\n\n我会慢慢地出现在您的屏幕上，让您可以清楚地观察到每个字符的输出过程。\n\n🎯 适合演示和调试场景。',
    },
  },

  STREAMING_LONG: {
    id: 'streaming-long',
    name: '长文本流式响应',
    description: '模拟长文本流式输出',
    type: 'streaming',
    delayMs: 80,
    data: {
      text: `# AI智能分析系统

## 系统概述

欢迎使用我们的AI智能分析系统！这是一个强大的数据分析和洞察平台。

## 核心功能

### 1. 数据收集与处理
- 多源数据整合
- 实时数据流处理  
- 数据质量监控
- 异常检测与处理

### 2. 智能分析引擎
- 机器学习算法
- 深度学习模型
- 统计分析方法
- 预测分析能力

### 3. 可视化展示
- 交互式图表
- 实时仪表板
- 自定义报告
- 移动端适配

## 技术优势

我们的系统采用最新的AI技术，包括：
- 自然语言处理
- 计算机视觉
- 时间序列分析
- 异常检测算法

## 应用场景

广泛应用于：
- 商业智能分析
- 风险评估与管控  
- 用户行为分析
- 市场趋势预测

开始您的智能分析之旅吧！`,
    },
  },

  // 表单交互场景
  FORM_SIMPLE: {
    id: 'form-simple',
    name: '简单表单交互',
    description: '模拟简单的用户信息收集表单',
    type: 'checkpoint',
    delayMs: 500,
  },

  FORM_COMPLEX: {
    id: 'form-complex',
    name: '复杂表单交互',
    description: '模拟复杂的业务需求调研表单',
    type: 'checkpoint',
    delayMs: 800,
  },

  FORM_SURVEY: {
    id: 'form-survey',
    name: '用户调研表单',
    description: '模拟用户满意度调研表单',
    type: 'checkpoint',
    delayMs: 600,
    data: {
      fields: [
        {
          id: 'satisfaction',
          label: '整体满意度',
          type: 'radio',
          required: true,
          options: [
            { label: '非常满意', value: 5 },
            { label: '满意', value: 4 },
            { label: '一般', value: 3 },
            { label: '不满意', value: 2 },
            { label: '非常不满意', value: 1 },
          ],
        },
        {
          id: 'features',
          label: '最喜欢的功能（多选）',
          type: 'checkbox',
          required: false,
          options: [
            { label: '界面设计', value: 'ui' },
            { label: '响应速度', value: 'speed' },
            { label: '功能丰富', value: 'features' },
            { label: '易于使用', value: 'usability' },
          ],
        },
        {
          id: 'suggestions',
          label: '改进建议',
          type: 'textarea',
          required: false,
          defaultValue: '',
        },
      ],
    },
  },

  // 报告场景
  REPORT_SHORT: {
    id: 'report-short',
    name: '简短分析报告',
    description: '模拟简短的数据分析报告',
    type: 'report',
    delayMs: 1000,
  },

  REPORT_DETAILED: {
    id: 'report-detailed',
    name: '详细分析报告',
    description: '模拟详细的综合分析报告',
    type: 'report',
    delayMs: 2000,
  },

  // 错误场景
  ERROR_NETWORK: {
    id: 'error-network',
    name: '网络连接错误',
    description: '模拟网络连接失败的错误',
    type: 'error',
    delayMs: 100,
    shouldFail: true,
  },

  ERROR_VALIDATION: {
    id: 'error-validation',
    name: '数据验证错误',
    description: '模拟数据格式验证失败',
    type: 'error',
    delayMs: 200,
  },

  ERROR_SERVER: {
    id: 'error-server',
    name: '服务器处理错误',
    description: '模拟服务器内部处理异常',
    type: 'error',
    delayMs: 300,
  },

  // 混合场景
  MIXED_WORKFLOW: {
    id: 'mixed-workflow',
    name: '完整工作流程',
    description: '模拟完整的业务工作流：流式响应→表单交互→最终报告',
    type: 'mixed',
    delayMs: 300,
    data: {
      steps: [
        { type: 'streaming', delay: 0, scenario: 'STREAMING_FAST' },
        { type: 'checkpoint', delay: 3000, scenario: 'FORM_SIMPLE' },
        { type: 'report', delay: 8000, scenario: 'REPORT_DETAILED' },
      ],
    },
  },

  MIXED_ANALYSIS: {
    id: 'mixed-analysis',
    name: '数据分析流程',
    description: '模拟数据分析全流程：参数收集→处理进度→分析报告',
    type: 'mixed',
    delayMs: 400,
    data: {
      steps: [
        { type: 'checkpoint', delay: 0, scenario: 'FORM_COMPLEX' },
        { type: 'streaming', delay: 5000, scenario: 'STREAMING_LONG' },
        { type: 'report', delay: 15000, scenario: 'REPORT_DETAILED' },
      ],
    },
  },
} as const

// ============================================================================
// 场景工具函数
// ============================================================================

export class ScenarioManager {
  /**
   * 获取所有场景列表
   */
  static getAllScenarios(): MockScenario[] {
    return Object.values(MockScenarios)
  }

  /**
   * 根据类型获取场景
   */
  static getScenariosByType(type: MockScenario['type']): MockScenario[] {
    return Object.values(MockScenarios).filter(scenario => scenario.type === type)
  }

  /**
   * 根据ID获取场景
   */
  static getScenario(id: string): MockScenario | null {
    // 通过场景的id属性查找，而不是对象键
    return Object.values(MockScenarios).find(scenario => scenario.id === id) || null
  }

  /**
   * 生成场景数据
   */
  static generateScenarioData(scenarioId: string, config?: any): BaseWebSocketMessage[] {
    const scenario = this.getScenario(scenarioId)
    if (!scenario) {
      throw new Error(`Unknown scenario: ${scenarioId}`)
    }

    switch (scenario.type) {
      case 'streaming':
        return this.generateStreamingData(scenario, config)
      case 'checkpoint':
        return this.generateCheckpointData(scenario, config)
      case 'report':
        return this.generateReportData(scenario, config)
      case 'error':
        return this.generateErrorData(scenario, config)
      case 'mixed':
        return this.generateMixedData(scenario, config)
      default:
        throw new Error(`Unsupported scenario type: ${scenario.type}`)
    }
  }

  /**
   * 生成流式数据
   */
  private static generateStreamingData(
    scenario: MockScenario,
    config?: any
  ): BaseWebSocketMessage[] {
    const text = scenario.data?.text || '这是一个流式响应示例。'
    const chunkSize = scenario.delayMs < 100 ? 5 : 3 // 根据延迟调整块大小

    return MockDataFactory.createStreamingSequence(text, chunkSize, config)
  }

  /**
   * 生成表单数据
   */
  private static generateCheckpointData(
    scenario: MockScenario,
    config?: any
  ): BaseWebSocketMessage[] {
    if (scenario.data?.fields) {
      return [MockDataFactory.createCheckpointMessage(scenario.data.fields, config)]
    }

    // 根据场景ID选择预定义表单
    switch (scenario.id) {
      case 'form-simple':
        return [MockDataFactory.createSimpleForm(config)]
      case 'form-complex':
        return [MockDataFactory.createComplexForm(config)]
      default:
        return [MockDataFactory.createSimpleForm(config)]
    }
  }

  /**
   * 生成报告数据
   */
  private static generateReportData(scenario: MockScenario, config?: any): BaseWebSocketMessage[] {
    switch (scenario.id) {
      case 'report-short':
        return [MockDataFactory.createShortReport(config)]
      case 'report-detailed':
        return [MockDataFactory.createDetailedReport(config)]
      default:
        return [MockDataFactory.createShortReport(config)]
    }
  }

  /**
   * 生成错误数据
   */
  private static generateErrorData(scenario: MockScenario, config?: any): BaseWebSocketMessage[] {
    switch (scenario.id) {
      case 'error-network':
        return [MockDataFactory.createNetworkError(config)]
      case 'error-validation':
        return [MockDataFactory.createValidationError(config)]
      case 'error-server':
        return [MockDataFactory.createServerError(config)]
      default:
        return [MockDataFactory.createNetworkError(config)]
    }
  }

  /**
   * 生成混合场景数据
   */
  private static generateMixedData(scenario: MockScenario, config?: any): BaseWebSocketMessage[] {
    // 混合场景需要在运行时动态生成，这里返回第一步的数据
    const steps = scenario.data?.steps || []
    if (steps.length === 0) return []

    const firstStep = steps[0]
    const firstScenario = this.getScenario(firstStep.scenario)
    if (!firstScenario) return []

    return this.generateScenarioData(firstStep.scenario, config)
  }

  /**
   * 获取随机场景
   */
  static getRandomScenario(type?: MockScenario['type']): MockScenario {
    const scenarios = type ? this.getScenariosByType(type) : this.getAllScenarios()
    const randomIndex = Math.floor(Math.random() * scenarios.length)
    return scenarios[randomIndex]
  }
}
