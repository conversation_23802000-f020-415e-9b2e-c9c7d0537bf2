/**
 * 问卷调查相关类型定义
 */

// 问卷步骤枚举
export enum PollStep {
  COMPANY_TYPE = 1,
  REGION = 2,
  BUSINESS_MODE = 3,
  SPECIFIC_NEEDS = 4,
}

// 单个问答答案
export interface PollAnswer {
  step: PollStep
  questionKey: string
  selectedOption: string
  selectedLabel: string
  timestamp: number
}

// 问卷数据结构
export interface PollData {
  currentStep: PollStep
  answers: PollAnswer[]
  isCompleted: boolean
  completedAt?: number
  startedAt: number
}

// 问卷选项
export interface PollOption {
  value: string
  labelKey: string
}

// 问卷问题配置
export interface PollQuestion {
  step: PollStep
  questionKey: string
  options: PollOption[]
}

// API提交数据格式
export interface PollSubmitData {
  answers: {
    companyType: string
    region: string
    businessMode: string
    specificNeeds: string
  }
  metadata: {
    startedAt: number
    completedAt: number
    totalTimeSpent: number
  }
}

// API响应格式
export interface PollSubmitResponse {
  success: boolean
  message: string
  data?: {
    profileId: string
    nextSteps?: string[]
  }
}
