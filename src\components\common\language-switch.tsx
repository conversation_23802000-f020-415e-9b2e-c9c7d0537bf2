'use client'

import { useLanguage } from '@/hooks/use-language'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { ChevronDown, Loader2 } from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

interface LanguageSwitchProps {
  /**
   * 自定义类名
   */
  className?: string
}

const languageOptions = [
  { value: 'chinese', label: '简体中文', icon: '🇨🇳' },
  { value: 'english', label: 'English', icon: '🇺🇸' },
  { value: 'japanese', label: '日本語', icon: '🇯🇵' },
]

/**
 * 语言切换组件
 * 仿照提供的Vue组件，使用DropdownMenu实现
 */
export function LanguageSwitch({ className }: LanguageSwitchProps) {
  const { currentLanguage, isChanging, changeLanguage } = useLanguage()

  const handleLanguageChange = async (language: string) => {
    if (language === currentLanguage || isChanging) {
      return
    }
    try {
      await changeLanguage(language)
    } catch (err) {
      // 错误已经在 hook 中处理
      console.error('语言切换失败:', err)
    }
  }

  const currentLangDetails = languageOptions.find(lang => lang.value === currentLanguage)

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className={cn('flex items-center justify-start gap-2 px-2 cursor-pointer', className)}
        >
          {isChanging ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            currentLangDetails && (
              <>
                <span className="text-lg" role="img" aria-label={currentLangDetails.label}>
                  {currentLangDetails.icon}
                </span>
                <span className="text-sm font-medium">{currentLangDetails.label}</span>
                <ChevronDown className="h-4 w-4 text-muted-foreground" />
              </>
            )
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-40">
        {languageOptions.map(lang => (
          <DropdownMenuItem
            key={lang.value}
            onSelect={() => handleLanguageChange(lang.value)}
            disabled={isChanging}
            className={cn('flex cursor-pointer items-center gap-2', {
              'bg-accent text-accent-foreground': currentLanguage === lang.value,
            })}
          >
            <span className="text-lg" role="img" aria-label={lang.label}>
              {lang.icon}
            </span>
            <span className="text-sm">{lang.label}</span>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
