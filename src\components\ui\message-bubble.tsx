'use client'

import { memo, useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { cn } from '@/lib/utils'

// Shadcn UI 组件
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { User, Bot, Copy, Check, MessageSquare, MoreHorizontal } from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

export type MessageRole = 'user' | 'assistant' | 'system'
export type MessageStatus = 'sending' | 'sent' | 'delivered' | 'failed'

export interface MessageBubbleProps {
  /** 消息ID */
  id: string
  /** 消息角色 */
  role: MessageRole
  /** 消息内容 */
  content: string
  /** 消息状态 */
  status?: MessageStatus | undefined
  /** 时间戳 */
  timestamp?: Date | undefined
  /** 是否显示头像 */
  showAvatar?: boolean
  /** 是否显示时间戳 */
  showTimestamp?: boolean
  /** 用户头像URL */
  userAvatarUrl?: string | undefined
  /** 助手头像URL */
  assistantAvatarUrl?: string | undefined
  /** 是否启用复制功能 */
  enableCopy?: boolean
  /** 是否启用菜单 */
  enableMenu?: boolean
  /** 自定义样式 */
  className?: string
  /** 复制回调 */
  onCopy?: ((_content: string) => void) | undefined
  /** 重试回调 */
  onRetry?: ((_messageId: string) => void) | undefined
  /** 删除回调 */
  onDelete?: ((_messageId: string) => void) | undefined
  /** 点击回调 */
  onClick?: ((_messageId: string) => void) | undefined
}

export const MessageBubble = memo(
  ({
    id,
    role,
    content,
    status = 'delivered',
    timestamp,
    showAvatar = true,
    showTimestamp = false,
    userAvatarUrl,
    assistantAvatarUrl,
    enableCopy = true,
    enableMenu = true,
    className,
    onCopy,
    onRetry,
    onDelete,
    onClick,
  }: MessageBubbleProps) => {
    const [copied, setCopied] = useState(false)
    const [isHovered, setIsHovered] = useState(false)

    const isUser = role === 'user'
    const isAssistant = role === 'assistant'
    const isSystem = role === 'system'

    // 复制文本到剪贴板
    const handleCopy = async () => {
      try {
        await navigator.clipboard.writeText(content)
        setCopied(true)
        onCopy?.(content)
        setTimeout(() => setCopied(false), 2000)
      } catch (error) {
        // 复制失败处理
        // Failed to copy text, ignore the error
      }
    }

    // 格式化时间
    const formatTime = (date: Date) => {
      return date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
      })
    }

    // 获取头像信息
    const getAvatarInfo = () => {
      if (isUser) {
        return {
          src: userAvatarUrl,
          fallback: 'U',
          icon: User,
          className: 'bg-primary text-primary-foreground',
        }
      }
      if (isAssistant) {
        return {
          src: assistantAvatarUrl,
          fallback: 'AI',
          icon: Bot,
          className: 'bg-secondary text-secondary-foreground',
        }
      }
      return {
        src: undefined,
        fallback: 'S',
        icon: MessageSquare,
        className: 'bg-muted text-muted-foreground',
      }
    }

    const avatarInfo = getAvatarInfo()

    // 系统消息样式
    if (isSystem) {
      return (
        <motion.div
          className={cn('flex justify-center py-3', className)}
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.2 }}
        >
          <Badge variant="outline" className="text-xs text-muted-foreground px-3 py-1">
            <MessageSquare className="w-3 h-3 mr-2" />
            {content}
          </Badge>
        </motion.div>
      )
    }

    return (
      <motion.div
        className={cn(
          'group flex gap-3 py-3 px-4 transition-colors duration-200',
          'hover:bg-muted/30 rounded-lg',
          isUser && 'flex-row-reverse',
          className
        )}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, ease: 'easeOut' }}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onClick={() => onClick?.(id)}
      >
        {/* 头像 */}
        {showAvatar && (
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: 0.1, duration: 0.2 }}
          >
            <Avatar className={cn('flex-shrink-0 w-8 h-8', isUser && 'bg-figma-user-avatar')}>
              {avatarInfo.src && <AvatarImage src={avatarInfo.src} />}
              <AvatarFallback
                className={cn(
                  'text-xs font-medium',
                  isUser ? 'bg-figma-user-avatar text-figma-text' : avatarInfo.className
                )}
              >
                {avatarInfo.fallback}
              </AvatarFallback>
            </Avatar>
          </motion.div>
        )}

        {/* 消息内容区 */}
        <div className={cn('flex flex-col max-w-[75%] min-w-0', isUser && 'items-end')}>
          {/* 发送者名称和时间戳 */}
          <div
            className={cn('flex items-center gap-2 mb-1', isUser ? 'flex-row-reverse' : 'flex-row')}
          >
            {/* 发送者名称 */}
            <span className="text-base font-medium text-figma-text leading-6">
              {isUser ? 'You' : isAssistant ? 'SystemBot' : 'System'}
            </span>

            {/* 时间戳 */}
            {showTimestamp && timestamp && (
              <span className="text-xs font-normal text-figma-timestamp leading-6">
                {timestamp.toLocaleTimeString([], {
                  hour: '2-digit',
                  minute: '2-digit',
                  hour12: true,
                })}
              </span>
            )}
          </div>

          {/* 消息气泡 */}
          <div className="relative">
            <motion.div
              className={cn(
                'relative px-[22px] py-[14px] rounded-[26px] font-normal text-base leading-6',
                'whitespace-pre-wrap break-words transition-all duration-200',
                isUser
                  ? 'bg-figma-user-bg text-figma-text max-w-[606px]'
                  : 'bg-figma-assistant-bg text-figma-text max-w-[714px]',
                status === 'failed' &&
                  'border-destructive bg-destructive/5 text-destructive-foreground'
              )}
              whileHover={{ y: -1 }}
              transition={{ duration: 0.15 }}
            >
              {content}
            </motion.div>

            {/* 操作按钮 */}
            <AnimatePresence>
              {(isHovered || copied) && (enableCopy || enableMenu) && (
                <motion.div
                  className={cn(
                    'absolute -top-1 flex gap-1 z-10',
                    isUser ? 'left-0 -translate-x-full pr-2' : 'right-0 translate-x-full pl-2'
                  )}
                  initial={{ opacity: 0, scale: 0.8, y: 5 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  exit={{ opacity: 0, scale: 0.8, y: 5 }}
                  transition={{ duration: 0.15, ease: 'easeOut' }}
                >
                  {enableCopy && (
                    <Button
                      size="sm"
                      variant="ghost"
                      className={cn(
                        'h-7 w-7 p-0 bg-background/95 backdrop-blur-sm shadow-md border',
                        'hover:bg-accent hover:scale-110 active:scale-95',
                        'transition-all duration-150'
                      )}
                      onClick={e => {
                        e.stopPropagation()
                        handleCopy()
                      }}
                      title="复制消息"
                    >
                      <AnimatePresence mode="wait">
                        {copied ? (
                          <motion.div
                            key="check"
                            initial={{ scale: 0, rotate: -90 }}
                            animate={{ scale: 1, rotate: 0 }}
                            exit={{ scale: 0, rotate: 90 }}
                            transition={{ duration: 0.15 }}
                          >
                            <Check className="w-3 h-3 text-green-500" />
                          </motion.div>
                        ) : (
                          <motion.div
                            key="copy"
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            exit={{ scale: 0 }}
                            transition={{ duration: 0.15 }}
                          >
                            <Copy className="w-3 h-3" />
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </Button>
                  )}

                  {enableMenu && (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          size="sm"
                          variant="ghost"
                          className={cn(
                            'h-7 w-7 p-0 bg-background/95 backdrop-blur-sm shadow-md border',
                            'hover:bg-accent hover:scale-110 active:scale-95',
                            'transition-all duration-150'
                          )}
                          onClick={e => e.stopPropagation()}
                        >
                          <MoreHorizontal className="w-3 h-3" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent
                        side={isUser ? 'left' : 'right'}
                        align="start"
                        className="animate-in fade-in-0 zoom-in-95"
                      >
                        {status === 'failed' && onRetry && (
                          <DropdownMenuItem onClick={() => onRetry(id)}>重新发送</DropdownMenuItem>
                        )}
                        {onDelete && (
                          <DropdownMenuItem
                            onClick={() => onDelete(id)}
                            className="text-destructive focus:text-destructive"
                          >
                            删除消息
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* 底部信息 */}
          {(showTimestamp || status === 'failed' || status === 'sending') && (
            <motion.div
              className={cn(
                'mt-2 flex items-center gap-2 text-xs text-muted-foreground',
                isUser && 'flex-row-reverse'
              )}
              initial={{ opacity: 0, y: 5 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.2 }}
            >
              {showTimestamp && timestamp && (
                <span className="font-medium">{formatTime(timestamp)}</span>
              )}
              {status === 'sending' && (
                <Badge variant="secondary" className="text-xs animate-pulse">
                  发送中...
                </Badge>
              )}
              {status === 'failed' && (
                <Badge variant="destructive" className="text-xs">
                  发送失败
                </Badge>
              )}
            </motion.div>
          )}
        </div>
      </motion.div>
    )
  }
)

MessageBubble.displayName = 'MessageBubble'

// 简化的消息气泡列表组件
export interface MessageBubbleListProps {
  /** 消息列表 */
  messages: Array<{
    id: string
    role: MessageRole
    content: string
    status?: MessageStatus | undefined
    timestamp?: Date | undefined
  }>
  /** 是否显示头像 */
  showAvatar?: boolean
  /** 是否显示时间戳 */
  showTimestamp?: boolean
  /** 用户头像URL */
  userAvatarUrl?: string | undefined
  /** 助手头像URL */
  assistantAvatarUrl?: string | undefined
  /** 是否启用复制 */
  enableCopy?: boolean
  /** 是否启用菜单 */
  enableMenu?: boolean
  /** 自定义样式 */
  className?: string
  /** 事件回调 */
  onCopy?: ((_content: string) => void) | undefined
  onRetry?: ((_messageId: string) => void) | undefined
  onDelete?: ((_messageId: string) => void) | undefined
  onMessageClick?: ((_messageId: string) => void) | undefined
}

export const MessageBubbleList = memo(
  ({
    messages,
    showAvatar = true,
    showTimestamp = false,
    userAvatarUrl,
    assistantAvatarUrl,
    enableCopy = true,
    enableMenu = true,
    className,
    onCopy,
    onRetry,
    onDelete,
    onMessageClick,
  }: MessageBubbleListProps) => {
    return (
      <div className={cn('space-y-2', className)}>
        <AnimatePresence initial={false}>
          {messages.map((message, index) => (
            <motion.div
              key={message.id}
              initial={{ opacity: 0, y: 20, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -20, scale: 0.95 }}
              transition={{
                duration: 0.3,
                delay: index * 0.05,
                ease: 'easeOut',
              }}
              layout
            >
              <MessageBubble
                id={message.id}
                role={message.role}
                content={message.content}
                status={message.status}
                timestamp={message.timestamp}
                showAvatar={showAvatar}
                showTimestamp={showTimestamp}
                userAvatarUrl={userAvatarUrl}
                assistantAvatarUrl={assistantAvatarUrl}
                enableCopy={enableCopy}
                enableMenu={enableMenu}
                onCopy={onCopy}
                onRetry={onRetry}
                onDelete={onDelete}
                onClick={onMessageClick}
              />
            </motion.div>
          ))}
        </AnimatePresence>
      </div>
    )
  }
)

MessageBubbleList.displayName = 'MessageBubbleList'
