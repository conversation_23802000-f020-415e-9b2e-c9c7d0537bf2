'use client'

import { useEffect, useState, useCallback, useMemo } from 'react'
import { notFound } from 'next/navigation'
import { useTranslations } from 'next-intl'
import { ChatContainer } from '@/components/chat/chat-container'
import { useSimplifiedChat } from '@/hooks/use-simplified-chat'
import { ChatErrorBoundary } from '@/components/error/chat-error-boundary'
import { ConnectionStatus } from '@/lib/connection/types'
import { useSession } from '@/components/providers/session-provider'

import Loading from '@/app/loading'

interface ChatPageProps {
  params: Promise<{ groupId: string }>
}

// 🔧 页面状态类型 - 简化为只处理参数解析
type PageState = { status: 'loading' } | { status: 'ready'; groupId: string }

/**
 * 动态群聊页面 - 基于新架构的智能聊天系统
 */
export default function ChatPage({ params }: ChatPageProps) {
  const [pageState, setPageState] = useState<PageState>({ status: 'loading' })

  // 🌐 国际化
  const t = useTranslations('chat')

  // 🎯 获取Better Auth session信息
  const { user, organization, isAuthenticated } = useSession()

  // 🎯 使用简化的聊天Hook
  const {
    // 连接状态
    isConnected,
    connectionStatus,
    connectionError,

    // 会话状态
    hasActiveSession,
    currentSession,

    // 消息状态
    messages,
    messageCount,

    // 操作方法
    switchToGroup,
    connect,
    sendMessage,
  } = useSimplifiedChat()

  // 计算衍生状态
  const hasMessages = messageCount > 0

  // 🔧 参数解析 - 简化的初始化逻辑
  useEffect(() => {
    let isCancelled = false

    const parseParams = async () => {
      try {
        const { groupId } = await params

        if (isCancelled) return

        setPageState({ status: 'ready', groupId })
      } catch (error) {
        console.error('❌ 参数解析失败:', error)
        // 参数解析失败，触发 404
        notFound()
      }
    }

    parseParams()

    return () => {
      isCancelled = true
    }
  }, [params])

  // 🔧 会话初始化 - 当页面准备就绪时自动切换到指定群聊
  useEffect(() => {
    if (pageState.status === 'ready' && !hasActiveSession && isAuthenticated && user) {
      console.log('🔗 初始化群聊会话:', pageState.groupId)

      // 🎯 优化：使用Better Auth session的真实用户信息
      const userId = user.id || 'anonymous-user'
      const organizationId = organization?.id || 'default-org'

      // 🔍 详细调试信息
      console.log('👤 Better Auth用户信息详情:', {
        userId,
        organizationId,
        userObject: user,
        organizationObject: organization,
        isUserIdValid: !!user.id,
        userIdType: typeof user.id,
        userIdEquals_MOCK_AI_ASSISTANT_ID: userId === 'mock-assistant-bot',
      })

      try {
        switchToGroup(
          pageState.groupId,
          userId, // 使用真实用户ID
          organizationId // 使用真实组织ID
        )
      } catch (error) {
        console.error('❌ 会话初始化失败:', error)
      }
    }
  }, [pageState, hasActiveSession, isAuthenticated, user, organization, switchToGroup])

  // 🔧 自动连接 - 在会话建立后自动建立WebSocket连接
  useEffect(() => {
    if (hasActiveSession && !isConnected && connectionStatus === ConnectionStatus.DISCONNECTED) {
      console.log('🔗 会话建立完成，自动建立WebSocket连接...')

      // 安全检查：确保connect方法存在且返回Promise
      if (typeof connect === 'function') {
        const result = connect()
        if (result && typeof result.catch === 'function') {
          result.catch(error => {
            console.error('❌ 自动连接失败:', error)
          })
        } else {
          console.warn('⚠️ Connect method did not return a Promise')
        }
      } else {
        console.error('❌ Connect method is not available')
      }
    }
  }, [hasActiveSession, isConnected, connectionStatus, connect])

  // 🔧 缓存的重连处理
  const handleReconnect = useCallback(() => {
    console.log('🔗 用户触发重连...')

    // 安全检查：确保connect方法存在且返回Promise
    if (typeof connect === 'function') {
      const result = connect()
      if (result && typeof result.catch === 'function') {
        result.catch(error => {
          console.error('重连失败:', error)
        })
      } else {
        console.warn('Connect method did not return a Promise')
      }
    } else {
      console.error('Connect method is not available')
    }
  }, [connect])

  // 🔧 性能优化：缓存占位符文本
  const placeholderText = useMemo(() => {
    if (hasActiveSession && currentSession) {
      return t('interface.placeholder.group', { groupId: currentSession.groupChatId })
    }
    return t('interface.placeholder.default')
  }, [hasActiveSession, currentSession, t])

  // 🔧 渲染逻辑 - 基于新架构状态
  if (pageState.status === 'loading') {
    return (
      <div className="flex items-center justify-center h-full">
        <Loading />
      </div>
    )
  }

  // 如果连接状态为连接中
  if (connectionStatus === ConnectionStatus.CONNECTING) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <Loading />
          <p className="mt-2 text-sm text-gray-600">{t('status.connecting')}</p>
        </div>
      </div>
    )
  }

  // 如果连接错误
  if (connectionStatus === ConnectionStatus.ERROR && connectionError) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center p-6 max-w-md">
          <div className="text-red-500 mb-4">
            <svg
              className="mx-auto h-12 w-12"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.96-.833-2.73 0L3.084 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">{t('error.title')}</h3>
          <p className="text-sm text-gray-600 mb-4">{connectionError}</p>
          <button
            onClick={handleReconnect}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
          >
            {t('error.reconnect')}
          </button>
        </div>
      </div>
    )
  }

  // 如果还没有活跃会话，继续显示加载
  if (!hasActiveSession) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <Loading />
          <p className="mt-2 text-sm text-gray-600">{t('status.validating')}</p>
        </div>
      </div>
    )
  }

  // 🎯 会话就绪：渲染新架构聊天界面（包装在错误边界中）
  return (
    <ChatErrorBoundary
      onError={errorInfo => {
        console.error('🔥 聊天系统错误:', errorInfo)
        // 可以在这里集成错误监控服务
      }}
      maxRetries={3}
    >
      <div className="flex flex-col h-full">
        <ChatContainer
          // 外观配置
          appearance={{
            showAvatar: true,
            showTimestamp: false,
            renderMode: 'card' as const,
          }}
          // 功能配置
          features={{
            enableFileUpload: false,
            enableVoiceInput: false,
            enableThinkMode: false,
            enableDeepSearch: false,
          }}
          // UI配置
          ui={{
            placeholder: placeholderText || '',
            maxInputRows: 4,
            className: 'flex-1',
          }}
          // 回调配置
          callbacks={{
            onMessageSent: (content: string) => {
              console.log('📤 Message sent:', content.substring(0, 50) + '...')
            },
          }}
        />
      </div>
    </ChatErrorBoundary>
  )
}
