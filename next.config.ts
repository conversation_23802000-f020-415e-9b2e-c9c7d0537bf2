import { withSentryConfig } from '@sentry/nextjs'
import type { NextConfig } from 'next'
import createNextIntlPlugin from 'next-intl/plugin'

// 创建next-intl插件
const withNextIntl = createNextIntlPlugin('./src/lib/i18n/request.ts')

/** @type {import('next').NextConfig} */
const nextConfig: NextConfig = {
  // Enable standalone output for Docker deployment
  output: 'standalone',

  // Compress responses
  compress: true,

  // Base path for deployment
  basePath: process.env.NEXT_PUBLIC_BASE_PATH || '',

  // Environment variables
  env: {
    NEXT_PUBLIC_APP_ENV: process.env.NEXT_PUBLIC_APP_ENV || 'production',
    NEXT_PUBLIC_API_BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL,
    NEXT_PUBLIC_EMBED_ENABLED: process.env.NEXT_PUBLIC_EMBED_ENABLED || 'true',
    NEXT_PUBLIC_EXCLUDE_UI_GALLERY: process.env.NEXT_PUBLIC_EXCLUDE_UI_GALLERY || 'false',
    CUSTOM_KEY: process.env.CUSTOM_KEY,
    NEXT_AUTH_BACKEND_ORIGIN: process.env.NEXT_AUTH_BACKEND_ORIGIN,
  },

  // Webpack configuration - simplified
  webpack: config => {
    return config
  },

  // Headers for CORS and security
  async headers() {
    const baseHeaders = [
      {
        source: '/_next/static/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
          {
            key: 'Access-Control-Allow-Origin',
            value: '*',
          },
        ],
      },
      {
        source: '/api/:path*',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            value: '*',
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET, POST, PUT, DELETE, OPTIONS',
          },
          {
            key: 'Access-Control-Allow-Headers',
            value:
              'Authorization, language, timezone, timezonestr, X-Requested-With, Content-Type, Cookie, User-Agent',
          },
        ],
      },
    ]

    return baseHeaders
  },

  // Image optimization
  images: {
    unoptimized: true,
    domains: ['localhost', 'dev.goglobalsp.com'],
    formats: ['image/webp', 'image/avif'],
  },

  async rewrites() {
    const backendOrigin = process.env.NEXT_AUTH_BACKEND_ORIGIN
    const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL

    const rewrites = []

    // Auth API 代理（优先级更高）
    if (backendOrigin) {
      rewrites.push({
        source: '/api/auth/:path*',
        destination: `${backendOrigin.replace(/\/$/, '')}/api/auth/:path*`,
      })
    }

    if (apiBaseUrl) {
      rewrites.push({
        source: '/api/:path*',
        destination: `${apiBaseUrl.replace(/\/$/, '')}/api/v1/:path*`,
      })
    }

    return rewrites
  },
}

// 使用next-intl插件包装配置
export default withSentryConfig(withNextIntl(nextConfig), {
  // For all available options, see:
  // https://www.npmjs.com/package/@sentry/webpack-plugin#options

  org: 'specificai',
  project: 'javascript-nextjs',

  // Only print logs for uploading source maps in CI
  silent: !process.env.CI,

  // For all available options, see:
  // https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/

  // Upload a larger set of source maps for prettier stack traces (increases build time)
  widenClientFileUpload: true,

  // Route browser requests to Sentry through a Next.js rewrite to circumvent ad-blockers.
  // This can increase your server load as well as your hosting bill.
  // Note: Check that the configured route will not match with your Next.js middleware, otherwise reporting of client-
  // side errors will fail.
  tunnelRoute: '/monitoring',

  // Automatically tree-shake Sentry logger statements to reduce bundle size.
  // Set to `true` to disable Sentry's logger and save bundle size.
  disableLogger: false,

  // Enables automatic instrumentation of Vercel Cron Monitors. (Does not yet work with App Router route handlers.)
  // See the following for more information:
  // https://docs.sentry.io/product/crons/
  // https://vercel.com/docs/cron-jobs
  automaticVercelMonitors: true,
})
