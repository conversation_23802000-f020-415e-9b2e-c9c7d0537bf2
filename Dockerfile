# Stage 1: 依赖安装和构建 - 使用更高效的 pnpm
FROM node:20-alpine AS deps
WORKDIR /app

# 安装 pnpm
RUN corepack enable && corepack prepare pnpm@latest --activate

# 只复制包管理器相关文件以优化缓存
COPY package.json pnpm-lock.yaml ./

# 安装所有依赖（包括 devDependencies，构建需要）
RUN pnpm install --frozen-lockfile --ignore-scripts
# 显式安装缺失的rollup平台依赖
RUN pnpm add @rollup/rollup-linux-x64-gnu --ignore-scripts

# Stage 2: 构建阶段
FROM node:20-alpine AS builder
WORKDIR /app

# 安装 pnpm 和必要的系统依赖
RUN corepack enable && corepack prepare pnpm@latest --activate && \
    apk add --no-cache \
    libc6-compat \
    python3 \
    make \
    g++

# 复制依赖
COPY --from=deps /app/node_modules ./node_modules
COPY package.json pnpm-lock.yaml ./
# 确保构建阶段也有rollup依赖
RUN pnpm add @rollup/rollup-linux-x64-gnu --ignore-scripts

# 复制源代码
COPY . .

# 构建时环境变量 - 提供合理的默认值确保构建成功
ARG NEXT_PUBLIC_API_BASE_URL=http://placeholder-api-url
ARG NEXT_PUBLIC_WS_URL=wss://placeholder-ws-url
ENV NEXT_PUBLIC_API_BASE_URL=$NEXT_PUBLIC_API_BASE_URL
ENV NEXT_PUBLIC_WS_URL=$NEXT_PUBLIC_WS_URL  
# 为构建时rewrites提供占位符，运行时会被k8s配置覆盖
ENV NEXT_AUTH_BACKEND_ORIGIN=http://placeholder-auth-url
ENV NEXT_TELEMETRY_DISABLED=1
ENV NODE_ENV=production

# 构建应用 - Next.js standalone 会自动优化依赖
RUN pnpm run build

# 验证构建输出 - 确保关键文件存在
RUN echo "=== 验证构建输出 ===" && \
    ls -la .next/ && \
    echo "=== standalone目录 ===" && \
    ls -la .next/standalone/ && \
    echo "=== 检查server.js ===" && \
    test -f .next/standalone/server.js && echo "server.js exists" || echo "server.js missing"

# Stage 3: 生产运行时 - 使用Node.js Alpine镜像以确保兼容性
FROM node:20-alpine AS runner
WORKDIR /app

# 运行时环境变量
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV PORT=8230

# 创建运行用户以提高安全性
RUN addgroup --system --gid 1001 nodejs && \
    adduser --system --uid 1001 nextjs

# Next.js standalone 模式 - 所有必要文件都在 standalone 输出中
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static
COPY --from=builder --chown=nextjs:nodejs /app/public ./public

# 切换到非root用户
USER nextjs

EXPOSE 8230

# 启动 Next.js standalone 应用
CMD ["node", "server.js"]
