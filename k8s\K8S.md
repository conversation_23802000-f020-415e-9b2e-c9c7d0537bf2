# Kubernetes 部署指导文档

## 概述

本文档基于 `specific-ai-auth` 项目的部署实践，提供了一套标准化的K8s部署流程。适用于需要从私有镜像仓库部署到K8s集群的项目。

## 架构说明

### 部署架构分离

* **开发机器**: 包含源代码，运行 `build-and-push.sh` 构建并推送镜像到私有仓库
* **测试联调机器**: 只包含 `k8s/` 目录，从私有仓库拉取镜像并部署到K8s集群

### 核心组件

* **私有镜像仓库**: `**************/specific-ai`
* **业务命名空间**: `ovs`
* **部署脚本**: 自动化的一键部署脚本
* **连通性测试**: 三层验证确保部署成功

## 项目结构（环境隔离架构）

```
your-project/
├── k8s/
│   ├── deploy-k8s.sh          # 一键部署脚本
│   ├── deployment.yaml        # 通用Pod部署配置
│   ├── service.yaml           # 通用服务配置（ClusterIP + NodePort）
│   ├── dev/
│   │   ├── configmap.yaml     # 开发环境非敏感配置
│   │   └── secret.yaml        # 开发环境敏感配置
│   └── prod/
│       ├── configmap.yaml     # 生产环境非敏感配置
│       └── secret.yaml        # 生产环境敏感配置
└── build-and-push.sh         # 统一镜像构建推送脚本（开发机器）
```

## 标准配置模板

### 1. k8s/deployment.yaml

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: your-app-name
  namespace: ovs
  labels:
    app: your-app-name
    app.kubernetes.io/name: your-app-name
    app.kubernetes.io/component: service
    app.kubernetes.io/version: "1.0.0"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: your-app-name
  template:
    metadata:
      labels:
        app: your-app-name
        app.kubernetes.io/name: your-app-name
        app.kubernetes.io/component: service
    spec:
      containers:
      - name: your-app-name
        image: **************/specific-ai/your-app-name:latest
        imagePullPolicy: Always
        ports:
        - containerPort: YOUR_PORT
          name: http
          protocol: TCP
        env:
        # 从ConfigMap加载非敏感配置
        - name: PORT
          valueFrom:
            configMapKeyRef:
              name: your-app-config
              key: PORT
        # 从Secret加载敏感配置
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: your-app-secret
              key: DATABASE_URL
        # 健康检查
        livenessProbe:
          httpGet:
            path: /api/health
            port: YOUR_PORT
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/health
            port: YOUR_PORT
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        # 资源限制
        resources:
          limits:
            cpu: 500m
            memory: 512Mi
          requests:
            cpu: 250m
            memory: 256Mi
      restartPolicy: Always
      dnsPolicy: ClusterFirst
```

### 2. k8s/service.yaml

```yaml
apiVersion: v1
kind: Service
metadata:
  name: your-app-service
  namespace: ovs
  labels:
    app: your-app-name
    app.kubernetes.io/name: your-app-name
    app.kubernetes.io/component: service
spec:
  type: ClusterIP
  ports:
  - port: YOUR_PORT
    targetPort: YOUR_PORT
    protocol: TCP
    name: http
  selector:
    app: your-app-name

---
apiVersion: v1
kind: Service
metadata:
  name: your-app-nodeport
  namespace: ovs
  labels:
    app: your-app-name
    app.kubernetes.io/name: your-app-name
    app.kubernetes.io/component: service
spec:
  type: NodePort
  ports:
  - port: YOUR_PORT
    targetPort: YOUR_PORT
    nodePort: YOUR_NODEPORT  # 30000-32767 范围内
    protocol: TCP
    name: http
  selector:
    app: your-app-name
```

### 3. k8s/dev/configmap.yaml（开发环境配置）

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: your-app-config
  namespace: ovs
  labels:
    app: your-app-name
    app.kubernetes.io/name: your-app-name
    app.kubernetes.io/component: service
    environment: dev
data:
  # 服务器配置
  PORT: "YOUR_PORT"
  HOST: "0.0.0.0"
  NODE_ENV: "development"
  
  # 开发环境特有配置
  DEBUG_MODE: "true"
  LOG_LEVEL: "debug"
  API_BASE_URL: "http://dev-api.example.com"
  CORS_ORIGIN: "*"
```

### 4. k8s/prod/configmap.yaml（生产环境配置）

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: your-app-config
  namespace: ovs
  labels:
    app: your-app-name
    app.kubernetes.io/name: your-app-name
    app.kubernetes.io/component: service
    environment: prod
data:
  # 服务器配置
  PORT: "YOUR_PORT"
  HOST: "0.0.0.0"
  NODE_ENV: "production"
  
  # 生产环境特有配置
  DEBUG_MODE: "false"
  LOG_LEVEL: "info"
  API_BASE_URL: "https://api.production.com"
  CORS_ORIGIN: "https://frontend.production.com"
```

### 5. k8s/dev/secret.yaml（开发环境敏感配置）

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: your-app-secret
  namespace: ovs
  labels:
    app: your-app-name
    app.kubernetes.io/name: your-app-name
    app.kubernetes.io/component: service
    environment: dev
type: Opaque
data:
  # 开发环境数据库连接
  DATABASE_URL: YOUR_DEV_BASE64_ENCODED_DATABASE_URL
  # 开发环境API密钥
  API_SECRET: YOUR_DEV_BASE64_ENCODED_API_SECRET
  JWT_SECRET: YOUR_DEV_BASE64_ENCODED_JWT_SECRET
```

### 6. k8s/prod/secret.yaml（生产环境敏感配置）

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: your-app-secret
  namespace: ovs
  labels:
    app: your-app-name
    app.kubernetes.io/name: your-app-name
    app.kubernetes.io/component: service
    environment: prod
type: Opaque
data:
  # 生产环境数据库连接
  DATABASE_URL: YOUR_PROD_BASE64_ENCODED_DATABASE_URL
  # 生产环境API密钥
  API_SECRET: YOUR_PROD_BASE64_ENCODED_API_SECRET
  JWT_SECRET: YOUR_PROD_BASE64_ENCODED_JWT_SECRET
```

### 5. k8s/deploy-k8s.sh（核心部署脚本）

```bash
#!/bin/bash

# 配置部分 - 根据项目修改
NAMESPACE="ovs"
APP_NAME="your-app-name"
IMAGE_REGISTRY="**************/specific-ai"
IMAGE_NAME="your-app-name"
YOUR_PORT="YOUR_PORT"
YOUR_NODEPORT="YOUR_NODEPORT"

# 默认启用强制更新和最新镜像拉取
FORCE_UPDATE=true
FORCE_LATEST=true
PULL_FIRST=true

# 其余脚本内容保持与 specific-ai-auth 项目一致
# ... (复制完整的部署脚本逻辑)
```

## 部署流程（环境隔离架构）

### 第一步：准备环境配置文件

1. **创建环境目录结构**：
   

```bash
   mkdir -p k8s/dev k8s/prod
   ```

2. **准备开发环境配置**：
   - 复制模板到 `k8s/dev/configmap.yaml` 和 `k8s/dev/secret.yaml`

   - 配置开发环境的数据库、API等信息

3. **准备生产环境配置**：
   - 复制模板到 `k8s/prod/configmap.yaml` 和 `k8s/prod/secret.yaml`

   - 配置生产环境的数据库、API等信息

4. **替换占位符**：
   - `your-app-name` → 你的应用名称
   - `YOUR_PORT` → 你的应用端口
   - `YOUR_NODEPORT` → NodePort端口（30000-32767范围）
   - `YOUR_*_BASE64_ENCODED_*` → 对应环境的base64编码配置

5. **生成base64编码**：
   

```bash
   # 开发环境示例
   echo -n "**********************************/app_dev" | base64
   
   # 生产环境示例
   echo -n "***********************************/app_prod" | base64
   ```

### 第二步：构建统一镜像

```bash
# 在开发机器上构建业务逻辑镜像
./build-and-push.sh
```

### 第三步：环境部署

**开发环境部署**：

```bash
cd k8s/
chmod +x deploy-k8s.sh
./deploy-k8s.sh --env dev
```

**生产环境部署**：

```bash
./deploy-k8s.sh --env prod
```

### 第四步：验证部署

脚本会自动执行以下验证：
1. **环境配置检查** - 确保使用正确的环境配置
2. **Pod内部健康检查** - 验证应用正常启动
3. **ClusterIP服务访问** - 验证集群内部访问
4. **NodePort外部访问** - 验证外部访问

## 常用命令

### 查看部署状态

```bash
kubectl get pods -n ovs -l app=your-app-name
kubectl get services -n ovs -l app=your-app-name
kubectl describe deployment your-app-name -n ovs
```

### 查看日志

```bash
kubectl logs -n ovs -l app=your-app-name --tail=50
kubectl logs -f -n ovs -l app=your-app-name  # 实时日志
```

### 进入容器调试

```bash
kubectl exec -it -n ovs $(kubectl get pod -n ovs -l app=your-app-name -o jsonpath='{.items[0].metadata.name}') -- /bin/sh
```

### 重启部署

```bash
kubectl rollout restart deployment/your-app-name -n ovs
```

### 手动测试连通性

```bash
# NodePort访问测试
curl http://NODE_IP:YOUR_NODEPORT/api/health

# 集群内部访问测试
kubectl run test-pod --rm -i --restart=Never --image=busybox -- wget -q --spider http://your-app-service.ovs.svc.cluster.local:YOUR_PORT/api/health
```

## 故障排查

### 常见问题

1. **ImagePullBackOff**
   - 检查镜像是否存在： `docker pull **************/specific-ai/your-app-name:latest`

   - 检查私有仓库连通性： `ping **************`

2. **CrashLoopBackOff**
   - 查看Pod日志： `kubectl logs -n ovs pod-name`

   - 检查配置是否正确
   - 检查健康检查端点是否可访问

3. **服务无法访问**
   - 检查Service selector是否匹配Pod labels
   - 检查端口配置是否正确
   - 检查防火墙设置

### 调试模式

使用详细输出模式：

```bash
./deploy-k8s.sh --verbose
```

使用干运行模式查看将要执行的操作：

```bash
./deploy-k8s.sh --dry-run
```

## 最佳实践

### 1. 配置管理

* 敏感信息使用Secret，非敏感信息使用ConfigMap
* 所有Secret值必须base64编码
* 环境变量统一通过ConfigMap/Secret注入

### 2. 资源管理

* 合理设置CPU和内存限制
* 配置适当的健康检查探针
* 使用标签进行资源分类

### 3. 部署策略

* 默认使用强制更新确保使用最新镜像
* 每次部署前自动清理旧资源
* 部署后执行完整的连通性测试

### 4. 监控和日志

* 配置合适的日志输出级别
* 使用标准化的健康检查端点
* 实现优雅的服务关闭

## 环境隔离架构优势

### 1. 一致性保证

* **镜像统一**: 开发和生产使用同一latest镜像，确保代码逻辑完全一致
* **构建一次，多环境部署**: 减少因环境差异导致的问题

### 2. 配置管理优化

* **环境隔离**: 开发和生产配置完全分离，避免配置混乱
* **安全性提升**: 敏感配置与代码完全分离，降低泄露风险
* **版本控制**: 环境配置可独立版本管理

### 3. 运维效率提升

* **部署简化**: 一个脚本处理所有环境，只需切换参数
* **故障隔离**: 环境问题不会相互影响
* **配置审计**: 每个环境的配置变更都有明确记录

### 4. DevOps最佳实践

* **CI/CD优化**: 构建流程简化，只需构建一次镜像
* **快速切换**: 可以快速在不同环境间切换配置
* **标准化**: 所有项目使用相同的部署模式

## 端口规划建议

为避免端口冲突，建议按以下规则分配：

| 服务类型 | 应用端口范围 | NodePort范围 |
|---------|-------------|-------------|
| 认证服务 | 10086 | 30086 |
| API服务 | 8000-8099 | 30000-30099 |
| 前端服务 | 3000-3099 | 30100-30199 |
| 其他服务 | 9000-9099 | 30200-30299 |

## 镜像构建问题排查

### 常见镜像问题

1. **Cannot find module 错误**
   

```
   Error: Cannot find module '/app/node'
   ```

   **原因**: Dockerfile配置问题，通常是：
   - 使用了不兼容的基础镜像（如distroless）
   - 健康检查路径错误
   - ESM模块兼容性问题

   **解决方案**:
   

```dockerfile
   # 使用兼容的基础镜像
   FROM node:20-alpine AS production
   
   # 正确的健康检查路径
   HEALTHCHECK CMD ["node", "-e", "...http://localhost:PORT/api/health..."]
   ```

2. **重新构建和推送镜像**
   

```bash
   # 在源代码机器上执行
   ./build-and-push.sh
   
   # 然后在部署机器上强制更新
   ./deploy-k8s.sh
   ```

3. **验证镜像正确性**
   

```bash
   # 本地测试镜像
   docker run --rm -p 10086:10086 **************/specific-ai/your-app:latest
   
   # 检查健康检查端点
   curl http://localhost:10086/api/health
   ```

## 结语

本文档提供了基于实践的K8s部署标准流程。每个新项目只需要：

1. 复制配置模板
2. 修改项目相关配置
3. 执行一键部署脚本

这样确保了部署流程的标准化和自动化，减少了人为错误，提高了部署效率。
