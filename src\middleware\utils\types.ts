/**
 * 中间件系统类型定义
 * 基于 Next.js Edge Runtime 环境设计
 */

import { NextRequest, NextResponse } from 'next/server'

/**
 * 中间件执行结果类型
 */
export type MiddlewareResult = NextResponse | null

/**
 * 中间件函数签名
 * @param request - Next.js 请求对象
 * @returns 响应对象或 null（继续执行后续中间件）
 */
export type MiddlewareFunction = (
  request: NextRequest
) => Promise<MiddlewareResult> | MiddlewareResult

/**
 * 中间件执行上下文
 * 用于在中间件之间传递状态信息
 */
export interface MiddlewareContext {
  /** 请求路径 */
  pathname: string
  /** 用户认证状态 */
  isAuthenticated?: boolean
  /** 用户会话数据 */
  session?: any
  /** 用户角色信息 */
  userRole?: string
  /** 是否为管理员 */
  isAdmin?: boolean
  /** 执行耗时统计 */
  timing: {
    start: number
    auth?: number
    business?: number
    security?: number
  }
}

/**
 * 中间件配置选项
 */
export interface MiddlewareOptions {
  /** 是否启用调试日志 */
  enableDebugLogs?: boolean
  /** 是否启用性能统计 */
  enableTiming?: boolean
  /** 错误处理策略 */
  errorHandling?: 'continue' | 'stop'
}

/**
 * 增强的中间件函数签名
 * 支持上下文传递
 */
export type EnhancedMiddlewareFunction = (
  request: NextRequest,
  context: MiddlewareContext
) => Promise<MiddlewareResult> | MiddlewareResult

/**
 * 路由匹配器类型
 */
export type RouteMatcher = (pathname: string) => boolean

/**
 * 重定向规则定义
 */
export interface RedirectRule {
  /** 匹配条件 */
  condition: (request: NextRequest, context: MiddlewareContext) => boolean
  /** 重定向目标 */
  target: string | ((request: NextRequest, context: MiddlewareContext) => string)
  /** 规则优先级 */
  priority: number
}
