# Vibe Coding Refactory - 项目 README

欢迎来到 Vibe Coding Refactory 项目！本文档旨在为开发者提供一个全面的指南，帮助您快速了解项目架构、搭建开发环境并参与贡献。

## 1. 项目概览

本项目是一个基于 **Next.js 14 (App Router)** 构建的现代化、功能丰富的 Web 应用程序。它采用了业界领先的技术栈和开发实践，旨在实现高性能、高可维护性和卓越的用户体验。项目核心功能包括但不限于用户认证、实时数据通信和可嵌入的IFrame模块。

## 2. 核心技术栈

- **框架:** Next.js 14+ (App Router)
- **语言:** TypeScript
- **UI:** React 18
- **样式:** Tailwind CSS
- **UI组件库:** Radix UI (底层) + 自定义组件 (类Shadcn/UI模式)
- **状态管理:** Zustand
- **实时通信:** Socket.IO (通过 `use-websocket.ts` Hook封装)
- **API请求:** 基于 `fetch` 的自定义拦截器 Hooks
- **测试:** Playwright (端到端测试)
- **包管理器:** pnpm

## 3. 项目架构

我们采用功能驱动的目录结构，以确保代码的高内聚和低耦合。

- **`src/app`**: 页面和路由，遵循 Next.js App Router 规范。
  - `[locale]`: 所有面向用户的路由都位于此动态路由下，以支持国际化 (i18n)。
  - `api`: 后端 API 代理和内部接口。
- **`src/components`**: 可复用的 React 组件。
  - `ui`: 基础UI组件，如 Button, Card, Input 等。
  - `layout`: 页面布局相关组件。
  - `forms`: 表单相关组件。
  - `embed`: iFrame嵌入页面专用组件。
- **`src/hooks`**: 全局自定义 React Hooks。命名以 `use-` 开头。
- **`src/lib`**: 核心业务逻辑、工具函数和第三方库配置。
  - `auth`: 用户认证逻辑。
  - `http`: 统一的API请求处理。
  - `i18n`: 国际化配置。
- **`src/stores`**: Zustand 状态管理。Store 文件以 `-store.ts` 结尾。
- **`src/types`**: 全局 TypeScript 类型定义。

## 4. 本地开发环境搭建

请遵循以下步骤来设置您的本地开发环境。

### 步骤 1: 环境准备

- 确保您已安装 [Node.js](https://nodejs.org/) (推荐 v18 或更高版本)。
- 确保您已安装 [pnpm](https://pnpm.io/installation)。

### 步骤 2: 克隆与安装

```bash
# 克隆项目仓库
git clone <your-repository-url>

# 进入项目目录
cd vibe-coding-refactory

# 使用 pnpm 安装依赖
pnpm install
```

### 步骤 3: 配置环境变量

项目使用环境变量进行配置。

1.  复制环境变量示例文件：
    ```bash
    cp .env.example .env.development
    ```
2.  根据您的本地开发需求，编辑 `.env.development` 文件，填入必要的配置信息（如API地址、认证密钥等）。

### 步骤 4: 启动开发服务器

```bash
# 启动 Next.js 开发服务器
pnpm run dev
```

现在，您可以在浏览器中访问 `http://localhost:3000` 来查看应用程序。

## 5. 核心功能实现指南

为保证代码的一致性和可维护性，请遵循以下核心功能的实现规范。

### 状态管理 (Zustand)

- **必须** 按功能领域创建独立的 Store 文件（如 `auth-store.ts`）。
- **推荐** 使用 Immer 中间件来简化复杂状态的不可变更新。
- **必须** 通过自定义选择器 Hook 从组件中消费状态，以优化性能。

### API 数据请求

- 所有与后端的HTTP通信 **必须** 通过 `src/hooks/useHttp.ts` 或 `src/hooks/useAuthHttp.ts` 进行。
- **严禁** 在组件中直接使用原生的 `fetch` 或 `axios`，以确保所有请求都经过统一的拦截器处理。

### 实时通信 (WebSocket)

- 所有 WebSocket 相关功能 **必须** 使用 `src/hooks/use-websocket.ts` Hook。
- **严禁** 在组件中直接实例化 `socket.io-client`。

### iFrame 嵌入

- iFrame 嵌入页面的所有状态和逻辑 **必须** 通过 `EmbedContext` (`src/contexts/embed-context.tsx`) 进行管理。
- 在嵌入页面中，**必须** 使用 `useEmbed()` Hook 来获取用户信息、Token等数据。

## 6. 代码质量与工作流

### 代码检查与格式化

在提交代码前，**必须** 在本地运行以下命令并确保全部通过：

```bash
# 运行 ESLint 进行代码风格检查
pnpm run lint

# 运行 TypeScript 进行类型检查
pnpm run type-check
```

### Git 提交规范

- **必须** 遵循 **Conventional Commits** 规范。
- **格式:** `<type>(<scope>): <subject>`
- **示例:**
  ```
  feat(auth): implement user password reset flow
  fix(embed): correct token parsing from URL parameters
  ```

### 测试

- **推荐** 为核心的用户流程编写 Playwright 端到端测试用例。

## 7. 构建与部署

### 生产构建

```bash
# 创建生产环境优化版本
pnpm run build
```

### 部署

项目支持通过 **Docker** 进行容器化部署。

1.  使用 `Dockerfile` 和 `docker-compose.yml` 构建和管理镜像。
2.  `build_push_image.sh` 和 `deploy.sh` 脚本可用于自动化构建和部署流程。

---

_本文档根据 `GEMINI.md` 开发规范和项目结构自动生成。_
