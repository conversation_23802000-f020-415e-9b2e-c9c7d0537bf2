/**
 * 聊天容器组件 - 主要布局和状态协调器
 *
 * 功能：整体布局管理、状态协调、组件组合
 * 依赖：新的store架构、各个子组件
 * 性能：轻量级容器，不包含复杂业务逻辑
 *
 * 数据流：Store -> Container -> Child Components
 */

'use client'

import { useRef, useCallback, useMemo } from 'react'
import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'

// 新的store hooks
import { useConnection } from '@/stores/connection-store'
import { useSession } from '@/stores/session-store'
import { useMessages } from '@/stores/message-store'
import { useUserInput, useMobileState } from '@/stores/ui-store'

// 子组件
import { MessageList } from './message-list'
import { AIChatInput } from '@/components/ui/ai-chat-input'
import { ConnectionStatus } from './connection-status'
import { ChatControls } from './chat-controls'

// Hooks
import { useSimpleScroll } from '@/hooks/use-simple-scroll'
import { useKeyboardShortcuts } from '@/hooks/use-keyboard-shortcuts'
import { useMobileOptimization } from '@/hooks/use-mobile-optimization'

// ============================================================================
// 组件接口定义
// ============================================================================

export interface ChatContainerProps {
  /** 聊天界面显示配置 */
  appearance?: {
    showAvatar?: boolean
    showTimestamp?: boolean
    renderMode?: 'bubble' | 'card' | 'system'
    userAvatarUrl?: string
    assistantAvatarUrl?: string
  }

  /** 功能开关 */
  features?: {
    enableFileUpload?: boolean
    enableVoiceInput?: boolean
    enableThinkMode?: boolean
    enableDeepSearch?: boolean
  }

  /** UI配置 */
  ui?: {
    placeholder?: string
    maxInputRows?: number
    className?: string
  }

  /** 事件回调 */
  callbacks?: {
    onMessageSent?: (content: string) => void
    onError?: (error: Error) => void
    onConnectionChange?: (status: string) => void
  }
}

// 默认配置
const DEFAULT_PROPS = {
  appearance: {
    showAvatar: true,
    showTimestamp: true,
    renderMode: 'card' as const,
  },
  features: {
    enableFileUpload: false,
    enableVoiceInput: false,
    enableThinkMode: false,
    enableDeepSearch: false,
  },
  ui: {
    placeholder: '输入消息...',
    maxInputRows: 5,
    className: '',
  },
  callbacks: {},
}

// ============================================================================
// 主要组件
// ============================================================================

export const ChatContainer: React.FC<ChatContainerProps> = props => {
  const config = useMemo(
    () => ({
      appearance: { ...DEFAULT_PROPS.appearance, ...props.appearance },
      features: { ...DEFAULT_PROPS.features, ...props.features },
      ui: { ...DEFAULT_PROPS.ui, ...props.ui },
      callbacks: { ...DEFAULT_PROPS.callbacks, ...props.callbacks },
    }),
    [props]
  )

  // ============================================================================
  // Store状态获取
  // ============================================================================

  const { connectionStatus, isConnected } = useConnection()
  const { currentSession } = useSession()
  const { messages, sendUserMessage, pendingSentMessages } = useMessages()
  const { userInput, updateUserInput, clearUserInput, setIsSubmitting } = useUserInput()
  const { isMobileView, isKeyboardVisible, viewportHeight } = useMobileState()

  // ============================================================================
  // Refs和本地状态
  // ============================================================================

  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLTextAreaElement>(null)

  // ============================================================================
  // 消息和滚动逻辑
  // ============================================================================

  const sessionMessages = useMemo(() => {
    return messages
  }, [messages])

  // 检测是否有streaming消息
  const hasStreamingMessage = useMemo(() => {
    const lastMessage = sessionMessages[sessionMessages.length - 1]
    return (
      lastMessage &&
      lastMessage.payload.type === 'streaming' &&
      lastMessage.metadata?.streamingState &&
      !lastMessage.metadata.streamingState.isComplete
    )
  }, [sessionMessages])

  // 移动端优化
  const { screenSize } = useMobileOptimization({
    enableViewportLock: true,
    enableKeyboardAware: true,
    enableTouchFeedback: true,
    debug: process.env.NODE_ENV === 'development',
  })

  // 简化滚动管理 (从277行复杂实现优化为80行)
  const { isAtBottom, unreadCount, scrollToBottom, handleScroll } = useSimpleScroll({
    containerRef: scrollAreaRef,
    endRef: messagesEndRef,
    messageCount: sessionMessages.length,
    hasStreamingMessage: hasStreamingMessage || false,
    threshold: 100,
    debug: process.env.NODE_ENV === 'development',
  })

  // ============================================================================
  // 用户交互处理
  // ============================================================================

  const focusInput = useCallback(() => {
    inputRef.current?.focus()
  }, [])

  const clearInput = useCallback(() => {
    clearUserInput()
    focusInput()
  }, [clearUserInput, focusInput])

  const clearChat = useCallback(() => {
    if (currentSession && sessionMessages.length > 0) {
      if (window.confirm('确定要清除当前对话记录吗？此操作无法撤销。')) {
        // 调用store的清理方法
        focusInput()
      }
    }
  }, [currentSession, sessionMessages.length, focusInput])

  // 🎯 防重复提交：记录最后一次提交的内容和时间
  const lastSubmitRef = useRef<{ content: string; timestamp: number }>({
    content: '',
    timestamp: 0,
  })

  // 消息发送处理
  const handleSubmit = useCallback(async () => {
    if (!userInput.content.trim() || userInput.isSubmitting || !currentSession) {
      return
    }

    const messageContent = userInput.content.trim()
    const now = Date.now()

    // 🎯 防重复提交：如果500ms内提交了相同内容，则忽略
    if (
      lastSubmitRef.current.content === messageContent &&
      now - lastSubmitRef.current.timestamp < 500
    ) {
      return
    }

    // 记录本次提交
    lastSubmitRef.current = { content: messageContent, timestamp: now }

    try {
      setIsSubmitting(true)

      // 发送消息
      await sendUserMessage(messageContent, currentSession.sessionId)

      // 清空输入
      clearUserInput()

      // 滚动到底部
      setTimeout(() => scrollToBottom(), 100)

      // 触发回调
      config.callbacks.onMessageSent?.(messageContent)
    } catch (error) {
      console.error('发送消息失败:', error)
      const err = error instanceof Error ? error : new Error('发送失败')
      config.callbacks.onError?.(err)
    } finally {
      setIsSubmitting(false)
    }
  }, [
    userInput,
    sendUserMessage,
    currentSession,
    clearUserInput,
    scrollToBottom,
    config.callbacks,
    setIsSubmitting,
  ])

  // 快捷键处理
  const { handleInputKeyDown } = useKeyboardShortcuts({
    enabled: true,
    onSend: handleSubmit,
    onClearInput: clearInput,
    onFocusInput: focusInput,
    onClearChat: clearChat,
    onShowHelp: () => {
      console.log('显示快捷键帮助')
    },
    debug: process.env.NODE_ENV === 'development',
  })

  // ============================================================================
  // 渲染
  // ============================================================================

  return (
    <div
      className={cn('flex flex-col h-full bg-background', config.ui.className)}
      style={{
        // 移动端键盘感知高度调整
        height: isMobileView && isKeyboardVisible ? `${viewportHeight}px` : undefined,
      }}
    >
      {/* 开发环境调试面板 */}
      {process.env.NODE_ENV === 'development' && (
        <div className="bg-yellow-50 border-b border-yellow-200 p-2 text-xs">
          <div className="flex gap-4 flex-wrap">
            <span>🔗 连接: {isConnected ? '✅' : '❌'}</span>
            <span>📡 状态: {connectionStatus}</span>
            <span>🔒 输入禁用: {!isConnected || userInput.isSubmitting ? '✅' : '❌'}</span>
            <span>📝 提交中: {userInput.isSubmitting ? '✅' : '❌'}</span>
            <span>💬 内容长度: {userInput.content.length}</span>
            <span>📱 移动端: {isMobileView ? '✅' : '❌'}</span>
            <span>📐 尺寸: {screenSize}</span>
            <span>⌨️ 键盘: {isKeyboardVisible ? '✅' : '❌'}</span>
            <span>💡 input可点击: {isConnected && !userInput.isSubmitting ? '✅' : '❌'}</span>
            <span>📋 会话: {currentSession ? '✅' : '❌'}</span>
            <span>🎯 会话ID: {currentSession?.sessionId?.slice(0, 8) || 'N/A'}</span>
          </div>
        </div>
      )}

      {/* 顶部状态栏 */}
      <ConnectionStatus
        connectionStatus={connectionStatus}
        isConnected={isConnected}
        currentSession={currentSession}
        messageCount={sessionMessages.length}
        showControls={process.env.NODE_ENV === 'development'}
      />

      {/* 消息列表区域 */}
      <div className="flex-1 relative overflow-hidden min-h-0">
        <MessageList
          ref={scrollAreaRef}
          messages={sessionMessages}
          isAtBottom={isAtBottom}
          unreadCount={unreadCount}
          onScroll={handleScroll}
          onScrollToBottom={scrollToBottom}
          messagesEndRef={messagesEndRef}
          appearance={config.appearance}
          isMobile={isMobileView}
          pendingSentMessages={pendingSentMessages}
        />
      </div>

      {/* 底部输入区域 */}
      <div className="border-t bg-background/80 backdrop-blur-sm p-3 sm:p-4 pb-safe">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-end space-x-2 sm:space-x-3">
            <div className="flex-1">
              <AIChatInput
                ref={inputRef}
                value={userInput.content}
                onChange={e => updateUserInput(e.target.value)}
                onSubmit={handleSubmit}
                onKeyDown={handleInputKeyDown}
                placeholder={config.ui.placeholder}
                disabled={!isConnected || userInput.isSubmitting}
                maxRows={config.ui.maxInputRows}
                autoFocus={true}
                features={config.features}
                className="w-full"
              />
            </div>

            {/* <ChatControls
              canSend={!!(userInput.content.trim() && isConnected && !userInput.isSubmitting)}
              isSubmitting={userInput.isSubmitting}
              onSend={handleSubmit}
              features={config.features}
              isMobile={isMobileView}
            /> */}
          </div>
        </div>
      </div>
    </div>
  )
}

// ============================================================================
// 导出
// ============================================================================

ChatContainer.displayName = 'ChatContainer'

export default ChatContainer
