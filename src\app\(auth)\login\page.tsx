'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { GalleryVerticalEnd, CheckCircle } from 'lucide-react'
import { LoginForm } from '@/components/common/login-form'
import { useSession } from '@/components/providers/session-provider'
import { Button } from '@/components/ui/button'
import Loading from '@/app/loading'
export default function LoginPage() {
  const { isAuthenticated, loading, user } = useSession()
  const router = useRouter()

  // 已登录用户自动跳转到dashboard
  useEffect(() => {
    if (!loading && isAuthenticated) {
      router.push('/chat')
    }
  }, [isAuthenticated, loading, router])

  // 显示加载状态
  if (loading) {
    return <Loading />
  }

  // 未登录用户显示登录表单
  return (
    <div className="min-h-svh flex flex-col items-center p-4 sm:p-6 md:p-10 overflow-y-auto">
      <div className="flex w-full max-w-sm flex-col gap-4 sm:gap-6 my-auto py-4">
        <a href="#" className="flex items-center gap-2 self-center font-medium">
          <div className="bg-primary text-primary-foreground flex size-6 items-center justify-center rounded-md">
            <GalleryVerticalEnd className="size-4" />
          </div>
          GoGlobal
        </a>
        <LoginForm />
      </div>
    </div>
  )
}
