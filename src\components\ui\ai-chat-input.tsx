'use client'

import * as React from 'react'
import { useState, useEffect, useRef } from 'react'
import { Lightbulb, Globe, Paperclip, Send, Mic } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { cn } from '@/lib/utils'

// Store integration
import { useInteractionState } from '@/stores/ui-store'

// Features configuration type (compatible with MessageInput)
interface Features {
  enableFileUpload?: boolean
  enableVoiceInput?: boolean
  enableThinkMode?: boolean
  enableDeepSearch?: boolean
}

interface AIChatInputProps {
  value: string
  onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void
  onSubmit: (e?: React.FormEvent) => void
  onKeyDown?: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void
  onAttachClick?: (() => void) | undefined
  onMicClick?: (() => void) | undefined
  onThinkChange?: ((active: boolean) => void) | undefined
  onDeepSearchChange?: ((active: boolean) => void) | undefined
  placeholder?: string
  disabled?: boolean
  className?: string
  thinkActive?: boolean
  deepSearchActive?: boolean
  showControls?: boolean
  maxRows?: number
  autoFocus?: boolean

  /** Features configuration - compatible with ChatContainer */
  features?: Features
}

const AIChatInput = React.forwardRef<HTMLTextAreaElement, AIChatInputProps>(
  (
    {
      value,
      onChange,
      onSubmit,
      onKeyDown,
      onAttachClick,
      onMicClick,
      onThinkChange,
      onDeepSearchChange,
      placeholder = 'Ask something...',
      disabled = false,
      className,
      thinkActive = false,
      deepSearchActive = false,
      showControls = true,
      maxRows = 5,
      autoFocus = false,
      features,
    },
    ref
  ) => {
    const [isActive, setIsActive] = useState(false)
    const textareaRef = useRef<HTMLTextAreaElement>(null)

    // 🎯 Store integration: 集成 UI Store 状态管理
    const { interactionState, setThinkMode, setDeepSearch } = useInteractionState()

    // 🎯 智能状态管理：优先使用 store，fallback 到 props
    const thinkModeActive = interactionState.thinkMode ?? thinkActive ?? false
    const deepSearchModeActive = interactionState.deepSearch ?? deepSearchActive ?? false

    // 🎯 Features 配置解析：兼容 ChatContainer 的 features 配置
    const featuresConfig = features || {}
    const showFileUpload = featuresConfig.enableFileUpload ?? onAttachClick !== undefined
    const showVoiceInput = featuresConfig.enableVoiceInput ?? onMicClick !== undefined
    const showThinkToggle = featuresConfig.enableThinkMode ?? true
    const showDeepSearchToggle = featuresConfig.enableDeepSearch ?? true
    // 合并外部ref和内部ref
    React.useImperativeHandle(ref, () => textareaRef.current!, [])

    // 自动聚焦
    useEffect(() => {
      if (autoFocus && textareaRef.current) {
        textareaRef.current.focus()
      }
    }, [autoFocus])

    // Auto-resize textarea height
    useEffect(() => {
      if (textareaRef.current) {
        const textarea = textareaRef.current
        textarea.style.height = 'auto' // Reset height to recalculate
        const scrollHeight = textarea.scrollHeight

        if (maxRows) {
          // A reasonable approximation of line height
          const lineHeight = parseFloat(window.getComputedStyle(textarea).lineHeight)
          const maxHeight = lineHeight * maxRows

          if (scrollHeight > maxHeight) {
            textarea.style.height = `${maxHeight}px`
            textarea.style.overflowY = 'auto'
          } else {
            textarea.style.height = `${scrollHeight}px`
            textarea.style.overflowY = 'hidden'
          }
        } else {
          textarea.style.height = `${scrollHeight}px`
          textarea.style.overflowY = 'hidden'
        }
      }
    }, [value, maxRows])

    // ============================================================================
    // 事件处理优化 - 支持 Store 和 Props 混合模式
    // ============================================================================

    const handleFocus = () => {
      setIsActive(true)
    }

    const handleBlur = () => {
      setIsActive(false)
    }

    const handleClick = (e: React.MouseEvent<HTMLTextAreaElement>) => {
      // 阻止事件冒泡，避免其他处理器干扰
      e.stopPropagation()

      if (!disabled) {
        e.currentTarget.focus()
      }
    }

    // 🎯 ThinkMode 切换处理 - 优先使用 Store，同时保持 props 兼容性
    const handleThinkToggle = () => {
      const newState = !thinkModeActive
      // 优先使用 store 更新
      setThinkMode(newState)
      // 同时触发 props 回调（向后兼容）
      onThinkChange?.(newState)
    }

    // 🎯 DeepSearch 切换处理 - 优先使用 Store，同时保持 props 兼容性
    const handleDeepSearchToggle = () => {
      const newState = !deepSearchModeActive
      setDeepSearch(newState)
      onDeepSearchChange?.(newState)
    }

    // 🎯 文件上传处理 - 提供默认空处理以增强兼容性
    const handleAttachClick = () => {
      onAttachClick?.()
    }

    // 🎯 语音输入处理 - 提供默认空处理以增强兼容性
    const handleMicClick = () => {
      onMicClick?.()
    }

    // 🎯 动态控制面板显示逻辑
    const showExpandedControls =
      (isActive || value) && showControls && (showThinkToggle || showDeepSearchToggle)

    const containerVariants = {
      collapsed: {
        height: 68,
        boxShadow: '0 2px 8px 0 rgba(0,0,0,0.08)',
        transition: { type: 'spring', stiffness: 120, damping: 18 },
      },
      expanded: {
        height: 'auto',
        boxShadow: '0 8px 32px 0 rgba(0,0,0,0.16)',
        transition: { type: 'spring', stiffness: 120, damping: 18 },
      },
    }

    return (
      <motion.div
        className={cn(
          'w-full max-w-4xl bg-white rounded-2xl sm:rounded-3xl overflow-hidden',
          className
        )}
        variants={containerVariants}
        animate={isActive || value ? 'expanded' : 'collapsed'}
        initial="collapsed"
      >
        <div className="flex flex-col items-stretch w-full h-full p-2 sm:p-3">
          {/* Input Row */}
          <div className="flex items-start gap-1 sm:gap-2 w-full">
            {/* 🎯 文件上传按钮 - 根据 features 配置显示 */}
            {showFileUpload && (
              <button
                className="p-2 sm:p-3 rounded-full hover:bg-gray-100 transition-colors disabled:opacity-50 min-w-[44px] min-h-[44px] flex items-center justify-center"
                title="附加文件"
                type="button"
                onClick={handleAttachClick}
                disabled={disabled}
              >
                <Paperclip size={18} className="sm:w-5 sm:h-5" />
              </button>
            )}

            {/* Textarea */}
            <div className="relative flex-1 pl-1 sm:pl-2">
              <textarea
                ref={textareaRef}
                value={value}
                onChange={onChange}
                onKeyDown={onKeyDown}
                onFocus={handleFocus}
                onBlur={handleBlur}
                onClick={handleClick}
                placeholder={placeholder}
                disabled={disabled}
                rows={1}
                className="ai-chat-textarea flex-1 border-0 outline-0 rounded-md text-base bg-transparent w-full font-normal resize-none pr-20"
                style={{
                  boxSizing: 'border-box',
                  overflowY: 'hidden',
                  pointerEvents: 'auto',
                  position: 'relative',
                  zIndex: 10,
                }}
              />
            </div>

            {/* 🎯 语音输入按钮 - 根据 features 配置显示 */}
            {showVoiceInput && (
              <button
                className="p-2 sm:p-3 rounded-full hover:bg-gray-100 transition-colors disabled:opacity-50 min-w-[44px] min-h-[44px] flex items-center justify-center"
                title="语音输入"
                type="button"
                onClick={handleMicClick}
                disabled={disabled}
              >
                <Mic size={18} className="sm:w-5 sm:h-5" />
              </button>
            )}
            {/* <div className='h-full flex items-end'>
              <button
                className="flex items-center justify-center bg-black hover:bg-zinc-700 text-white p-3 rounded-full font-medium transition-colors disabled:opacity-50 disabled:bg-gray-400"
                title="Send"
                type="button"
                onClick={onSubmit}
                disabled={!value || disabled}
              >
                <Send size={18} />
              </button>
            </div> */}
          </div>

          {/* Expanded Controls */}
          <AnimatePresence>
            {showExpandedControls && (
              <motion.div
                className="w-full flex justify-start px-2 sm:px-4 items-center text-xs sm:text-sm mt-2"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0, transition: { duration: 0.3, delay: 0.1 } }}
                exit={{ opacity: 0, y: 10, transition: { duration: 0.2 } }}
              >
                <div className="flex gap-2 sm:gap-3 items-center">
                  {/* 🎯 思考模式切换 - 根据 features 配置显示 */}
                  {showThinkToggle && (
                    <button
                      className={cn(
                        'flex items-center gap-1 px-3 sm:px-4 py-2 rounded-full transition-all font-medium group text-xs sm:text-sm min-h-[36px]',
                        thinkModeActive
                          ? 'bg-blue-600/10 outline outline-blue-600/60 text-blue-950'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      )}
                      title="思考模式"
                      type="button"
                      onClick={e => {
                        e.stopPropagation()
                        handleThinkToggle()
                      }}
                    >
                      <Lightbulb
                        className={cn(
                          'transition-all',
                          thinkModeActive ? 'fill-blue-500' : 'group-hover:fill-yellow-300'
                        )}
                        size={18}
                      />
                      思考模式
                    </button>
                  )}

                  {/* 🎯 深度搜索切换 - 根据 features 配置显示 */}
                  {showDeepSearchToggle && (
                    <button
                      className={cn(
                        'flex items-center gap-1 px-3 sm:px-4 py-2 rounded-full transition-all font-medium text-xs sm:text-sm min-h-[36px]',
                        deepSearchModeActive
                          ? 'bg-purple-600/10 outline outline-purple-600/60 text-purple-950'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      )}
                      title="深度搜索"
                      type="button"
                      onClick={e => {
                        e.stopPropagation()
                        handleDeepSearchToggle()
                      }}
                    >
                      <Globe
                        className={cn(
                          'transition-all',
                          deepSearchModeActive ? 'text-purple-600' : ''
                        )}
                        size={18}
                      />
                      深度搜索
                    </button>
                  )}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
        <style jsx>{`
          .ai-chat-textarea::-webkit-scrollbar {
            width: 6px;
            height: 6px;
          }
          .ai-chat-textarea::-webkit-scrollbar-track {
            background: transparent;
          }
          .ai-chat-textarea::-webkit-scrollbar-thumb {
            background-color: var(--border);
            border-radius: 3px;
          }
          .ai-chat-textarea::-webkit-scrollbar-thumb:hover {
            background-color: var(--muted-foreground);
          }
          .ai-chat-textarea {
            scrollbar-width: thin;
            scrollbar-color: var(--border) transparent;
            pointer-events: auto !important;
            cursor: text;
          }
          .ai-chat-textarea:disabled {
            pointer-events: none;
            cursor: not-allowed;
            opacity: 0.6;
          }
        `}</style>
      </motion.div>
    )
  }
)

AIChatInput.displayName = 'AIChatInput'

export { AIChatInput }
