/**
 * 定时器驱动的Mock控制器
 * 完全避开网络连接问题的兜底方案
 * 保持与Socket.IO相同的接口和功能
 */

import type {
  BaseWebSocketMessage,
  StreamingPayload,
  CheckpointPayload,
  ReportPayload,
  ErrorPayload,
  FormField,
} from '@/types/websocket-event-type'

// Mock场景配置
interface MockScenario {
  name: string
  description: string
  responseType: 'streaming' | 'checkpoint' | 'report' | 'error' | 'mixed'
  delayMs: number
  shouldFail?: boolean
}

// 预定义的测试场景
const mockScenarios: Record<string, MockScenario> = {
  'streaming-fast': {
    name: 'streaming-fast',
    description: '快速流式文本响应',
    responseType: 'streaming',
    delayMs: 50,
  },
  'streaming-slow': {
    name: 'streaming-slow',
    description: '慢速流式文本响应',
    responseType: 'streaming',
    delayMs: 200,
  },
  'streaming-long': {
    name: 'streaming-long',
    description: '长文本流式响应',
    responseType: 'streaming',
    delayMs: 100,
  },
  'form-simple': {
    name: 'form-simple',
    description: '简单表单交互',
    responseType: 'checkpoint',
    delayMs: 500,
  },
  'form-complex': {
    name: 'form-complex',
    description: '复杂表单交互',
    responseType: 'checkpoint',
    delayMs: 800,
  },
  'report-short': {
    name: 'report-short',
    description: '简短分析报告',
    responseType: 'report',
    delayMs: 1000,
  },
  'report-detailed': {
    name: 'report-detailed',
    description: '详细分析报告',
    responseType: 'report',
    delayMs: 2000,
  },
  'error-network': {
    name: 'error-network',
    description: '网络连接错误',
    responseType: 'error',
    delayMs: 100,
    shouldFail: true,
  },
  'error-validation': {
    name: 'error-validation',
    description: '数据验证错误',
    responseType: 'error',
    delayMs: 200,
  },
  'mixed-workflow': {
    name: 'mixed-workflow',
    description: '完整工作流程（流式→表单→报告）',
    responseType: 'mixed',
    delayMs: 300,
  },
}

// 状态接口
export interface TimerMockStatus {
  isConnected: boolean
  currentScenario: string | null
  availableScenarios: Array<{
    name: string
    description: string
    responseType: string
  }>
}

// 消息处理器类型
type MessageHandler = (message: BaseWebSocketMessage) => void

export class TimerMockController {
  private isConnected: boolean = false
  private currentScenario: MockScenario = mockScenarios['streaming-fast']
  private messageHandlers: Set<MessageHandler> = new Set()
  private statusListeners: Set<(status: TimerMockStatus) => void> = new Set()
  private connectionTimer: NodeJS.Timeout | null = null

  constructor() {
    // 模拟连接延迟
    this.simulateConnection()
  }

  // 模拟连接过程
  private simulateConnection(): void {
    console.log('🔗 模拟连接到定时器Mock服务器...')

    this.connectionTimer = setTimeout(() => {
      this.isConnected = true
      console.log('✅ 定时器Mock连接成功')

      // 发送连接确认消息
      const connectionMessage = this.createConnectionMessage()
      this.emitMessage('connection-confirmed', connectionMessage)

      this.notifyStatusChange()
    }, 100) // 100ms后连接成功
  }

  // 连接方法 - 保持与Socket.IO相同接口
  connect(): Promise<void> {
    return new Promise(resolve => {
      if (this.isConnected) {
        resolve()
        return
      }

      // 监听连接状态变化
      const statusListener = (status: TimerMockStatus) => {
        if (status.isConnected) {
          this.removeStatusListener(statusListener)
          resolve()
        }
      }

      this.addStatusListener(statusListener)
    })
  }

  // 断开连接
  disconnect(): void {
    if (this.connectionTimer) {
      clearTimeout(this.connectionTimer)
      this.connectionTimer = null
    }

    this.isConnected = false
    console.log('🔌 定时器Mock连接断开')
    this.notifyStatusChange()
  }

  // 检查连接状态
  isConnectedStatus(): boolean {
    return this.isConnected
  }

  // 切换场景
  switchScenario(scenarioName: string): void {
    if (!this.isConnected) {
      console.warn('⚠️ Mock服务未连接，无法切换场景')
      return
    }

    if (mockScenarios[scenarioName]) {
      this.currentScenario = mockScenarios[scenarioName]
      console.log(`🔄 场景已切换至: ${this.currentScenario.description}`)

      // 发送场景切换确认
      setTimeout(() => {
        this.emitMessage('scenario-switched', {
          scenario: this.currentScenario.name,
          description: this.currentScenario.description,
        })
      }, 50)

      this.notifyStatusChange()
    } else {
      console.warn(`⚠️ 未知场景: ${scenarioName}`)
    }
  }

  // 获取场景列表
  getScenarios(): void {
    if (!this.isConnected) {
      console.warn('⚠️ Mock服务未连接，无法获取场景列表')
      return
    }

    setTimeout(() => {
      this.emitMessage('scenarios-list', {
        current: this.currentScenario.name,
        available: Object.values(mockScenarios),
      })
    }, 50)
  }

  // 发送用户消息
  sendMessage(data: Partial<BaseWebSocketMessage>): void {
    if (!this.isConnected) {
      console.warn('⚠️ Mock服务未连接，无法发送消息')
      return
    }

    const message: BaseWebSocketMessage = {
      groupChatId: data.groupChatId || 'mock-group',
      sessionId: data.sessionId || 'mock-session',
      userId: data.userId || 'mock-user',
      organizationId: data.organizationId || 'mock-org',
      timestamp: new Date().toISOString(),
      ...data,
    } as BaseWebSocketMessage

    console.log('📨 定时器Mock收到用户消息:', message.payload?.type)
    this.handleUserMessage(message)
  }

  // 处理用户消息
  private handleUserMessage(message: BaseWebSocketMessage): void {
    console.log(
      `🎭 处理${this.currentScenario.responseType}场景:`,
      this.currentScenario.description
    )

    switch (this.currentScenario.responseType) {
      case 'streaming':
        this.handleStreamingScenario(message)
        break
      case 'checkpoint':
        this.handleCheckpointScenario(message)
        break
      case 'report':
        this.handleReportScenario(message)
        break
      case 'error':
        this.handleErrorScenario(message)
        break
      case 'mixed':
        this.handleMixedScenario(message)
        break
    }
  }

  // 处理流式场景
  private handleStreamingScenario(originalMessage: BaseWebSocketMessage): void {
    const textChunks = this.getStreamingText(this.currentScenario.name)
    let accumulated = ''

    textChunks.forEach((chunk, index) => {
      setTimeout(
        () => {
          accumulated += chunk
          const isComplete = index === textChunks.length - 1

          const streamingMessage = this.createStreamingMessage(
            originalMessage,
            chunk,
            accumulated,
            isComplete
          )

          this.emitMessage('streaming-message', streamingMessage)
          console.log(`📝 发送流式消息 ${index + 1}/${textChunks.length}`)
        },
        this.currentScenario.delayMs * (index + 1)
      )
    })
  }

  // 处理表单场景
  private handleCheckpointScenario(originalMessage: BaseWebSocketMessage): void {
    setTimeout(() => {
      const formFields = this.getFormFields(this.currentScenario.name)
      const checkpointMessage = this.createCheckpointMessage(originalMessage, formFields)
      this.emitMessage('checkpoint-message', checkpointMessage)
      console.log('📋 发送表单消息:', formFields.length, '个字段')
    }, this.currentScenario.delayMs)
  }

  // 处理报告场景
  private handleReportScenario(originalMessage: BaseWebSocketMessage): void {
    setTimeout(() => {
      const reportContent = this.getReportContent(this.currentScenario.name)
      const reportMessage = this.createReportMessage(originalMessage, reportContent)
      this.emitMessage('report-message', reportMessage)
      console.log('📊 发送报告消息，长度:', reportContent.length)
    }, this.currentScenario.delayMs)
  }

  // 处理错误场景
  private handleErrorScenario(originalMessage: BaseWebSocketMessage): void {
    setTimeout(() => {
      const errorData = this.getErrorData(this.currentScenario.name)
      const errorMessage = this.createErrorMessage(
        originalMessage,
        errorData.code,
        errorData.message
      )
      this.emitMessage('error-message', errorMessage)
      console.log('❌ 发送错误消息:', errorData.code)
    }, this.currentScenario.delayMs)
  }

  // 处理混合场景
  private handleMixedScenario(originalMessage: BaseWebSocketMessage): void {
    // 1. 先发送流式响应
    this.handleStreamingScenario(originalMessage)

    // 2. 然后发送表单
    setTimeout(() => {
      const formFields = this.getFormFields('form-simple')
      const checkpointMessage = this.createCheckpointMessage(originalMessage, formFields)
      this.emitMessage('checkpoint-message', checkpointMessage)
      console.log('📋 混合场景: 发送表单')
    }, 3000)

    // 3. 最后发送报告
    setTimeout(() => {
      const reportContent = this.getReportContent('report-short')
      const reportMessage = this.createReportMessage(originalMessage, reportContent)
      this.emitMessage('report-message', reportMessage)
      console.log('📊 混合场景: 发送报告')
    }, 8000)
  }

  // 添加消息处理器
  addMessageHandler(handler: MessageHandler): void {
    this.messageHandlers.add(handler)
  }

  // 移除消息处理器
  removeMessageHandler(handler: MessageHandler): void {
    this.messageHandlers.delete(handler)
  }

  // 添加状态监听器
  addStatusListener(listener: (status: TimerMockStatus) => void): void {
    this.statusListeners.add(listener)
  }

  // 移除状态监听器
  removeStatusListener(listener: (status: TimerMockStatus) => void): void {
    this.statusListeners.delete(listener)
  }

  // 获取状态
  getStatus(): TimerMockStatus {
    return {
      isConnected: this.isConnected,
      currentScenario: this.currentScenario.name,
      availableScenarios: Object.values(mockScenarios),
    }
  }

  // 发送消息给处理器
  private emitMessage(eventType: string, data: any): void {
    this.messageHandlers.forEach(handler => {
      try {
        handler(data)
      } catch (error) {
        console.error('❌ 消息处理器错误:', error)
      }
    })
  }

  // 通知状态变化
  private notifyStatusChange(): void {
    const status = this.getStatus()
    this.statusListeners.forEach(listener => {
      try {
        listener(status)
      } catch (error) {
        console.error('❌ 状态监听器错误:', error)
      }
    })
  }

  // 创建连接消息
  private createConnectionMessage(): BaseWebSocketMessage {
    return {
      id: `mock-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      groupChatId: 'mock-group',
      sessionId: 'mock-session',
      userId: 'mock-user',
      organizationId: 'mock-org',
      timestamp: new Date().toISOString(),
      payload: {
        type: 'system',
        message: `已连接到定时器Mock服务器 - 当前场景: ${this.currentScenario.description}`,
      } as any,
    }
  }

  // 创建流式消息，metadata中包含累积文本
  private createStreamingMessage(
    original: BaseWebSocketMessage,
    delta: string,
    accumulated: string,
    isComplete: boolean
  ): BaseWebSocketMessage {
    const payload: StreamingPayload = {
      type: 'streaming',
      delta,
      isComplete,
    }

    return {
      ...original,
      timestamp: new Date().toISOString(),
      payload,
      metadata: {
        ...original.metadata,
        streamingState: {
          isComplete,
          accumulatedText: accumulated,
        },
      },
    }
  }

  // 创建表单消息
  private createCheckpointMessage(
    original: BaseWebSocketMessage,
    fields: FormField[]
  ): BaseWebSocketMessage {
    const payload: CheckpointPayload = {
      type: 'checkpoint',
      fields,
    }

    return {
      ...original,
      timestamp: new Date().toISOString(),
      payload,
    }
  }

  // 创建报告消息
  private createReportMessage(
    original: BaseWebSocketMessage,
    content: string
  ): BaseWebSocketMessage {
    const payload: ReportPayload = {
      type: 'report',
      content,
    }

    return {
      ...original,
      timestamp: new Date().toISOString(),
      payload,
    }
  }

  // 创建错误消息
  private createErrorMessage(
    original: BaseWebSocketMessage,
    code: string,
    message: string
  ): BaseWebSocketMessage {
    const payload: ErrorPayload = {
      type: 'error',
      code,
      message,
    }

    return {
      ...original,
      timestamp: new Date().toISOString(),
      payload,
    }
  }

  // 获取流式文本内容
  private getStreamingText(scenarioName: string): string[] {
    const texts: Record<string, string[]> = {
      'streaming-fast': [
        '正在分析您的',
        '请求...',
        '\n\n基于您提供的',
        '信息，我们将',
        '为您生成',
        '详细的分析报告。',
      ],
      'streaming-slow': [
        '欢迎使用',
        '定时器Mock系统。',
        '\n\n我正在',
        '仔细分析',
        '您的数据...',
        '\n\n请稍候，',
        '分析即将完成。',
      ],
      'streaming-long': [
        '# AI分析报告\n\n',
        '## 概述\n',
        '根据您提供的数据，我们进行了全面的分析。',
        '\n\n## 主要发现\n',
        '1. 定时器Mock系统运行正常\n',
        '2. 所有功能完美运作\n',
        '3. 无网络连接问题\n\n',
        '## 建议\n',
        '这是最稳定的Mock方案...',
      ],
    }

    return texts[scenarioName] || texts['streaming-fast']
  }

  // 获取表单字段
  private getFormFields(scenarioName: string): FormField[] {
    const forms: Record<string, FormField[]> = {
      'form-simple': [
        {
          id: 'name',
          label: '姓名',
          type: 'text' as const,
          required: true,
          defaultValue: '',
        },
        {
          id: 'email',
          label: '邮箱',
          type: 'text' as const,
          required: true,
          defaultValue: '',
        },
      ],
      'form-complex': [
        {
          id: 'company',
          label: '公司名称',
          type: 'text' as const,
          required: true,
          defaultValue: '',
        },
        {
          id: 'industry',
          label: '行业',
          type: 'select' as const,
          required: true,
          options: [
            { label: '科技', value: 'tech' },
            { label: '金融', value: 'finance' },
            { label: '制造', value: 'manufacturing' },
          ],
        },
        {
          id: 'size',
          label: '公司规模',
          type: 'radio' as const,
          required: true,
          options: [
            { label: '1-50人', value: 'small' },
            { label: '51-200人', value: 'medium' },
            { label: '200+人', value: 'large' },
          ],
        },
        {
          id: 'description',
          label: '描述',
          type: 'textarea' as const,
          required: false,
          defaultValue: '',
        },
      ],
    }

    return forms[scenarioName] || forms['form-simple']
  }

  // 获取报告内容
  private getReportContent(scenarioName: string): string {
    const reports: Record<string, string> = {
      'report-short': `# 定时器Mock分析报告

## 执行摘要
本次分析基于定时器Mock系统进行，结果表明系统运行完美。

## 主要指标
- **稳定性**: 100%
- **响应速度**: 毫秒级
- **用户体验**: 5.0/5.0

## 建议
1. 定时器Mock是最可靠的解决方案
2. 完全避开了网络连接问题
3. 保持了所有Socket.IO功能`,

      'report-detailed': `# 详细分析报告

## 1. 概述
本报告详细分析了定时器Mock系统的优势和特性。

## 2. 技术优势

### 2.1 稳定性分析
\`\`\`javascript
const stability = {
  networkIssues: "0%",
  reliability: "100%"
}
\`\`\`

### 2.2 性能指标

| 指标 | 定时器Mock | Socket.IO | 状态 |
|------|------------|-----------|------|
| 连接速度 | 100ms | 变化 | ✅ |
| 稳定性 | 100% | 依赖网络 | ✅ |
| 维护性 | 简单 | 复杂 | ✅ |

## 3. 用户体验

### 3.1 响应性能
定时器Mock提供一致的响应时间，用户体验极佳。

### 3.2 功能完整性
保持了所有预期功能，包括流式输出、表单交互和报告生成。

## 4. 结论
定时器Mock系统是当前最优的解决方案，提供了稳定可靠的Mock功能。`,
    }

    return reports[scenarioName] || reports['report-short']
  }

  // 获取错误数据
  private getErrorData(scenarioName: string): { code: string; message: string } {
    const errors: Record<string, { code: string; message: string }> = {
      'error-network': {
        code: 'MOCK_NETWORK_ERROR',
        message: '模拟的网络连接错误（实际上定时器Mock运行正常）',
      },
      'error-validation': {
        code: 'MOCK_VALIDATION_ERROR',
        message: '模拟的输入数据格式错误（用于测试错误处理）',
      },
    }

    return errors[scenarioName] || errors['error-network']
  }
}

// 导出单例实例
export const timerMockController = new TimerMockController()
