/**
 * ReportPayload处理器
 * 负责Markdown内容解析和渲染准备
 */

import MarkdownIt from 'markdown-it'
import type { ReportMessage, ReportPayload } from '@/types/websocket-event-type'

export interface TableOfContentsItem {
  id: string
  title: string
  level: number
  anchor: string
  children: TableOfContentsItem[]
}

export interface CodeBlock {
  id: string
  language: string
  code: string
  startLine: number
  endLine: number
}

export interface TableData {
  id: string
  headers: string[]
  rows: string[][]
  startLine: number
  endLine: number
}

export interface LinkData {
  id: string
  text: string
  url: string
  title?: string
  isExternal: boolean
}

export interface ParsedReport {
  id: string
  sessionId: string
  rawContent: string
  htmlContent: string
  plainText: string
  wordCount: number
  readingTime: number // 分钟
  tableOfContents: TableOfContentsItem[]
  codeBlocks: CodeBlock[]
  tables: TableData[]
  links: LinkData[]
  images: LinkData[]
  createdAt: Date
  updatedAt: Date
}

export interface ReportEvent {
  type: 'created' | 'updated' | 'parsed' | 'error'
  report: ParsedReport
  error?: string
}

export interface ReportEventListener {
  (event: ReportEvent): void
}

export interface SearchResult {
  reportId: string
  matches: {
    type: 'heading' | 'paragraph' | 'code' | 'table'
    content: string
    context: string
    position: number
  }[]
}

export class ReportProcessor {
  private reports: Map<string, ParsedReport> = new Map()
  private eventListeners: Set<ReportEventListener> = new Set()
  private markdownIt: MarkdownIt

  constructor() {
    // 配置Markdown解析器
    this.markdownIt = new MarkdownIt({
      html: true,
      breaks: true,
      linkify: true,
      typographer: true,
    })

    // 添加插件支持（如果项目中有安装）
    try {
      // 这些插件可能在项目中可用
      const anchor = require('markdown-it-anchor')
      const emoji = require('markdown-it-emoji')
      const taskLists = require('markdown-it-task-lists')

      this.markdownIt.use(anchor, {
        permalink: anchor.permalink.headerLink(),
      })
      this.markdownIt.use(emoji)
      this.markdownIt.use(taskLists)
    } catch (error) {
      // 插件未安装，使用基础功能
      console.warn('Some markdown-it plugins not available')
    }
  }

  // 处理报告消息
  processReportMessage(message: ReportMessage): void {
    try {
      const report = this.parseReportPayload(message.sessionId, message.payload, message.timestamp)
      this.reports.set(report.id, report)

      this.notifyEvent({
        type: 'created',
        report: { ...report },
      })

      // 异步解析内容
      this.parseContent(report)
    } catch (error) {
      console.error('Error processing report message:', error)
      this.notifyEvent({
        type: 'error',
        report: {} as ParsedReport,
        error: error instanceof Error ? error.message : 'Unknown error',
      })
    }
  }

  // 从ReportPayload创建报告
  private parseReportPayload(
    sessionId: string,
    payload: ReportPayload,
    timestamp: string
  ): ParsedReport {
    const reportId = this.generateReportId(sessionId, timestamp)
    const now = new Date()

    return {
      id: reportId,
      sessionId,
      rawContent: payload.content,
      htmlContent: '',
      plainText: '',
      wordCount: 0,
      readingTime: 0,
      tableOfContents: [],
      codeBlocks: [],
      tables: [],
      links: [],
      images: [],
      createdAt: now,
      updatedAt: now,
    }
  }

  // 生成报告ID
  private generateReportId(sessionId: string, timestamp: string): string {
    return `report-${sessionId}-${new Date(timestamp).getTime()}`
  }

  // 解析内容
  private async parseContent(report: ParsedReport): Promise<void> {
    try {
      // 解析HTML
      report.htmlContent = this.markdownIt.render(report.rawContent)

      // 提取纯文本
      report.plainText = this.extractPlainText(report.htmlContent)

      // 计算字数和阅读时间
      report.wordCount = this.countWords(report.plainText)
      report.readingTime = this.calculateReadingTime(report.wordCount)

      // 解析文档结构
      report.tableOfContents = this.extractTableOfContents(report.rawContent)
      report.codeBlocks = this.extractCodeBlocks(report.rawContent)
      report.tables = this.extractTables(report.rawContent)
      report.links = this.extractLinks(report.rawContent)
      report.images = this.extractImages(report.rawContent)

      report.updatedAt = new Date()

      this.notifyEvent({
        type: 'parsed',
        report: { ...report },
      })
    } catch (error) {
      console.error('Error parsing report content:', error)
      this.notifyEvent({
        type: 'error',
        report: { ...report },
        error: error instanceof Error ? error.message : 'Parse error',
      })
    }
  }

  // 提取纯文本
  private extractPlainText(html: string): string {
    // 简单的HTML标签移除
    return html
      .replace(/<[^>]*>/g, '')
      .replace(/\s+/g, ' ')
      .trim()
  }

  // 计算字数
  private countWords(text: string): number {
    // 支持中英文字数统计
    const chineseChars = text.match(/[\u4e00-\u9fa5]/g) || []
    const englishWords = text.match(/[a-zA-Z]+/g) || []
    return chineseChars.length + englishWords.length
  }

  // 计算阅读时间（每分钟200-250字）
  private calculateReadingTime(wordCount: number): number {
    return Math.ceil(wordCount / 225)
  }

  // 提取目录
  private extractTableOfContents(content: string): TableOfContentsItem[] {
    const lines = content.split('\n')
    const toc: TableOfContentsItem[] = []
    const stack: TableOfContentsItem[] = []
    let idCounter = 0

    lines.forEach((line, index) => {
      const match = line.match(/^(#{1,6})\s+(.+)$/)
      if (match) {
        const level = match[1].length
        const title = match[2].trim()
        const id = `heading-${++idCounter}`
        const anchor = this.generateAnchor(title)

        const item: TableOfContentsItem = {
          id,
          title,
          level,
          anchor,
          children: [],
        }

        // 找到正确的父级
        while (stack.length > 0 && stack[stack.length - 1].level >= level) {
          stack.pop()
        }

        if (stack.length === 0) {
          toc.push(item)
        } else {
          stack[stack.length - 1].children.push(item)
        }

        stack.push(item)
      }
    })

    return toc
  }

  // 生成锚点
  private generateAnchor(title: string): string {
    return title
      .toLowerCase()
      .replace(/[^\w\u4e00-\u9fa5\s-]/g, '')
      .replace(/\s+/g, '-')
      .trim()
  }

  // 提取代码块
  private extractCodeBlocks(content: string): CodeBlock[] {
    const blocks: CodeBlock[] = []
    const lines = content.split('\n')
    let inCodeBlock = false
    let currentBlock: Partial<CodeBlock> = {}
    let idCounter = 0

    lines.forEach((line, index) => {
      if (line.startsWith('```')) {
        if (!inCodeBlock) {
          // 开始代码块
          inCodeBlock = true
          currentBlock = {
            id: `code-${++idCounter}`,
            language: line.substring(3).trim() || 'text',
            code: '',
            startLine: index + 1,
          }
        } else {
          // 结束代码块
          inCodeBlock = false
          currentBlock.endLine = index + 1
          if (currentBlock.id && currentBlock.language && currentBlock.startLine) {
            blocks.push(currentBlock as CodeBlock)
          }
          currentBlock = {}
        }
      } else if (inCodeBlock) {
        currentBlock.code = (currentBlock.code || '') + line + '\n'
      }
    })

    return blocks
  }

  // 提取表格
  private extractTables(content: string): TableData[] {
    const tables: TableData[] = []
    const lines = content.split('\n')
    let inTable = false
    let currentTable: Partial<TableData> = {}
    let idCounter = 0

    lines.forEach((line, index) => {
      const isTableRow = line.trim().startsWith('|') && line.trim().endsWith('|')

      if (isTableRow && !inTable) {
        // 开始表格
        inTable = true
        currentTable = {
          id: `table-${++idCounter}`,
          headers: this.parseTableRow(line),
          rows: [],
          startLine: index + 1,
        }
      } else if (isTableRow && inTable) {
        // 表格行
        if (line.includes('---')) {
          // 跳过分隔行
          return
        }
        currentTable.rows?.push(this.parseTableRow(line))
      } else if (!isTableRow && inTable) {
        // 结束表格
        inTable = false
        currentTable.endLine = index
        if (currentTable.id && currentTable.headers && currentTable.rows) {
          tables.push(currentTable as TableData)
        }
        currentTable = {}
      }
    })

    return tables
  }

  // 解析表格行
  private parseTableRow(line: string): string[] {
    return line
      .split('|')
      .slice(1, -1) // 移除首尾空元素
      .map(cell => cell.trim())
  }

  // 提取链接
  private extractLinks(content: string): LinkData[] {
    const links: LinkData[] = []
    const linkRegex = /\[([^\]]+)\]\(([^)]+)(?:\s+"([^"]*)")?\)/g
    let match
    let idCounter = 0

    while ((match = linkRegex.exec(content)) !== null) {
      const [, text, url, title] = match
      links.push({
        id: `link-${++idCounter}`,
        text,
        url,
        title,
        isExternal: this.isExternalUrl(url),
      })
    }

    return links
  }

  // 提取图片
  private extractImages(content: string): LinkData[] {
    const images: LinkData[] = []
    const imageRegex = /!\[([^\]]*)\]\(([^)]+)(?:\s+"([^"]*)")?\)/g
    let match
    let idCounter = 0

    while ((match = imageRegex.exec(content)) !== null) {
      const [, text, url, title] = match
      images.push({
        id: `image-${++idCounter}`,
        text,
        url,
        title,
        isExternal: this.isExternalUrl(url),
      })
    }

    return images
  }

  // 检查是否为外部URL
  private isExternalUrl(url: string): boolean {
    return /^https?:\/\//.test(url)
  }

  // 搜索报告内容
  search(query: string, reportIds?: string[]): SearchResult[] {
    const results: SearchResult[] = []
    const searchReports = reportIds
      ? (reportIds.map(id => this.reports.get(id)).filter(Boolean) as ParsedReport[])
      : Array.from(this.reports.values())

    const normalizedQuery = query.toLowerCase()

    searchReports.forEach(report => {
      const matches: SearchResult['matches'] = []

      // 搜索标题
      report.tableOfContents.forEach(item => {
        if (item.title.toLowerCase().includes(normalizedQuery)) {
          matches.push({
            type: 'heading',
            content: item.title,
            context: item.title,
            position: report.rawContent.indexOf(item.title),
          })
        }
      })

      // 搜索代码块
      report.codeBlocks.forEach(block => {
        if (block.code.toLowerCase().includes(normalizedQuery)) {
          matches.push({
            type: 'code',
            content: block.code,
            context: `${block.language} code block`,
            position: report.rawContent.indexOf(block.code),
          })
        }
      })

      // 搜索纯文本
      const textLines = report.plainText.split('\n')
      textLines.forEach((line, index) => {
        if (line.toLowerCase().includes(normalizedQuery)) {
          matches.push({
            type: 'paragraph',
            content: line,
            context: `Line ${index + 1}`,
            position: report.plainText.indexOf(line),
          })
        }
      })

      if (matches.length > 0) {
        results.push({
          reportId: report.id,
          matches: matches.sort((a, b) => a.position - b.position),
        })
      }
    })

    return results
  }

  // 获取报告
  getReport(reportId: string): ParsedReport | null {
    const report = this.reports.get(reportId)
    return report ? { ...report } : null
  }

  // 获取会话的所有报告
  getSessionReports(sessionId: string): ParsedReport[] {
    const reports: ParsedReport[] = []
    this.reports.forEach(report => {
      if (report.sessionId === sessionId) {
        reports.push({ ...report })
      }
    })
    return reports.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
  }

  // 清理报告
  clearReport(reportId: string): void {
    this.reports.delete(reportId)
  }

  // 清理会话的所有报告
  clearSessionReports(sessionId: string): void {
    const reportIds: string[] = []
    this.reports.forEach((report, id) => {
      if (report.sessionId === sessionId) {
        reportIds.push(id)
      }
    })
    reportIds.forEach(id => this.clearReport(id))
  }

  // 清理过期报告
  cleanupOldReports(maxAge: number = 24 * 60 * 60 * 1000): void {
    // 24小时
    const now = new Date()
    const expiredReports: string[] = []

    this.reports.forEach((report, id) => {
      const age = now.getTime() - report.updatedAt.getTime()
      if (age > maxAge) {
        expiredReports.push(id)
      }
    })

    expiredReports.forEach(id => this.clearReport(id))
  }

  // 事件监听器管理
  addEventListener(listener: ReportEventListener): void {
    this.eventListeners.add(listener)
  }

  removeEventListener(listener: ReportEventListener): void {
    this.eventListeners.delete(listener)
  }

  // 清理所有监听器
  clearAllListeners(): void {
    this.eventListeners.clear()
  }

  // 获取统计信息
  getStats(): {
    totalReports: number
    totalWords: number
    averageReadingTime: number
    totalCodeBlocks: number
    totalTables: number
    totalLinks: number
    totalImages: number
  } {
    let totalWords = 0
    let totalReadingTime = 0
    let totalCodeBlocks = 0
    let totalTables = 0
    let totalLinks = 0
    let totalImages = 0

    this.reports.forEach(report => {
      totalWords += report.wordCount
      totalReadingTime += report.readingTime
      totalCodeBlocks += report.codeBlocks.length
      totalTables += report.tables.length
      totalLinks += report.links.length
      totalImages += report.images.length
    })

    return {
      totalReports: this.reports.size,
      totalWords,
      averageReadingTime: this.reports.size > 0 ? totalReadingTime / this.reports.size : 0,
      totalCodeBlocks,
      totalTables,
      totalLinks,
      totalImages,
    }
  }

  // 私有方法：通知事件
  private notifyEvent(event: ReportEvent): void {
    this.eventListeners.forEach(listener => {
      try {
        listener(event)
      } catch (error) {
        console.error('Error in report event listener:', error)
      }
    })
  }
}
