'use client'

import * as Sentry from '@sentry/nextjs'
import { useEffect } from 'react'

/**
 * @description
 * This is a placeholder for your authentication store.
 * Replace this with your actual authentication hook or state management logic.
 *
 * @example
 * import { useAuth } from '@/lib/stores/auth-store'
 * const { user, isAuthenticated } = useAuth()
 */
const useAuthPlaceholder = () => {
  // Replace this with your actual user data
  const isAuthenticated = false // Example: check if user is logged in
  const user = isAuthenticated
    ? { id: '123', email: '<EMAIL>', username: 'testuser' }
    : null

  return { user, isAuthenticated }
}

/**
 * @description
 * This component sets the Sentry user context based on the authentication state.
 * It should be placed in a layout component that wraps the pages requiring user context.
 */
export function SentryUserProvider({ children }: { children: React.ReactNode }) {
  const { user, isAuthenticated } = useAuthPlaceholder()

  useEffect(() => {
    if (isAuthenticated && user) {
      Sentry.setUser({
        id: user.id,
        email: user.email,
        username: user.username,
      })
    } else {
      Sentry.setUser(null)
    }
  }, [isAuthenticated, user])

  return <>{children}</>
}
