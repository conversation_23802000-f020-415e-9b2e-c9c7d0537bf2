apiVersion: v1
kind: ConfigMap
metadata:
  name: specific-ai-ui-config
  namespace: ovs
  labels:
    app: specific-ai-ui
    app.kubernetes.io/name: specific-ai-ui
    app.kubernetes.io/component: service
    app.kubernetes.io/version: '1.0.0'
    app.kubernetes.io/managed-by: 'kubectl'
    environment: dev
data:
  # 服务器基础配置
  PORT: '8230'
  HOST: '0.0.0.0'
  NODE_ENV: 'development'

  # CORS 和安全配置
  CORS_ORIGIN: '*'
  ALLOWED_EMBED_DOMAINS: 'https://dev.goglobalsp.com'
  EMBED_SECURITY_ENABLED: 'true'

  # Next.js 公共配置
  NEXT_PUBLIC_API_BASE_URL: 'https://dev.goglobalsp.com'
  NEXT_PUBLIC_WS_URL: 'wss://dev.goglobalsp.com/api/v1/aftercure/start'
  NEXT_PUBLIC_APP_ENV: 'dev'
  NEXT_PUBLIC_DEBUG: 'true'
  NEXT_PUBLIC_EMBED_ENABLED: 'true'

  # 业务服务配置
  BUSINESS_BASE_API: 'http://***************:8000'
  NEXT_AUTH_BACKEND_ORIGIN: 'http://***************:10086'

  # Next.js 优化配置
  NEXT_TELEMETRY_DISABLED: '1'

  # 开发环境特有配置
  DEBUG_MODE: 'true'
  LOG_LEVEL: 'debug'