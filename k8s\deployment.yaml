apiVersion: apps/v1
kind: Deployment
metadata:
  name: specific-ai-ui
  namespace: ovs
  labels:
    app: specific-ai-ui
    app.kubernetes.io/name: specific-ai-ui
    app.kubernetes.io/component: service
    app.kubernetes.io/version: '1.0.0'
spec:
  replicas: 1
  selector:
    matchLabels:
      app: specific-ai-ui
  template:
    metadata:
      labels:
        app: specific-ai-ui
        app.kubernetes.io/name: specific-ai-ui
        app.kubernetes.io/component: service
    spec:
      containers:
        - name: specific-ai-ui
          image: **************/specific-ai/ui:latest # 镜像地址匹配build_push_image.sh脚本
          imagePullPolicy: Always # 确保获取最新镜像
          ports:
            - containerPort: 8230
              name: http
              protocol: TCP
          env:
            # 从ConfigMap加载服务器配置
            - name: PORT
              valueFrom:
                configMapKeyRef:
                  name: specific-ai-ui-config
                  key: PORT
            - name: HOST
              valueFrom:
                configMapKeyRef:
                  name: specific-ai-ui-config
                  key: HOST
            - name: NODE_ENV
              valueFrom:
                configMapKeyRef:
                  name: specific-ai-ui-config
                  key: NODE_ENV
            - name: CORS_ORIGIN
              valueFrom:
                configMapKeyRef:
                  name: specific-ai-ui-config
                  key: CORS_ORIGIN
            - name: BUSINESS_BASE_API
              valueFrom:
                configMapKeyRef:
                  name: specific-ai-ui-config
                  key: BUSINESS_BASE_API
            - name: NEXT_AUTH_BACKEND_ORIGIN
              valueFrom:
                configMapKeyRef:
                  name: specific-ai-ui-config
                  key: NEXT_AUTH_BACKEND_ORIGIN
            # 从ConfigMap加载Next.js公共配置
            - name: NEXT_PUBLIC_API_BASE_URL
              valueFrom:
                configMapKeyRef:
                  name: specific-ai-ui-config
                  key: NEXT_PUBLIC_API_BASE_URL
            - name: NEXT_PUBLIC_WS_URL
              valueFrom:
                configMapKeyRef:
                  name: specific-ai-ui-config
                  key: NEXT_PUBLIC_WS_URL
            - name: ALLOWED_EMBED_DOMAINS
              valueFrom:
                configMapKeyRef:
                  name: specific-ai-ui-config
                  key: ALLOWED_EMBED_DOMAINS
            - name: EMBED_SECURITY_ENABLED
              valueFrom:
                configMapKeyRef:
                  name: specific-ai-ui-config
                  key: EMBED_SECURITY_ENABLED
            - name: NEXT_PUBLIC_APP_ENV
              valueFrom:
                configMapKeyRef:
                  name: specific-ai-ui-config
                  key: NEXT_PUBLIC_APP_ENV
            - name: NEXT_PUBLIC_DEBUG
              valueFrom:
                configMapKeyRef:
                  name: specific-ai-ui-config
                  key: NEXT_PUBLIC_DEBUG
            - name: NEXT_PUBLIC_EMBED_ENABLED
              valueFrom:
                configMapKeyRef:
                  name: specific-ai-ui-config
                  key: NEXT_PUBLIC_EMBED_ENABLED

            # 从 Secret 加载敏感配置
            - name: DATABASE_URL
              valueFrom:
                secretKeyRef:
                  name: specific-ai-ui-secret
                  key: DATABASE_URL
            - name: BETTER_AUTH_SECRET
              valueFrom:
                secretKeyRef:
                  name: specific-ai-ui-secret
                  key: BETTER_AUTH_SECRET
            - name: RESEND_API_KEY
              valueFrom:
                secretKeyRef:
                  name: specific-ai-ui-secret
                  key: RESEND_API_KEY
            - name: GOOGLE_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: specific-ai-ui-secret
                  key: GOOGLE_CLIENT_SECRET

          # 健康检查 - 优化延迟和频率
          livenessProbe:
            httpGet:
              path: /api/health
              port: 8230
              scheme: HTTP
            initialDelaySeconds: 20 # 减少初始延迟
            periodSeconds: 20 # 减少检查频率
            timeoutSeconds: 8
            failureThreshold: 3
            successThreshold: 1

          readinessProbe:
            httpGet:
              path: /api/health
              port: 8230
              scheme: HTTP
            initialDelaySeconds: 5 # 更快的就绪检查
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
            successThreshold: 1

          # 启动探针 - 为慢启动提供更多时间
          startupProbe:
            httpGet:
              path: /api/health
              port: 8230
              scheme: HTTP
            initialDelaySeconds: 10
            periodSeconds: 5
            timeoutSeconds: 5
            failureThreshold: 12 # 允许最多 60 秒启动时间
            successThreshold: 1

          # 安全上下文
          securityContext:
            allowPrivilegeEscalation: false
            runAsNonRoot: true
            runAsUser: 65532 # 与 distroless nonroot 镜像匹配
            runAsGroup: 65532
            capabilities:
              drop:
                - ALL
            readOnlyRootFilesystem: false # Next.js 需要写入权限
            seccompProfile:
              type: RuntimeDefault

          # 资源限制 - 针对优化后的镜像调整
          resources:
            limits:
              cpu: 500m
              memory: 384Mi # 减少内存限制，优化后的镜像更节省内存
              ephemeral-storage: 1Gi # 限制临时存储
            requests:
              cpu: 200m # 稍微减少 CPU 请求
              memory: 192Mi # 减少内存请求
              ephemeral-storage: 256Mi

      # 重启策略
      restartPolicy: Always

      # DNS策略
      dnsPolicy: ClusterFirst
