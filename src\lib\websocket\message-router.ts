/**
 * 消息路由分发器
 * 基于四种Payload类型的消息路由：streaming, checkpoint, report, error
 */

import type {
  BaseWebSocketMessage,
  StreamingMessage,
  CheckpointMessage,
  ReportMessage,
  ErrorMessage,
} from '@/types/websocket-event-type'

import {
  isStreamingPayload,
  isCheckpointPayload,
  isReportPayload,
  isErrorPayload,
} from '@/types/websocket-event-type'

export interface StreamingHandler {
  (message: StreamingMessage): void
}

export interface CheckpointHandler {
  (message: CheckpointMessage): void
}

export interface ReportHandler {
  (message: ReportMessage): void
}

export interface ErrorHandler {
  (message: ErrorMessage): void
}

export interface UnknownHandler {
  (message: BaseWebSocketMessage): void
}

export class MessageRouter {
  private streamingHandlers: Set<StreamingHandler> = new Set()
  private checkpointHandlers: Set<CheckpointHandler> = new Set()
  private reportHandlers: Set<ReportHandler> = new Set()
  private errorHandlers: Set<ErrorHandler> = new Set()
  private unknownHandlers: Set<UnknownHandler> = new Set()

  // 路由消息到对应的处理器
  routeMessage(message: BaseWebSocketMessage): void {
    try {
      if (isStreamingPayload(message.payload)) {
        this.dispatchStreaming(message as StreamingMessage)
      } else if (isCheckpointPayload(message.payload)) {
        this.dispatchCheckpoint(message as CheckpointMessage)
      } else if (isReportPayload(message.payload)) {
        this.dispatchReport(message as ReportMessage)
      } else if (isErrorPayload(message.payload)) {
        this.dispatchError(message as ErrorMessage)
      } else {
        this.dispatchUnknown(message)
      }
    } catch (error) {
      console.error('Error routing message:', error, message)
      // 当作错误消息处理
      this.dispatchUnknown(message)
    }
  }

  // === Streaming 消息处理 ===
  addStreamingHandler(handler: StreamingHandler): void {
    this.streamingHandlers.add(handler)
  }

  removeStreamingHandler(handler: StreamingHandler): void {
    this.streamingHandlers.delete(handler)
  }

  private dispatchStreaming(message: StreamingMessage): void {
    this.streamingHandlers.forEach(handler => {
      try {
        handler(message)
      } catch (error) {
        console.error('Error in streaming handler:', error)
      }
    })
  }

  // === Checkpoint 消息处理 ===
  addCheckpointHandler(handler: CheckpointHandler): void {
    this.checkpointHandlers.add(handler)
  }

  removeCheckpointHandler(handler: CheckpointHandler): void {
    this.checkpointHandlers.delete(handler)
  }

  private dispatchCheckpoint(message: CheckpointMessage): void {
    this.checkpointHandlers.forEach(handler => {
      try {
        handler(message)
      } catch (error) {
        console.error('Error in checkpoint handler:', error)
      }
    })
  }

  // === Report 消息处理 ===
  addReportHandler(handler: ReportHandler): void {
    this.reportHandlers.add(handler)
  }

  removeReportHandler(handler: ReportHandler): void {
    this.reportHandlers.delete(handler)
  }

  private dispatchReport(message: ReportMessage): void {
    this.reportHandlers.forEach(handler => {
      try {
        handler(message)
      } catch (error) {
        console.error('Error in report handler:', error)
      }
    })
  }

  // === Error 消息处理 ===
  addErrorHandler(handler: ErrorHandler): void {
    this.errorHandlers.add(handler)
  }

  removeErrorHandler(handler: ErrorHandler): void {
    this.errorHandlers.delete(handler)
  }

  private dispatchError(message: ErrorMessage): void {
    this.errorHandlers.forEach(handler => {
      try {
        handler(message)
      } catch (error) {
        console.error('Error in error handler:', error)
      }
    })
  }

  // === 未知消息处理 ===
  addUnknownHandler(handler: UnknownHandler): void {
    this.unknownHandlers.add(handler)
  }

  removeUnknownHandler(handler: UnknownHandler): void {
    this.unknownHandlers.delete(handler)
  }

  private dispatchUnknown(message: BaseWebSocketMessage): void {
    console.warn('Unknown message type received:', message.payload.type, message)
    this.unknownHandlers.forEach(handler => {
      try {
        handler(message)
      } catch (error) {
        console.error('Error in unknown handler:', error)
      }
    })
  }

  // 清理所有处理器
  clearAllHandlers(): void {
    this.streamingHandlers.clear()
    this.checkpointHandlers.clear()
    this.reportHandlers.clear()
    this.errorHandlers.clear()
    this.unknownHandlers.clear()
  }

  // 获取处理器统计信息
  getHandlerStats(): {
    streaming: number
    checkpoint: number
    report: number
    error: number
    unknown: number
  } {
    return {
      streaming: this.streamingHandlers.size,
      checkpoint: this.checkpointHandlers.size,
      report: this.reportHandlers.size,
      error: this.errorHandlers.size,
      unknown: this.unknownHandlers.size,
    }
  }
}
