/**
 * useHttp Hook - React-friendly HTTP client for Better Auth
 *
 * 功能：基于 request.ts 构建的 React Hook，提供易用的 HTTP 请求接口
 * 特性：加载状态、错误处理、成功状态、Better Auth cookie 认证兼容
 *
 * 使用示例：
 * ```tsx
 * const { get, post, loading, error, data } = useHttp()
 *
 * // GET 请求
 * const fetchData = async () => {
 *   const result = await get<UserData>('/users/profile')
 *   if (result.success) {
 *     console.log(result.data)
 *   }
 * }
 *
 * // POST 请求
 * const createUser = async (userData: CreateUserData) => {
 *   const result = await post<User>('/users', userData)
 *   return result
 * }
 * ```
 */

import { useState, useCallback, useRef } from 'react'
import { AxiosRequestConfig, AxiosResponse } from 'axios'
import { request, type ApiResponse } from '@/lib/request'
import { useErrorHandler } from './useErrorHandler'
import { ErrorHandlerConfig } from '@/lib/error-handler'

// HTTP 方法类型
type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE'

// 请求状态接口
interface RequestState {
  loading: boolean
  error: Error | null
  data: any
}

// Hook 返回类型
interface UseHttpReturn {
  // 状态
  loading: boolean
  error: Error | null
  data: any

  // HTTP 方法
  get: <T = any>(url: string, config?: AxiosRequestConfig) => Promise<HttpResult<T>>
  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) => Promise<HttpResult<T>>
  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) => Promise<HttpResult<T>>
  del: <T = any>(url: string, config?: AxiosRequestConfig) => Promise<HttpResult<T>>

  // 工具方法
  reset: () => void
  setLoading: (loading: boolean) => void
}

// HTTP 请求结果类型
interface HttpResult<T = any> {
  success: boolean
  data?: T
  error?: Error
  response?: AxiosResponse<ApiResponse<T>>
}

// 请求选项
interface RequestOptions {
  showLoading?: boolean // 是否显示加载状态
  resetOnRequest?: boolean // 请求前是否重置状态
  errorHandling?: ErrorHandlerConfig // 错误处理配置
  enableAutoErrorToast?: boolean // 是否启用自动错误 toast（默认 true）
  silentError?: boolean // 是否静默处理错误
}

/**
 * useHttp Hook
 *
 * @param defaultOptions 默认选项
 * @returns HTTP 客户端接口
 */
export function useHttp(defaultOptions: RequestOptions = {}): UseHttpReturn {
  const {
    showLoading = true,
    resetOnRequest = true,
    enableAutoErrorToast = true,
    silentError = false,
    errorHandling = {},
  } = defaultOptions

  // 集成错误处理 Hook
  const { handleError, handleErrorSilently } = useErrorHandler({
    showToast: enableAutoErrorToast && !silentError,
    maintainErrorState: false, // useHttp 自己管理错误状态
    ...errorHandling,
  })

  // 状态管理
  const [state, setState] = useState<RequestState>({
    loading: false,
    error: null,
    data: null,
  })

  // 使用 ref 来避免闭包问题
  const stateRef = useRef(state)
  stateRef.current = state

  // 更新状态的辅助函数
  const updateState = useCallback((updates: Partial<RequestState>) => {
    setState(prev => ({ ...prev, ...updates }))
  }, [])

  // 重置状态
  const reset = useCallback(() => {
    setState({
      loading: false,
      error: null,
      data: null,
    })
  }, [])

  // 设置加载状态
  const setLoading = useCallback(
    (loading: boolean) => {
      updateState({ loading })
    },
    [updateState]
  )

  // 通用请求处理函数
  const makeRequest = useCallback(
    async <T = any>(
      method: HttpMethod,
      url: string,
      data?: any,
      config?: AxiosRequestConfig,
      options: RequestOptions = {}
    ): Promise<HttpResult<T>> => {
      const requestOptions = { ...defaultOptions, ...options }

      try {
        // 请求前重置状态
        if (requestOptions.resetOnRequest) {
          updateState({ error: null, data: null })
        }

        // 显示加载状态
        if (requestOptions.showLoading) {
          updateState({ loading: true })
        }

        let response: AxiosResponse<ApiResponse<T>>

        // 根据方法执行请求
        switch (method) {
          case 'GET':
            response = await request.get<ApiResponse<T>>(url, config)
            break
          case 'POST':
            response = await request.post<ApiResponse<T>>(url, data, config)
            break
          case 'PUT':
            response = await request.put<ApiResponse<T>>(url, data, config)
            break
          case 'DELETE':
            response = await request.delete<ApiResponse<T>>(url, config)
            break
          default:
            throw new Error(`Unsupported HTTP method: ${method}`)
        }

        // 处理响应
        const responseData = response.data

        // 检查是否是 ApiResponse 格式
        if (responseData && typeof responseData === 'object' && 'success' in responseData) {
          if (responseData.success) {
            // API 成功响应
            updateState({
              loading: false,
              data: responseData.data,
              error: null,
            })

            return {
              success: true,
              data: responseData.data,
              response,
            }
          } else {
            // API 错误响应
            const error = new Error(responseData.message || 'API request failed')
            ;(error as any).code = responseData.code
            ;(error as any).response = responseData
            ;(error as any).isApiError = true

            updateState({
              loading: false,
              error,
              data: null,
            })

            return {
              success: false,
              error,
              response,
            }
          }
        } else {
          // 非标准 ApiResponse 格式，直接返回数据
          updateState({
            loading: false,
            data: responseData,
            error: null,
          })

          return {
            success: true,
            data: responseData as T,
            response,
          }
        }
      } catch (error) {
        const httpError = error as Error

        // 使用统一错误处理系统
        const processedError = silentError
          ? handleErrorSilently(httpError)
          : handleError(httpError, {
              showToast: enableAutoErrorToast,
              ...errorHandling,
            })

        updateState({
          loading: false,
          error: httpError,
          data: null,
        })

        return {
          success: false,
          error: httpError,
        }
      }
    },
    [
      defaultOptions,
      updateState,
      handleError,
      handleErrorSilently,
      silentError,
      enableAutoErrorToast,
      errorHandling,
    ]
  )

  // HTTP 方法实现
  const get = useCallback(
    <T = any>(url: string, config?: AxiosRequestConfig) =>
      makeRequest<T>('GET', url, undefined, config),
    [makeRequest]
  )

  const post = useCallback(
    <T = any>(url: string, data?: any, config?: AxiosRequestConfig) =>
      makeRequest<T>('POST', url, data, config),
    [makeRequest]
  )

  const put = useCallback(
    <T = any>(url: string, data?: any, config?: AxiosRequestConfig) =>
      makeRequest<T>('PUT', url, data, config),
    [makeRequest]
  )

  const del = useCallback(
    <T = any>(url: string, config?: AxiosRequestConfig) =>
      makeRequest<T>('DELETE', url, undefined, config),
    [makeRequest]
  )

  return {
    // 状态
    loading: state.loading,
    error: state.error,
    data: state.data,

    // HTTP 方法
    get,
    post,
    put,
    del,

    // 工具方法
    reset,
    setLoading,
  }
}

// 便利的单次请求 hooks
export function useGet<T = any>(url?: string, config?: AxiosRequestConfig) {
  const http = useHttp()

  const execute = useCallback(
    async (requestUrl?: string) => {
      const targetUrl = requestUrl || url
      if (!targetUrl) {
        throw new Error('URL is required for GET request')
      }
      return http.get<T>(targetUrl, config)
    },
    [http, url, config]
  )

  return {
    ...http,
    execute,
  }
}

export function usePost<T = any>() {
  const http = useHttp()

  const execute = useCallback(
    async (url: string, data?: any, config?: AxiosRequestConfig) => {
      return http.post<T>(url, data, config)
    },
    [http]
  )

  return {
    ...http,
    execute,
  }
}

/**
 * 专用的静默 HTTP Hook
 * 不显示错误 toast，适用于轮询等场景
 */
export function useSilentHttp() {
  return useHttp({
    enableAutoErrorToast: false,
    silentError: true,
  })
}

/**
 * 专用的关键业务 HTTP Hook
 * 错误会传播到错误边界
 */
export function useCriticalHttp() {
  return useHttp({
    enableAutoErrorToast: true,
    errorHandling: {
      propagateToErrorBoundary: true,
      logError: true,
    },
  })
}

/**
 * 专用的后台任务 HTTP Hook
 * 适用于不需要用户立即关注的请求
 */
export function useBackgroundHttp() {
  return useHttp({
    showLoading: false,
    enableAutoErrorToast: false,
    silentError: true,
  })
}

export default useHttp
