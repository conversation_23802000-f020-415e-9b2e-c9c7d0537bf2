/**
 * Mock Chat API 端点
 * 提供流式响应模拟真实的 AI 对话
 */

import { NextRequest, NextResponse } from 'next/server'

export const runtime = 'edge'

// Mock 响应数据
const mockResponses = [
  '我来帮助您分析这个问题。',
  '根据您提供的信息，我建议以下几点：\n\n1. 首先需要明确需求范围\n2. 评估技术可行性\n3. 制定实施计划',
  '这是一个很好的想法！让我为您详细分析一下可能的实现方案。',
  '基于当前的技术栈，我推荐使用以下方法：\n\n• 使用 Next.js 14 的 App Router\n• 集成 shadcn/ui 组件库\n• 实现响应式设计\n\n这样可以确保最佳的用户体验。',
  '我正在为您生成详细的技术文档，请稍候...',
]

// 模拟生成报告的响应
const reportResponse = `# WebSocket 实时数据分析报告

## 概述
基于您的需求，我为您设计了一个完整的 WebSocket 实时数据监控解决方案。

## 技术架构
- **前端框架**: Next.js 14 + TypeScript
- **UI 组件**: shadcn/ui + Tailwind CSS
- **状态管理**: Zustand
- **实时通信**: WebSocket + Server-Sent Events

## 核心功能
1. **实时数据展示**: 类似聊天界面的消息流
2. **状态监控**: 连接状态和数据处理状态
3. **数据过滤**: 支持按类型筛选消息
4. **导出功能**: 支持多种格式导出

## 实现细节
### 组件结构
\`\`\`typescript
interface MessageData {
  id: string
  type: 'info' | 'warning' | 'error' | 'success'
  timestamp: Date
  content: string
  metadata?: any
}
\`\`\`

### 性能优化
- 使用虚拟滚动处理大量数据
- 实现消息缓存机制
- 支持懒加载和分页

这个方案能够满足您的所有需求，并且具有良好的扩展性。`

export async function POST(req: NextRequest) {
  try {
    const body = await req.json()
    const { messages } = body

    // 获取最后一条用户消息
    const lastUserMessage = messages?.filter((m: any) => m.role === 'user')?.pop()
    const userContent = lastUserMessage?.content || ''

    // 根据用户输入选择合适的 mock 响应
    let mockResponse = mockResponses[Math.floor(Math.random() * mockResponses.length)]

    // 如果用户询问报告相关内容，返回报告
    if (
      userContent.includes('报告') ||
      userContent.includes('文档') ||
      userContent.includes('分析')
    ) {
      mockResponse = reportResponse
    }

    // 如果用户询问代码相关内容，返回代码示例
    if (
      userContent.includes('代码') ||
      userContent.includes('实现') ||
      userContent.includes('组件')
    ) {
      mockResponse = `这是一个 React 组件的实现示例：

\`\`\`typescript
import { useState, useEffect } from 'react'
import { Card, CardContent } from '@/components/ui/card'

export function DataViewer() {
  const [data, setData] = useState([])
  const [isLoading, setIsLoading] = useState(false)
  
  useEffect(() => {
    // 模拟数据加载
    setIsLoading(true)
    setTimeout(() => {
      setData([
        { id: 1, title: '数据项 1', status: 'active' },
        { id: 2, title: '数据项 2', status: 'inactive' },
      ])
      setIsLoading(false)
    }, 1000)
  }, [])
  
  if (isLoading) {
    return <div>加载中...</div>
  }
  
  return (
    <div className="space-y-4">
      {data.map(item => (
        <Card key={item.id}>
          <CardContent className="p-4">
            <h3 className="font-semibold">{item.title}</h3>
            <p className="text-sm text-gray-500">状态: {item.status}</p>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
\`\`\`

这个组件展示了如何使用 shadcn/ui 组件来构建数据展示界面。`
    }

    // 创建流式响应
    const encoder = new TextEncoder()
    const stream = new ReadableStream({
      start(controller) {
        // 模拟打字机效果
        let index = 0
        const interval = setInterval(() => {
          if (index < mockResponse.length) {
            const char = mockResponse[index]
            controller.enqueue(encoder.encode(char))
            index++
          } else {
            clearInterval(interval)
            controller.close()
          }
        }, 30) // 每30ms输出一个字符
      },
    })

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Cache-Control': 'no-cache',
        Connection: 'keep-alive',
      },
    })
  } catch (error) {
    console.error('Mock API 错误:', error)

    return NextResponse.json({ error: '模拟 API 发生错误' }, { status: 500 })
  }
}

// 支持 OPTIONS 请求 (CORS)
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}
