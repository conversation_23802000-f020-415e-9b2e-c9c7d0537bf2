import type { Metada<PERSON> } from 'next'
import { NextIntlClientProvider } from 'next-intl'
import { getMessages } from 'next-intl/server'
import { SentryUserProvider } from '@/components/providers/sentry-user-provider'
import { QueryProvider } from '@/components/providers/query-provider'
import './globals.css'
import { SessionProvider } from '@/components/providers/session-provider'
import { Toaster } from 'sonner'
import Script from 'next/script'

export const metadata: Metadata = {
  title: 'SpecificAI',
  description: 'SpecificAI is a platform for AI-powered content creation and analysis.',
  keywords: ['AI', 'Content Creation', 'Analysis'],
  authors: [{ name: 'SpecificAI' }],
  icons: {
    icon: [
      { url: '/icon', sizes: '32x32', type: 'image/png' },
      { url: '/logo.svg', sizes: 'any', type: 'image/svg+xml' },
    ],
    apple: { url: '/icon', sizes: '180x180', type: 'image/png' },
    shortcut: '/logo.svg',
  },
}

export const viewport = {
  width: 'device-width',
  initialScale: 1,
}

export default async function RootLayout({ children }: { children: React.ReactNode }) {
  // 获取当前语言的消息
  const messages = await getMessages()

  return (
    <html lang="zh" suppressHydrationWarning className="w-full h-full">
      <head>
        {/* Define isSpace function globally to fix markdown-it issues with Next.js + Turbopack */}
        <Script id="markdown-it-fix" strategy="beforeInteractive">
          {`
            if (typeof window !== 'undefined' && typeof window.isSpace === 'undefined') {
              window.isSpace = function(code) {
                return code === 0x20 || code === 0x09 || code === 0x0A || code === 0x0B || code === 0x0C || code === 0x0D;
              };
            }
          `}
        </Script>
      </head>
      <body className={`font-sans antialiased w-full h-full m-0 p-0 box-border overflow-hidden`}>
        <NextIntlClientProvider messages={messages}>
          <SessionProvider>
            <SentryUserProvider>
              <QueryProvider>
                {children}
                <Toaster position="top-right" richColors />
              </QueryProvider>
            </SentryUserProvider>
          </SessionProvider>
        </NextIntlClientProvider>
      </body>
    </html>
  )
}
