'use client'

import { useFrontendLanguage } from '@/hooks/use-frontend-language'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { ChevronDown, Loader2 } from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

interface FrontendLanguageSwitchProps {
  /**
   * 自定义类名
   */
  className?: string
  /**
   * 按钮变体
   */
  variant?: 'default' | 'outline' | 'ghost' | 'secondary'
  /**
   * 按钮大小
   */
  size?: 'default' | 'sm' | 'lg'
}

/**
 * 纯前端语言切换组件
 * 不依赖用户认证状态，通过设置cookie和路由刷新实现语言切换
 * 适用于登录、注册等未认证页面，不刷新页面，即时生效
 */
export function FrontendLanguageSwitch({
  className,
  variant = 'outline',
  size = 'default',
}: FrontendLanguageSwitchProps) {
  const { currentLocale, availableLanguages, isChanging, changeLanguage } = useFrontendLanguage()

  const handleLanguageChange = (locale: string) => {
    changeLanguage(locale as any)
  }

  const currentLangDetails = availableLanguages.find(lang => lang.value === currentLocale)

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant={variant}
          size={size}
          className={cn('flex items-center justify-center gap-2 min-w-[120px]', className)}
          disabled={isChanging}
        >
          {isChanging ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            currentLangDetails && (
              <>
                {/* <span className="text-lg" role="img" aria-label={currentLangDetails.label}>
                  {currentLangDetails.icon}
                </span> */}
                <span className="text-sm font-medium">{currentLangDetails.label}</span>
                <ChevronDown className="h-4 w-4 text-muted-foreground" />
              </>
            )
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-40">
        {availableLanguages.map(lang => (
          <DropdownMenuItem
            key={lang.value}
            onSelect={() => handleLanguageChange(lang.value)}
            disabled={isChanging}
            className={cn('flex cursor-pointer items-center gap-2', {
              'bg-accent text-accent-foreground': currentLocale === lang.value,
            })}
          >
            <span className="text-lg" role="img" aria-label={lang.label}>
              {lang.icon}
            </span>
            <span className="text-sm">{lang.label}</span>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
