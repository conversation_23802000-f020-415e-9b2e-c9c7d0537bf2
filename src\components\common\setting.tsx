"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { useSession } from "@/components/providers/session-provider"

const sidebarItems = [
  { id: "userinfo", label: "User Info" },
  { id: "organization", label: "Organization" },
]

export default function SettingsPage() {
  const [activeSection, setActiveSection] = useState("userinfo")
  const { user, organization, loading, error } = useSession()
  console.log('user', user)
  console.log('organization', organization)
  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="mx-auto max-w-6xl">
        <div className="rounded-lg bg-white p-6 shadow-sm">
          {/* Header */}
          <div className="mb-6">
            <div className="flex items-center gap-3 mb-2">
              <h1 className="text-3xl font-bold">Settings</h1>
              <Badge variant="secondary" className="bg-purple-600 text-white hover:bg-purple-700">
                Dev
              </Badge>
            </div>
            <p className="text-gray-600">Manage your account settings and set e-mail preferences.</p>
          </div>

          <div className="flex gap-8">
            {/* Sidebar */}
            <div className="w-48 flex-shrink-0">
              <nav className="space-y-1">
                {sidebarItems.map((item) => (
                  <button
                    key={item.id}
                    onClick={() => setActiveSection(item.id)}
                    className={`w-full text-left px-3 py-2 text-sm rounded-md transition-colors ${
                      activeSection === item.id
                        ? "bg-gray-100 text-gray-900 font-medium"
                        : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                    }`}
                  >
                    {item.label}
                  </button>
                ))}
              </nav>
            </div>

            {/* Main Content */}
            <div className="flex-1">
              <ScrollArea className="h-[calc(100vh-250px)] pr-4">
                {loading && (
                  <div className="flex items-center justify-center py-8">
                    <div className="text-gray-500">Loading...</div>
                  </div>
                )}

                {error && (
                  <div className="flex items-center justify-center py-8">
                    <div className="text-red-500">Error: {error}</div>
                  </div>
                )}

                {!loading && !error && (
                <>
                  {/* User Info Section */}
                  {activeSection === "userinfo" && (
                    <div className="space-y-6">
                      <div>
                        <h2 className="text-xl font-semibold mb-1">User Info</h2>
                        <p className="text-sm text-gray-600">Your personal account information and settings.</p>
                      </div>

                      <div className="space-y-4">
                        {/* User Name */}
                        <div className="space-y-2">
                          <Label htmlFor="name">Name</Label>
                          <Input id="name" value={user?.name || ''} readOnly className="max-w-md bg-gray-50" />
                          <p className="text-xs text-gray-500">Your display name as shown to other users.</p>
                        </div>

                        {/* Email */}
                        <div className="space-y-2">
                          <Label htmlFor="email">Email</Label>
                          <div className="flex items-center gap-2">
                            <Input id="email" value={user?.email || ''} readOnly className="max-w-md bg-gray-50" />
                            {user?.emailVerified && (
                              <Badge variant="secondary" className="bg-green-100 text-green-800">
                                Verified
                              </Badge>
                            )}
                          </div>
                          <p className="text-xs text-gray-500">
                            Your email address. {user?.emailVerified ? 'Verified' : 'Not verified'}.
                          </p>
                        </div>

                        {/* Company Name */}
                        <div className="space-y-2">
                          <Label htmlFor="company">Company Name</Label>
                          <Input id="company" value={user?.companyName || ''} readOnly className="max-w-md bg-gray-50" />
                          <p className="text-xs text-gray-500">Your associated company or organization.</p>
                        </div>

                        {/* Language */}
                        <div className="space-y-2">
                          <Label htmlFor="language">Language</Label>
                          <Input id="language" value={user?.language || ''} readOnly className="max-w-md bg-gray-50" />
                          <p className="text-xs text-gray-500">Your preferred language setting.</p>
                        </div>

                        {/* Role */}
                        <div className="space-y-2">
                          <Label htmlFor="role">Role</Label>
                          <div className="flex items-center gap-2">
                            <Input id="role" value={user?.role || ''} readOnly className="max-w-md bg-gray-50" />
                            <Badge variant="outline" className="capitalize">
                              {user?.role}
                            </Badge>
                          </div>
                          <p className="text-xs text-gray-500">Your account role and permissions level.</p>
                        </div>

                        {/* Account Dates */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="created">Account Created</Label>
                            <Input 
                              id="created" 
                              value={user?.createdAt ? new Date(user.createdAt).toLocaleDateString() : ''} 
                              readOnly 
                              className="bg-gray-50" 
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="updated">Last Updated</Label>
                            <Input 
                              id="updated" 
                              value={user?.updatedAt ? new Date(user.updatedAt).toLocaleDateString() : ''} 
                              readOnly 
                              className="bg-gray-50" 
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Organization Section */}
                  {activeSection === "organization" && (
                    <div className="space-y-6">
                      <div>
                        <h2 className="text-xl font-semibold mb-1">Organization</h2>
                        <p className="text-sm text-gray-600">Your organization details and subscription information.</p>
                      </div>

                      <div className="space-y-4">
                        {/* Organization Name */}
                        <div className="space-y-2">
                          <Label htmlFor="org-name">Organization Name</Label>
                          <Input id="org-name" value={organization?.name || ''} readOnly className="max-w-md bg-gray-50" />
                          <p className="text-xs text-gray-500">The name of your organization.</p>
                        </div>

                        {/* Organization ID */}
                        <div className="space-y-2">
                          <Label htmlFor="org-id">Organization ID</Label>
                          <Input id="org-id" value={organization?.id || ''} readOnly className="max-w-md bg-gray-50 font-mono text-sm" />
                          <p className="text-xs text-gray-500">Unique identifier for your organization.</p>
                        </div>

                        {/* Organization Slug */}
                        <div className="space-y-2">
                          <Label htmlFor="org-slug">Organization Slug</Label>
                          <Input id="org-slug" value={organization?.slug || ''} readOnly className="max-w-md bg-gray-50" />
                          <p className="text-xs text-gray-500">URL-friendly identifier for your organization.</p>
                        </div>

                        {/* Your Role in Organization */}
                        <div className="space-y-2">
                          <Label htmlFor="org-role">Your Role</Label>
                          <div className="flex items-center gap-2">
                            <Input id="org-role" value={organization?.role || ''} readOnly className="max-w-md bg-gray-50" />
                            <Badge variant="secondary" className="capitalize bg-blue-100 text-blue-800">
                              {organization?.role}
                            </Badge>
                          </div>
                          <p className="text-xs text-gray-500">Your role within this organization.</p>
                        </div>

                        {/* Subscription Information */}
                        {organization?.subscription && (
                          <div className="space-y-4">
                            <div className="border-t pt-4">
                              <h3 className="text-lg font-medium mb-3">Subscription Details</h3>
                            </div>

                            {/* Plan */}
                            <div className="space-y-2">
                              <Label htmlFor="plan">Plan</Label>
                              <div className="flex items-center gap-2">
                                <Input id="plan" value={organization.subscription.plan || ''} readOnly className="max-w-md bg-gray-50" />
                                <Badge variant="outline" className="capitalize">
                                  {organization.subscription.plan}
                                </Badge>
                              </div>
                              <p className="text-xs text-gray-500">Your current subscription plan.</p>
                            </div>

                            {/* Member Information */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div className="space-y-2">
                                <Label htmlFor="member-limit">Member Limit</Label>
                                <Input 
                                  id="member-limit" 
                                  value={organization.subscription.memberLimit || ''} 
                                  readOnly 
                                  className="bg-gray-50" 
                                />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="current-members">Current Members</Label>
                                <Input 
                                  id="current-members" 
                                  value={organization.subscription.currentMemberCount || ''} 
                                  readOnly 
                                  className="bg-gray-50" 
                                />
                              </div>
                            </div>

                            {/* Can Add Member Status */}
                            <div className="space-y-2">
                              <Label htmlFor="can-add">Can Add Members</Label>
                              <div className="flex items-center gap-2">
                                <Input 
                                  id="can-add" 
                                  value={organization.subscription.canAddMember ? 'Yes' : 'No'} 
                                  readOnly 
                                  className="max-w-md bg-gray-50" 
                                />
                                <Badge variant={organization.subscription.canAddMember ? "secondary" : "outline"} 
                                       className={organization.subscription.canAddMember ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}>
                                  {organization.subscription.canAddMember ? 'Available' : 'Unavailable'}
                                </Badge>
                              </div>
                              <p className="text-xs text-gray-500">Whether you can add new members to the organization.</p>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </>
                )}
              </ScrollArea>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
