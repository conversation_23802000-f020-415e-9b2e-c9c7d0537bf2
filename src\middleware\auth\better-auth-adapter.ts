/**
 * Better Auth 中间件适配器
 * 在 Edge Runtime 环境中安全地使用 Better Auth 功能
 *
 * 这个适配器解决了以下问题：
 * 1. auth-client.ts 是为客户端设计的，不能直接在中间件中使用
 * 2. 中间件运行在 Edge Runtime，有特定的 API 限制
 * 3. 需要高性能的会话验证，避免每次都发起网络请求
 */

import { NextRequest } from 'next/server'
import { UserSession, AuthCheckResult } from './auth-utils'

/**
 * Better Auth 服务端会话验证适配器
 * 这是一个更优雅的方案，直接使用 Better Auth 的服务端逻辑
 */
export class BetterAuthMiddlewareAdapter {
  private sessionCache = new Map<
    string,
    {
      session: UserSession | null
      timestamp: number
    }
  >()

  private readonly CACHE_TTL = 60000 // 1分钟缓存

  /**
   * 获取用户会话（带缓存）
   */
  async getSession(request: NextRequest): Promise<AuthCheckResult> {
    try {
      const sessionToken = this.extractSessionToken(request)

      if (!sessionToken) {
        return {
          isAuthenticated: false,
          session: null,
        }
      }

      // 检查缓存
      const cached = this.getFromCache(sessionToken)
      if (cached !== undefined) {
        return {
          isAuthenticated: !!cached,
          session: cached,
        }
      }

      // 验证会话
      const session = await this.validateSession(request, sessionToken)

      // 缓存结果
      this.setCache(sessionToken, session)

      return {
        isAuthenticated: !!session,
        session,
      }
    } catch (error) {
      return {
        isAuthenticated: false,
        session: null,
        error: error instanceof Error ? error.message : 'Session validation failed',
      }
    }
  }

  /**
   * 从请求中提取 session token
   */
  private extractSessionToken(request: NextRequest): string | null {
    // Better Auth 的标准 cookie 名
    const sessionCookie =
      request.cookies.get('better-auth.session_token') || request.cookies.get('auth.session_token')

    if (!sessionCookie?.value) {
      return null
    }

    // Better Auth token 格式验证
    const parts = sessionCookie.value.split('.')
    if (parts.length !== 2) {
      return null
    }

    const [sessionId, encryptedData] = parts
    if (sessionId.length < 10 || encryptedData.length < 10) {
      return null
    }

    return sessionCookie.value
  }

  /**
   * 验证会话有效性
   *
   * 理想情况下，这里应该直接使用 Better Auth 的服务端验证逻辑
   * 而不是发起 HTTP 请求。但由于架构限制，我们暂时使用 API 调用方式
   *
   * TODO: 后续可以考虑直接集成 Better Auth 的服务端验证函数
   */
  private async validateSession(
    request: NextRequest,
    sessionToken: string
  ): Promise<UserSession | null> {
    try {
      // 构建 Better Auth API URL
      const baseUrl = request.nextUrl.origin
      const apiUrl = `${baseUrl}/api/auth/get-session`

      // 准备请求头
      const headers: HeadersInit = {
        'Content-Type': 'application/json',
        Cookie: request.headers.get('cookie') || '',
        'User-Agent': 'NextJS-Middleware-BetterAuth/2.0',
      }

      // 调用 Better Auth API
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers,
        cache: 'no-store',
      })

      if (!response.ok) {
        return null
      }

      const data = await response.json()

      // Better Auth 标准响应格式
      if (!data.data?.session || !data.data?.user) {
        return null
      }

      // 转换为标准 UserSession 格式
      return this.transformBetterAuthResponse(data.data)
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('[BetterAuth Adapter] Session validation failed:', error)
      }
      return null
    }
  }

  /**
   * 转换 Better Auth 响应为标准格式
   */
  private transformBetterAuthResponse(data: any): UserSession {
    const userSession: UserSession = {
      user: {
        id: data.user.id,
        email: data.user.email,
        name: data.user.name,
        role: data.user.role,
        language: data.user.language || 'zh-CN',
      },
      expires: data.session.expires,
    }

    // 只有当 organization 存在时才添加该字段
    if (data.user.organization) {
      userSession.organization = {
        id: data.user.organization.id,
        hasCompanyProfile: data.user.organization.hasCompanyProfile,
        role: data.user.organization.role,
      }
    }

    return userSession
  }

  /**
   * 缓存管理
   */
  private getFromCache(sessionToken: string): UserSession | null | undefined {
    const key = this.generateCacheKey(sessionToken)
    const cached = this.sessionCache.get(key)

    if (!cached) {
      return undefined
    }

    // 检查过期
    if (Date.now() - cached.timestamp > this.CACHE_TTL) {
      this.sessionCache.delete(key)
      return undefined
    }

    return cached.session
  }

  private setCache(sessionToken: string, session: UserSession | null): void {
    const key = this.generateCacheKey(sessionToken)
    this.sessionCache.set(key, {
      session,
      timestamp: Date.now(),
    })
  }

  private generateCacheKey(sessionToken: string): string {
    // 使用 token 的前16位作为缓存键（安全考虑）
    return sessionToken.substring(0, 16)
  }

  /**
   * 清理过期缓存
   */
  cleanupCache(): void {
    const now = Date.now()
    for (const [key, value] of this.sessionCache.entries()) {
      if (now - value.timestamp > this.CACHE_TTL) {
        this.sessionCache.delete(key)
      }
    }
  }
}

/**
 * 全局适配器实例
 * 在中间件中复用，提升性能
 */
const betterAuthAdapter = new BetterAuthMiddlewareAdapter()

// 定期清理缓存
if (typeof globalThis !== 'undefined') {
  setInterval(() => {
    betterAuthAdapter.cleanupCache()
  }, 300000) // 5分钟清理一次
}

export default betterAuthAdapter

/**
 * 便捷导出函数
 * 保持与原有 auth-utils 的兼容性
 */
export async function getBetterAuthSession(request: NextRequest): Promise<AuthCheckResult> {
  return betterAuthAdapter.getSession(request)
}

/**
 * 快速会话检查（只验证 cookie 格式，不发起网络请求）
 * 适用于性能敏感的场景
 */
export function hasBetterAuthSession(request: NextRequest): boolean {
  const sessionCookie =
    request.cookies.get('better-auth.session_token') || request.cookies.get('auth.session_token')

  if (!sessionCookie?.value) {
    return false
  }

  const parts = sessionCookie.value.split('.')
  if (parts.length !== 2) return false

  const [sessionId, encryptedData] = parts
  return sessionId.length > 10 && encryptedData.length > 10
}
