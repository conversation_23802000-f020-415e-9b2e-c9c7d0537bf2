"use client"

import { useEffect } from "react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { ChevronRight } from "lucide-react"

interface SidePanelProps {
  isOpen: boolean
  onClose?: () => void
  title?: string
  width?: string  // 例如 '450px'
  children?: React.ReactNode
}

export default function SidePanel({
  isOpen,
  onClose,
  title,
  width = "450px",
  children,
}: SidePanelProps) {
  useEffect(() => {
    document.body.style.overflow = isOpen ? "hidden" : ""
    return () => {
      document.body.style.overflow = ""
    }
  }, [isOpen])

  return (
    <aside
      className={cn(
        "fixed inset-y-0 right-0 bg-white border-l border-gray-200 flex flex-col overflow-hidden transition-[width] duration-300 ease-in-out z-40",
        isOpen ? `w-[${width}]` : "w-0"
      )}
      style={{ width: isOpen ? width : 0 }}
    >
    <div className="px-6 py-4 flex items-center justify-between flex-shrink-0">
        {title && <h2 className="text-lg font-semibold text-gray-900">{title}</h2>}
        {onClose && (
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0"
            onClick={onClose}
            aria-label="Collapse right panel"
          >
            <ChevronRight className="h-4 w-4 text-gray-600" />
          </Button>
        )}
      </div>
      <div className="flex-1 overflow-auto px-6 py-6">
        {children}
      </div>
    </aside>
  )
}
