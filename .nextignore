# Next.js构建忽略文件

# 依赖和缓存
node_modules/
.next/
.cache/
dist/
build/
out/

# 文档和说明文件
docs/
migration-docs/
*.md
README*
Todo*
CHANGELOG*
LICENSE*

# 配置和脚本文件
.env*
.dockerignore
Dockerfile*
docker-compose*
deploy.sh
quick_deploy.sh
build_push_image.sh

# 编辑器和IDE文件
.vscode/
.idea/
.cursor/
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
.DS_Store?
._*
Thumbs.db
desktop.ini

# 日志文件
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 测试和覆盖率文件
coverage/
.nyc_output/
test-results/
*.lcov

# 包管理文件
package-lock.json
yarn.lock
pnpm-lock.yaml

# TypeScript编译文件
*.tsbuildinfo
tsconfig.tsbuildinfo

# Git文件
.git/
.gitignore
.gitattributes

# 临时文件
tmp/
temp/
*.tmp
*.temp

# 环境配置
.env.local
.env.development.local
.env.test.local
.env.production.local

# 备份文件
*.bak
*.backup 