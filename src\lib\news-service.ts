// 简化的新闻API服务
import { get, post, del, ApiResponse } from './request'
import { TopicsResponse, NewsListRequest, NewsListResponse, CreateTopicRequest, CreateTopicResponse } from '@/types/news'

/**
 * 获取用户关注的主题列表
 * GET /information_news/subscribed_topics
 */
export async function fetchSubscribedTopics(): Promise<TopicsResponse> {
  try {
    const response = await get<ApiResponse<TopicsResponse>>('/information_news/subscribed_topics')
    
    // 添加数据验证
    console.log('原始API响应:', response)
    
    // 处理API响应格式 { code, success, message, data }
    const apiData = response.data
    if (apiData && typeof apiData === 'object' && 'data' in apiData) {
      const topicsData = apiData.data
      
      // 确保返回的数据是正确的格式
      if (topicsData && typeof topicsData === 'object') {
        // 验证所有值都是字符串
        const validatedData: TopicsResponse = {}
        Object.entries(topicsData).forEach(([key, value]) => {
          if (typeof value === 'string') {
            validatedData[key] = value
          } else {
            console.warn(`API返回非字符串话题值: ${key} =`, value)
          }
        })
        return validatedData
      }
    }
    
    return {}
  } catch (error) {
    console.error('获取话题列表失败:', error)
    throw error
  }
}

/**
 * 获取指定主题下的新闻列表
 * POST /information_news/topic_news_list
 */
export async function fetchTopicNewsList(params: NewsListRequest): Promise<NewsListResponse> {
  try {
    const response = await post<ApiResponse<NewsListResponse>>('/information_news/topic_news_list', params)
    
    // 处理API响应格式 { code, success, message, data }
    const apiData = response.data
    if (apiData && typeof apiData === 'object' && 'data' in apiData) {
      return apiData.data as NewsListResponse
    }
    
    
    // 如果没有包装格式，返回默认空数据
    return {
      news_list: [],
      total: 0,
      page: 1,
      page_size: 20,
      total_pages: 0
    }
  } catch (error) {
    console.error('获取新闻列表失败:', error)
    throw error
  }
}

/**
 * 取消订阅话题
 * DELETE /information_news/unsubscribe_topic/{topic_id}
 */
export async function unsubscribeTopic(topicId: string): Promise<boolean> {
  try {
    const response = await del<ApiResponse<any>>(`/information_news/unsubscribe_topic/${topicId}`)

    // 处理API响应格式 { code, success, message, data }
    const apiData = response.data
    if (apiData && typeof apiData === 'object' && 'success' in apiData) {
      return apiData.success && apiData.code === 200
    }

    // 如果没有包装格式，检查HTTP状态码
    return response.status === 200
  } catch (error) {
    console.error('删除话题失败:', error)
    throw error
  }
}

/**
 * 创建新话题
 * POST /information_news/create_topic
 */
export async function createTopic(params: CreateTopicRequest): Promise<CreateTopicResponse> {
  try {
    const response = await post<ApiResponse<CreateTopicResponse>>('/information_news/create_topic', params)

    console.log('创建话题API响应:', response)

    // 处理API响应格式 { code, success, message, data }
    const apiData = response.data
    if (apiData && typeof apiData === 'object' && 'data' in apiData) {
      return apiData.data as CreateTopicResponse
    }

    // 如果没有包装格式，直接返回响应数据
    if (response.data && typeof response.data === 'object') {
      return response.data as unknown as CreateTopicResponse
    }

    // 如果响应格式不符合预期，抛出错误
    throw new Error('创建话题响应格式不正确')
  } catch (error) {
    console.error('创建话题失败:', error)
    throw error
  }
}

/**
 * 获取系统所有话题
 * GET /information_news/all_topics
 */
export async function fetchAllTopics(): Promise<TopicsResponse> {
  try {
    const response = await get<ApiResponse<TopicsResponse>>('/information_news/all_topics')

    // 添加数据验证
    console.log('获取系统话题API响应:', response)

    // 处理API响应格式 { code, success, message, data }
    const apiData = response.data
    if (apiData && typeof apiData === 'object' && 'data' in apiData) {
      const topicsData = apiData.data

      // 确保返回的数据是正确的格式
      if (topicsData && typeof topicsData === 'object') {
        // 验证所有值都是字符串
        const validatedData: TopicsResponse = {}
        Object.entries(topicsData).forEach(([key, value]) => {
          if (typeof value === 'string') {
            validatedData[key] = value
          } else {
            console.warn(`API返回非字符串话题值: ${key} =`, value)
          }
        })
        return validatedData
      }
    }

    return {}
  } catch (error) {
    console.error('获取系统话题列表失败:', error)
    throw error
  }
}



/**
 * 获取用户关注的招投标主题列表 (占位符)
 * GET /information_tender/subscribed_topics
 */
export async function fetchSubscribedTenderTopics(): Promise<TopicsResponse> {
  try {
    // 返回模拟数据，不实际调用API
    console.log('使用招投标话题占位符数据')
    return Promise.resolve({
      '1': '基础设施建设',
      '2': '政府采购项目',
      '3': '工程承包项目',
      '4': '建筑工程招标',
      '5': '设备采购项目'
    })
  } catch (error) {
    console.error('获取招投标话题列表失败:', error)
    throw error
  }
}

/**
 * 获取指定招投标主题下的项目列表 (占位符)
 * POST /information_tender/topic_tender_list
 */
export async function fetchTenderTopicsList(params: NewsListRequest): Promise<NewsListResponse> {
  try {
    console.log('使用招投标项目列表占位符数据:', params)
    // 返回空数据结构
    return Promise.resolve({
      news_list: [],
      total: 0,
      page: 1,
      page_size: 20,
      total_pages: 0
    })
  } catch (error) {
    console.error('获取招投标项目列表失败:', error)
    throw error
  }
}

/**
 * 取消订阅招投标话题 (占位符)
 * DELETE /information_tender/unsubscribe_topic/{topic_id}
 */
export async function unsubscribeTenderTopic(topicId: string): Promise<boolean> {
  try {
    console.log('模拟删除招投标话题:', topicId)
    // 模拟删除成功
    return Promise.resolve(true)
  } catch (error) {
    console.error('删除招投标话题失败:', error)
    throw error
  }
}

