/**
 * 聊天控制组件 - 发送按钮和功能控制
 * 
 * 功能：发送按钮、功能切换、快捷操作
 * 依赖：UI Store中的交互状态
 * 性能：轻量级控制组件，响应式设计
 * 
 * 数据流：User Action -> ChatControls -> Parent Callbacks
 */

'use client'

import React from 'react'
import { cn } from '@/lib/utils'
import { Send, Loader2, MoreHorizontal, Trash2, HelpCircle } from 'lucide-react'

// UI组件
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'

// ============================================================================
// 组件接口定义
// ============================================================================

export interface ChatControlsProps {
  /** 发送状态 */
  canSend: boolean
  isSubmitting: boolean
  
  /** 发送回调 */
  onSend: () => void
  
  /** 功能配置 */
  features?: {
    enableFileUpload?: boolean
    enableVoiceInput?: boolean
    enableThinkMode?: boolean
    enableDeepSearch?: boolean
  }
  
  /** 移动端适配 */
  isMobile: boolean
  
  /** 额外控制 */
  showMoreOptions?: boolean
  showHelpButton?: boolean
  
  /** 事件回调 */
  onClearChat?: () => void
  onShowHelp?: () => void
  onShowSettings?: () => void
  
  /** 样式配置 */
  className?: string
  variant?: 'default' | 'compact' | 'minimal'
}

// 默认配置
const DEFAULT_FEATURES = {
  enableFileUpload: false,
  enableVoiceInput: false,
  enableThinkMode: false,
  enableDeepSearch: false,
}

// ============================================================================
// 发送按钮组件
// ============================================================================

interface SendButtonProps {
  canSend: boolean
  isSubmitting: boolean
  onSend: () => void
  isMobile: boolean
  variant?: ChatControlsProps['variant']
}

const SendButton: React.FC<SendButtonProps> = ({
  canSend,
  isSubmitting,
  onSend,
  isMobile,
  variant = 'default',
}) => {
  const getButtonSize = () => {
    if (variant === 'compact') return 'sm'
    if (variant === 'minimal') return 'sm'
    return 'default'
  }

  const getButtonClass = () => {
    const base = 'rounded-full flex-shrink-0 transition-all duration-200'
    
    switch (variant) {
      case 'compact':
        return cn(base, 'h-8 w-8')
      case 'minimal':
        return cn(base, 'h-10 w-10')
      case 'default':
      default:
        return cn(base, 'h-12 w-12')
    }
  }

  const getIconSize = () => {
    switch (variant) {
      case 'compact':
        return 'w-3 h-3'
      case 'minimal':
        return 'w-4 h-4'
      case 'default':
      default:
        return 'w-5 h-5'
    }
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            size={getButtonSize()}
            onClick={onSend}
            disabled={!canSend}
            className={cn(
              getButtonClass(),
              canSend
                ? 'bg-black hover:bg-zinc-700 text-white'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed',
              isSubmitting && 'animate-pulse'
            )}
          >
            {isSubmitting ? (
              <Loader2 className={cn(getIconSize(), 'animate-spin')} />
            ) : (
              <Send className={getIconSize()} />
            )}
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>{isSubmitting ? '发送中...' : canSend ? '发送消息' : '输入消息后发送'}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}

// ============================================================================
// 更多选项菜单
// ============================================================================

interface MoreOptionsMenuProps {
  onClearChat?: (() => void) | undefined
  onShowHelp?: (() => void) | undefined
  onShowSettings?: (() => void) | undefined
  isMobile: boolean
}

const MoreOptionsMenu: React.FC<MoreOptionsMenuProps> = ({
  onClearChat,
  onShowHelp,
  onShowSettings,
  isMobile,
}) => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size={isMobile ? 'sm' : 'default'}
          className="rounded-full"
        >
          <MoreHorizontal className="w-4 h-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        {onShowHelp && (
          <DropdownMenuItem onClick={onShowHelp}>
            <HelpCircle className="w-4 h-4 mr-2" />
            快捷键帮助
          </DropdownMenuItem>
        )}
        
        {onShowSettings && (
          <DropdownMenuItem onClick={onShowSettings}>
            <HelpCircle className="w-4 h-4 mr-2" />
            聊天设置
          </DropdownMenuItem>
        )}
        
        {(onShowHelp || onShowSettings) && onClearChat && (
          <DropdownMenuSeparator />
        )}
        
        {onClearChat && (
          <DropdownMenuItem 
            onClick={onClearChat}
            className="text-red-600 focus:text-red-600"
          >
            <Trash2 className="w-4 h-4 mr-2" />
            清空对话
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

// ============================================================================
// 快捷操作按钮
// ============================================================================

interface QuickActionsProps {
  onShowHelp?: (() => void) | undefined
  isMobile: boolean
  variant?: ChatControlsProps['variant']
}

const QuickActions: React.FC<QuickActionsProps> = ({
  onShowHelp,
  isMobile,
  variant = 'default',
}) => {
  if (!onShowHelp) return null

  const buttonSize = variant === 'compact' || isMobile ? 'sm' : 'default'

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            size={buttonSize}
            onClick={onShowHelp}
            className="rounded-full"
          >
            <HelpCircle className="w-4 h-4" />
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>快捷键帮助</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}

// ============================================================================
// 主要组件
// ============================================================================

export const ChatControls: React.FC<ChatControlsProps> = ({
  canSend,
  isSubmitting,
  onSend,
  features = DEFAULT_FEATURES,
  isMobile,
  showMoreOptions = true,
  showHelpButton = true,
  onClearChat,
  onShowHelp,
  onShowSettings,
  className,
  variant = 'default',
}) => {
  // ============================================================================
  // 功能配置
  // ============================================================================
  
  const config = {
    ...DEFAULT_FEATURES,
    ...features,
  }

  const hasAdvancedFeatures = Object.values(config).some(Boolean)
  const showQuickActions = showHelpButton && !showMoreOptions

  // ============================================================================
  // 渲染逻辑
  // ============================================================================

  const renderControls = () => {
    switch (variant) {
      case 'minimal':
        return (
          <SendButton
            canSend={canSend}
            isSubmitting={isSubmitting}
            onSend={onSend}
            isMobile={isMobile}
            variant={variant}
          />
        )

      case 'compact':
        return (
          <div className="flex items-center space-x-1">
            {showQuickActions && (
              <QuickActions
                onShowHelp={onShowHelp}
                isMobile={isMobile}
                variant={variant}
              />
            )}
            
            <SendButton
              canSend={canSend}
              isSubmitting={isSubmitting}
              onSend={onSend}
              isMobile={isMobile}
              variant={variant}
            />
          </div>
        )

      case 'default':
      default:
        return (
          <div className="flex items-center space-x-2">
            {/* 快捷操作 */}
            {showQuickActions && (
              <QuickActions
                onShowHelp={onShowHelp}
                isMobile={isMobile}
                variant={variant}
              />
            )}

            {/* 更多选项菜单 */}
            {showMoreOptions && (onClearChat || onShowHelp || onShowSettings) && (
              <MoreOptionsMenu
                onClearChat={onClearChat}
                onShowHelp={onShowHelp}
                onShowSettings={onShowSettings}
                isMobile={isMobile}
              />
            )}

            {/* 发送按钮 */}
            <SendButton
              canSend={canSend}
              isSubmitting={isSubmitting}
              onSend={onSend}
              isMobile={isMobile}
              variant={variant}
            />
          </div>
        )
    }
  }

  // ============================================================================
  // 主要渲染
  // ============================================================================

  return (
    <div className={cn('flex items-end', className)}>
      {renderControls()}
      
      {/* 开发环境状态显示 */}
      {process.env.NODE_ENV === 'development' && variant === 'default' && (
        <div className="ml-2 text-xs text-muted-foreground">
          <div>发送: {canSend ? '✅' : '❌'}</div>
          <div>提交: {isSubmitting ? '✅' : '❌'}</div>
        </div>
      )}
    </div>
  )
}

// ============================================================================
// 控制状态Hook
// ============================================================================

export const useChatControlsState = (
  userInputContent: string,
  isConnected: boolean,
  isSubmitting: boolean
) => {
  const canSend = !!(
    userInputContent.trim() && 
    isConnected && 
    !isSubmitting
  )

  return {
    canSend,
    isSubmitting,
    isConnected,
    hasContent: !!userInputContent.trim(),
  }
}

// ============================================================================
// 导出
// ============================================================================

ChatControls.displayName = 'ChatControls'

export default ChatControls