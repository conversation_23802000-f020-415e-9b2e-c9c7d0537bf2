'use client'

import { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Search, RotateCcw, ChevronLeft, Clock } from 'lucide-react'
import { WeekAgoDatePicker } from '@/components/common/date-pick'
import Image from 'next/image'
import TopIcon from '@/assets/Top.svg'
import informationIcon from '@/assets/information.png'

// 导入 session provider
import { useSession } from '@/components/providers/session-provider'

// 导入核心组件
import NewsList from '@/components/news/NewsList'
import RightPanel from '@/components/news/RightPanel'

// 导入简化的API服务和类型
import { fetchTopicNewsList, fetchSubscribedTopics, fetchAllTopics } from '@/lib/news-service'
import { NewsItem } from '@/types/news'

export default function AboutPage() {
  const searchParams = useSearchParams()
  const topicId = searchParams.get('topicId')
  const category = searchParams.get('category') // 保持向后兼容
  
  const [selectedMainArticleId, setSelectedMainArticleId] = useState<string | null>(null)
  const [isRightPanelOpen, setIsRightPanelOpen] = useState(false)
  const [inputValue, setInputValue] = useState('')
  const [newsArticles, setNewsArticles] = useState<NewsItem[]>([])
  const [topics, setTopics] = useState<{[key: string]: string}>({})
  const [currentTopicName, setCurrentTopicName] = useState<string>('')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const { user, loading: sessionLoading, organization } = useSession()
  console.log('user', user)

   // 调用获取系统话题接口
   useEffect(() => {
    const fetchSystemTopics = async () => {
      try {
        console.log('正在调用获取系统话题接口...')
        const topics = await fetchAllTopics()
        console.log('获取系统话题成功:', topics)
      } catch (error) {
        console.error('获取系统话题失败:', error)
      }
    }

    fetchSystemTopics()
  }, [])

  // 获取话题列表（用于获取话题名称）
  useEffect(() => {
    async function loadTopics() {
      try {
        const topicsData = await fetchSubscribedTopics()
        setTopics(topicsData)
      } catch (error) {
        console.error('获取话题列表失败:', error)
        setTopics({})
      }
    }
    loadTopics()
  }, [])

  // 获取新闻数据
  useEffect(() => {
    async function loadNews() {
      if (!topicId && !category) {
        // 如果没有topicId，显示默认状态
        setNewsArticles([])
        setCurrentTopicName('')
        setLoading(false)
        setError('请选择一个话题查看新闻')
        return
      }

      try {
        setLoading(true)
        setError(null)
        
        const currentTopicId = topicId || category
        if (!currentTopicId) return

        // 获取话题新闻
        const newsData = await fetchTopicNewsList({
          topic_id: parseInt(currentTopicId),
          page: 1,
          page_size: 20
        })
        setNewsArticles(newsData.news_list)
      } catch (err) {
        console.error('获取新闻数据失败:', err)
        setError('获取新闻数据失败，请稍后重试')
        setNewsArticles([])
      } finally {
        setLoading(false)
      }
    }

    loadNews()
  }, [topicId, category]) // 移除topics依赖，避免循环调用

  // 单独处理话题名称设置
  useEffect(() => {
    if (topicId || category) {
      const currentTopicId = topicId || category
      if (currentTopicId) {
        const topicName = topics[currentTopicId]
        const displayTopicName = typeof topicName === 'string' ? topicName : `话题 ${currentTopicId}`
        setCurrentTopicName(displayTopicName)
      }
    }
  }, [topicId, category, topics])
  // 处理新闻点击事件
  const handleArticleClick = (articleId: string | null) => {
    setSelectedMainArticleId(articleId)
    if (articleId) {
      setIsRightPanelOpen(true)
    }
  }

  // 处理右侧面板关闭事件
  const handleRightPanelClose = () => {
    setIsRightPanelOpen(false)
  }

  return (
    <div className="flex h-screen bg-gray-50 font-sans">
      {/* Main Content Area */}
      <main className="flex-1 flex flex-col bg-white relative">
        {/* Wrapper for main content to apply max-width and centering */}
        <div
          className={`mx-auto w-full h-full flex flex-col transition-all duration-300 ease-in-out ${
            isRightPanelOpen ? 'max-w-4xl' : 'max-w-6xl'
          }`}
        >
          {/* Main Header */}
          <header className="px-6 py-4 bg-white">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                {/* 根据是否有topicId/category来决定显示内容 */}
                {topicId || category ? (
                  // 话题页面：只显示话题名称
                  <div className="text-2xl font-semibold text-gray-900">
                    {currentTopicName || '话题新闻'}
                  </div>
                ) : (
                  // 默认页面：只显示TOP图片
                  <Image 
                    src={TopIcon} 
                    alt="TOP" 
                    width={144} 
                    height={60}
                    className="object-contain"
                  />
                )}
              </div>
            </div>
          </header>

          {/* Chat Content */}
          <div className="flex-1 flex flex-col overflow-hidden">
            {/* Bot Message and Timestamp - Fixed height */}
            <div className="px-6 py-6 flex-shrink-0">
              <div className="flex justify-between items-start">
                {/* Bot Message */}
                <div className="flex items-start">
                  <div className="h-8 w-8 mr-3 mt-1 flex-shrink-0 flex items-center justify-center">
                    <Image src={informationIcon} alt="Information" width={48} height={48} />
                  </div>
                  <div className="bg-gray-100 rounded-2xl px-4 py-3 max-w-md">
                    <div className="text-sm text-gray-800">
                      {topicId || category 
                        ? `今天的${currentTopicName || '话题'}有重要资讯更新，建议关注` 
                        : '今天的针对双XX有新政策消息，建议关注'
                      }
                    </div>
                  </div>
                </div>

                {/* Timestamp - Right Aligned and Stacked */}
                <div className="flex flex-col items-end text-right mt-1">
                  <div className="flex items-center text-xs text-gray-500 bg-gray-50 rounded-full px-3 py-1 mb-1">
                    <Clock className="h-3 w-3 mr-1" />
                    每小时更新
                  </div>
                  <WeekAgoDatePicker />
                </div>
              </div>
            </div>

            {/* News Articles - Scrollable area */}
            <div className="flex-1 overflow-hidden pb-6">
              <ScrollArea className="h-[calc(100vh-320px)] px-6">
                {loading ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="text-gray-500">加载中...</div>
                  </div>
                ) : error ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="text-red-500">{error}</div>
                  </div>
                ) : (
                  <NewsList
                    articles={newsArticles}
                    onArticleClick={handleArticleClick}
                    selectedArticleId={selectedMainArticleId}
                    hideNumbers={topicId || category ? true : false}
                  />
                )}
              </ScrollArea>
              <div className="px-6 py-4 bg-white">
                <div className="flex items-center space-x-3">
                  <Input
                    placeholder="输入请求"
                    value={inputValue}
                    onChange={e => setInputValue(e.target.value)}
                    className="flex-1 h-10 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Expand Right Panel Button (conditionally rendered) */}
        {!isRightPanelOpen && (
          <Button
            variant="ghost"
            size="sm"
            className="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-white border border-gray-200 rounded-l-lg rounded-r-none h-12 w-6 flex items-center justify-center shadow-md"
            onClick={() => setIsRightPanelOpen(true)}
            aria-label="Expand right panel"
          >
            <ChevronLeft className="h-4 w-4 text-gray-600" />
          </Button>
        )}
      </main>

      {/* Right Panel */}
      <RightPanel
        articleId={selectedMainArticleId}
        isOpen={isRightPanelOpen}
        onClose={handleRightPanelClose}
        articles={newsArticles}
      />
    </div>
  )
}
