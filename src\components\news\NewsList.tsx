"use client"

// 简化导入
import { NewsItem } from "@/types/news"
import NewsDetail from "./NewsDetail"
import { cn } from "@/lib/utils"

interface NewsListProps {
  articles: NewsItem[]
  onArticleClick: (articleId: string | null) => void
  selectedArticleId: string | null
  hideNumbers?: boolean
}

export default function NewsList({ articles, onArticleClick, selectedArticleId, hideNumbers = false }: NewsListProps) {
  // 处理新闻点击事件
  const handleArticleClick = (articleId: string) => {
    console.log('articleId', articleId)
    // 如果点击的是当前选中的新闻，则收起详情（传递null）
    // 否则选中新的新闻
    const newSelectedId = selectedArticleId === articleId ? null : articleId
    onArticleClick(newSelectedId)
  }

  // 简化：只处理API原始格式数据

  // 获取文章ID（API原始格式）
  const getArticleId = (article: NewsItem): string => {
    return article._id
  }

  // 获取文章内容（API原始格式）
  const getArticleContent = (article: NewsItem): string => {
    return article.summary
  }

  // 获取文章编号
  const getArticleNumber = (article: NewsItem, index: number): string => {
    return (index + 1).toString()
  }

  return (
    <div className="space-y-4">
      {articles.map((article, index) => {
        const articleId = getArticleId(article)
        const isSelected = selectedArticleId === articleId
        
        return (
          <div key={articleId} className={cn(
            "rounded-lg overflow-hidden transition-all duration-300",
            isSelected ? "bg-gray-100" : "bg-white"
          )}>
            {/* News Article Header - 始终显示 */}
            <article
              className="cursor-pointer transition-all duration-200 hover:bg-gray-50"
              onClick={() => handleArticleClick(articleId)}
            >
              <div className="flex items-start p-4">
                {!hideNumbers && (
                  <div className="text-3xl font-bold text-orange-500 mr-4 mt-1 flex-shrink-0 leading-none">
                    {getArticleNumber(article, index)}
                  </div>
                )}
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <h2 className="text-lg font-semibold text-gray-900 mb-3 leading-tight">
                      {article.title}
                    </h2>
                  </div>
                  <p className="text-sm text-gray-700 leading-relaxed">{getArticleContent(article)}</p>
                  {/* 显示时间戳 */}
                  {article.publish_date && (
                    <div className="text-xs text-gray-400 mt-2">
                      {new Date(article.publish_date).toLocaleDateString('zh-CN')}
                    </div>
                  )}
                  {/* 显示来源 */}
                  {article.web_site && (
                    <div className="text-xs text-gray-500 mt-1">
                      来源：{article.web_site}
                    </div>
                  )}
                </div>
              </div>
            </article>

            {/* News Detail - 展开/收起动画 */}
            <div 
              className={cn(
                "overflow-hidden transition-all duration-300 ease-in-out",
                isSelected ? "max-h-[1000px] opacity-100" : "max-h-0 opacity-0"
              )}
            >
              <div className="px-4 pb-4">
                <NewsDetail 
                  articleId={articleId}
                  articles={articles}
                />
              </div>
            </div>
          </div>
        )
      })}
    </div>
  )
} 