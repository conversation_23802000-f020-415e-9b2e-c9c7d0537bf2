'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { LogOut } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { signOut } from '@/lib/auth/auth-client'

interface LogoutButtonProps {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
  size?: 'default' | 'sm' | 'lg' | 'icon'
  showIcon?: boolean
  className?: string
  children?: React.ReactNode
}

export function LogoutButton({
  variant = 'outline',
  size = 'default',
  showIcon = true,
  className,
  children,
}: LogoutButtonProps) {
  const [isLoggingOut, setIsLoggingOut] = useState(false)
  const router = useRouter()

  const handleLogout = async () => {
    if (isLoggingOut) return

    setIsLoggingOut(true)
    try {
      console.log('🔄 开始登出...')

      // 调用Better Auth的登出API
      await signOut()

      console.log('✅ 登出成功')

      // 重定向到landing页面
      router.push('')
    } catch (error) {
      console.error('❌ 登出失败:', error)
      // 即使登出失败，也尝试跳转（可能是网络问题）
      router.push('')
    } finally {
      setIsLoggingOut(false)
    }
  }

  return (
    <Button
      onClick={handleLogout}
      disabled={isLoggingOut}
      variant={variant}
      size={size}
      className={className}
    >
      {isLoggingOut ? (
        <>
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
          登出中...
        </>
      ) : (
        <>
          {showIcon && <LogOut className="h-4 w-4 mr-2" />}
          {children || '登出'}
        </>
      )}
    </Button>
  )
}
