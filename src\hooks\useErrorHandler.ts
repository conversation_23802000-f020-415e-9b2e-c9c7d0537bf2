/**
 * useErrorHandler Hook
 * 统一错误处理接口，集成 React 状态管理和错误边界
 *
 * 功能特性：
 * - 与 ErrorHandler 工具类集成
 * - React 状态管理支持
 * - 错误边界传播控制
 * - 组件级错误处理
 * - 自动 toast 显示
 */

import { useCallback, useState } from 'react'
import {
  ErrorHandler,
  ErrorHandlerConfig,
  ProcessedError,
  ErrorType,
  ErrorSeverity,
  handleError,
  handleCriticalError,
} from '@/lib/error-handler'

// Hook 配置接口
export interface UseErrorHandlerConfig {
  /** 是否在组件中维护错误状态 */
  maintainErrorState?: boolean
  /** 错误状态重置延迟（毫秒） */
  errorResetDelay?: number
  /** 自定义错误状态处理 */
  onErrorStateChange?: (error: ProcessedError | null) => void
  /** 是否显示 toast */
  showToast?: boolean
  /** 是否向上传播到错误边界 */
  propagateToErrorBoundary?: boolean
  /** 是否记录错误日志 */
  logError?: boolean
  /** 自定义错误消息 */
  customMessage?: string
  /** 错误操作回调 */
  onError?: (error: ProcessedError) => void
  /** 重试回调 */
  onRetry?: () => void | Promise<void>
}

// Hook 返回接口
export interface UseErrorHandlerReturn {
  /** 当前错误状态 */
  currentError: ProcessedError | null
  /** 是否有错误 */
  hasError: boolean
  /** 处理错误的主方法 */
  handleError: (error: Error | any, config?: ErrorHandlerConfig) => ProcessedError
  /** 处理关键错误（会传播到错误边界） */
  handleCriticalError: (error: Error | any) => ProcessedError
  /** 静默处理错误（不显示 toast） */
  handleErrorSilently: (error: Error | any) => ProcessedError
  /** 清除当前错误状态 */
  clearError: () => void
  /** 重试当前操作 */
  retry: () => void
  /** 检查错误类型 */
  isErrorType: (type: ErrorType) => boolean
}

/**
 * useErrorHandler Hook 实现
 */
export function useErrorHandler(config: UseErrorHandlerConfig = {}): UseErrorHandlerReturn {
  const {
    maintainErrorState = true,
    errorResetDelay = 5000,
    onErrorStateChange,
    ...errorHandlerConfig
  } = config

  // 错误状态管理
  const [currentError, setCurrentError] = useState<ProcessedError | null>(null)

  // 错误状态变化回调
  const handleErrorStateChange = useCallback(
    (error: ProcessedError | null) => {
      if (maintainErrorState) {
        setCurrentError(error)
      }

      if (onErrorStateChange) {
        onErrorStateChange(error)
      }

      // 自动清除错误状态
      if (error && errorResetDelay > 0) {
        setTimeout(() => {
          setCurrentError(null)
        }, errorResetDelay)
      }
    },
    [maintainErrorState, onErrorStateChange, errorResetDelay]
  )

  // 主错误处理方法
  const handleErrorMethod = useCallback(
    (error: Error | any, overrideConfig?: ErrorHandlerConfig): ProcessedError => {
      const finalConfig: ErrorHandlerConfig = {
        ...errorHandlerConfig,
        ...overrideConfig,
        onError: (processedError: ProcessedError) => {
          // 更新组件错误状态
          handleErrorStateChange(processedError)

          // 执行用户自定义错误处理
          if (errorHandlerConfig.onError) {
            errorHandlerConfig.onError(processedError)
          }
          if (overrideConfig && 'onError' in overrideConfig && overrideConfig.onError) {
            overrideConfig.onError(processedError)
          }
        },
      }

      return ErrorHandler.handle(error, finalConfig)
    },
    [errorHandlerConfig, handleErrorStateChange]
  )

  // 关键错误处理
  const handleCriticalErrorMethod = useCallback(
    (error: Error | any): ProcessedError => {
      return handleErrorMethod(error, {
        showToast: true,
        propagateToErrorBoundary: true,
        logError: true,
      })
    },
    [handleErrorMethod]
  )

  // 静默错误处理
  const handleErrorSilentlyMethod = useCallback(
    (error: Error | any): ProcessedError => {
      return handleErrorMethod(error, {
        showToast: false,
        logError: true,
      })
    },
    [handleErrorMethod]
  )

  // 清除错误状态
  const clearError = useCallback(() => {
    handleErrorStateChange(null)
  }, [handleErrorStateChange])

  // 重试方法
  const retry = useCallback(() => {
    if (currentError?.canRetry && errorHandlerConfig.onRetry) {
      try {
        errorHandlerConfig.onRetry()
        clearError()
      } catch (retryError) {
        handleErrorMethod(retryError, {
          customMessage: '重试失败，请稍后再试',
        })
      }
    }
  }, [currentError, clearError, handleErrorMethod, errorHandlerConfig])

  // 检查错误类型
  const isErrorType = useCallback(
    (type: ErrorType): boolean => {
      return currentError?.type === type
    },
    [currentError]
  )

  return {
    currentError,
    hasError: currentError !== null,
    handleError: handleErrorMethod,
    handleCriticalError: handleCriticalErrorMethod,
    handleErrorSilently: handleErrorSilentlyMethod,
    clearError,
    retry,
    isErrorType,
  }
}

/**
 * 专用的网络错误处理 Hook
 */
export function useNetworkErrorHandler() {
  return useErrorHandler({
    showToast: true,
    maintainErrorState: true,
    errorResetDelay: 3000,
    customMessage: '网络连接失败，请检查网络后重试',
  })
}

/**
 * 专用的 API 错误处理 Hook
 */
export function useApiErrorHandler() {
  return useErrorHandler({
    showToast: true,
    maintainErrorState: true,
    errorResetDelay: 4000,
    logError: true,
  })
}

/**
 * 专用的表单验证错误处理 Hook
 */
export function useValidationErrorHandler() {
  return useErrorHandler({
    showToast: true,
    maintainErrorState: true,
    errorResetDelay: 6000,
    propagateToErrorBoundary: false,
  })
}

/**
 * 专用的关键业务流程错误处理 Hook
 */
export function useCriticalErrorHandler() {
  return useErrorHandler({
    showToast: true,
    maintainErrorState: true,
    propagateToErrorBoundary: true,
    logError: true,
    errorResetDelay: 0, // 关键错误不自动清除
  })
}

/**
 * React 错误边界辅助 Hook
 * 用于在函数组件中处理错误边界逻辑
 */
export function useErrorBoundary() {
  const [error, setError] = useState<Error | null>(null)

  const resetError = useCallback(() => {
    setError(null)
  }, [])

  const captureError = useCallback((error: Error) => {
    setError(error)
  }, [])

  // 如果有错误，抛出给最近的错误边界
  if (error) {
    throw error
  }

  return {
    captureError,
    resetError,
  }
}

/**
 * 异步操作错误处理 Hook
 * 专门处理 async/await 场景中的错误
 */
export function useAsyncErrorHandler() {
  const { handleError, handleCriticalError } = useErrorHandler()

  const safeAsync = useCallback(
    async <T>(
      asyncFn: () => Promise<T>,
      options: {
        onError?: (error: ProcessedError) => void
        criticalError?: boolean
        customMessage?: string
      } = {}
    ): Promise<T | null> => {
      try {
        return await asyncFn()
      } catch (error) {
        const processedError = options.criticalError
          ? handleCriticalError(error)
          : handleError(error, {
              ...(options.customMessage && { customMessage: options.customMessage }),
            })

        if (options.onError) {
          options.onError(processedError)
        }

        return null
      }
    },
    [handleError, handleCriticalError]
  )

  return { safeAsync }
}

// 默认导出
export default useErrorHandler
