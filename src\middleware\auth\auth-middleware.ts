/**
 * 认证中间件
 * 负责检查用户认证状态，设置认证上下文
 * 不处理业务重定向逻辑，专注于认证状态管理
 */

import { NextRequest, NextResponse } from 'next/server'
import { EnhancedMiddlewareFunction, MiddlewareContext, MiddlewareResult } from '../utils/types'
import { RouteClassifier } from '../utils/route-matcher'
import {
  getUserSession,
  buildAuthContext,
  logAuthAction,
  measureAuthTime,
  hasValidSessionCookie,
} from './auth-utils'

/**
 * 认证中间件主函数
 * 专注于认证状态检查和上下文设置
 */
export const authMiddleware: EnhancedMiddlewareFunction = async (
  request: NextRequest,
  context: MiddlewareContext
): Promise<MiddlewareResult> => {
  const { pathname } = context

  // 跳过不需要认证检查的路由
  if (shouldSkipAuthCheck(pathname)) {
    logAuthAction('跳过认证检查', {
      pathname,
      isAuthenticated: false,
    })
    return null
  }

  try {
    // 执行认证检查（带性能监控）
    const authResult = await measureAuthTime('获取用户会话', () => getUserSession(request))

    // 构建认证上下文
    const authContext = buildAuthContext(authResult)

    // 更新中间件上下文
    Object.assign(context, {
      isAuthenticated: authContext.isAuthenticated,
      session: authContext.session,
      userRole: authContext.userRole,
      isAdmin: authContext.isAdmin,
      // 添加业务相关的上下文信息
      hasCompanyProfile: authContext.hasCompanyProfile,
      organizationRole: authContext.organizationRole,
    })

    // 记录认证结果
    logAuthAction('认证检查完成', {
      pathname,
      isAuthenticated: authContext.isAuthenticated,
      userRole: authContext.userRole || null,
      userId: authContext.session?.user?.id || null,
      error: authResult.error || null,
    })

    // 记录认证阶段的耗时
    if (context.timing) {
      context.timing.auth = Date.now() - (context.timing.auth || Date.now())
    }

    // 认证中间件不处理重定向，只设置上下文
    return null
  } catch (error) {
    console.error('[Auth Middleware] 认证检查失败:', error)

    // 出错时设置默认的未认证状态
    Object.assign(context, {
      isAuthenticated: false,
      session: null,
      userRole: null,
      isAdmin: false,
      hasCompanyProfile: false,
    })

    logAuthAction('认证检查失败', {
      pathname,
      isAuthenticated: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    })

    // 继续执行后续中间件
    return null
  }
}

/**
 * 快速认证检查中间件
 * 只检查 cookie 存在性，不进行网络请求
 * 适用于对性能要求极高的场景
 */
export const fastAuthMiddleware: EnhancedMiddlewareFunction = async (
  request: NextRequest,
  context: MiddlewareContext
): Promise<MiddlewareResult> => {
  const { pathname } = context

  if (shouldSkipAuthCheck(pathname)) {
    return null
  }

  try {
    // 只检查 cookie 格式，不发起网络请求
    const hasValidCookie = hasValidSessionCookie(request)

    // 设置基础认证状态
    Object.assign(context, {
      isAuthenticated: hasValidCookie,
      session: null, // 快速模式不获取完整会话
      userRole: null,
      isAdmin: false,
      hasCompanyProfile: null, // 未知状态
    })

    logAuthAction('快速认证检查', {
      pathname,
      isAuthenticated: hasValidCookie,
    })

    return null
  } catch (error) {
    console.error('[Fast Auth Middleware] 快速认证检查失败:', error)

    Object.assign(context, {
      isAuthenticated: false,
      session: null,
      userRole: null,
      isAdmin: false,
      hasCompanyProfile: false,
    })

    return null
  }
}

/**
 * 管理员权限检查中间件
 * 专门处理管理员路由的认证
 */
export const adminAuthMiddleware: EnhancedMiddlewareFunction = async (
  request: NextRequest,
  context: MiddlewareContext
): Promise<MiddlewareResult> => {
  const { pathname } = context

  // 只处理管理员路由
  if (!RouteClassifier.isAdminOnlyRoute(pathname)) {
    return null
  }

  // 如果还没有进行过认证检查，先执行认证
  if (typeof context.isAuthenticated === 'undefined') {
    await authMiddleware(request, context)
  }

  // 检查管理员权限
  if (!context.isAdmin) {
    logAuthAction('管理员权限不足', {
      pathname,
      isAuthenticated: context.isAuthenticated || false,
      userRole: context.userRole || null,
    })

    // 未登录用户重定向到登录页
    if (!context.isAuthenticated) {
      return NextResponse.redirect(new URL('/login', request.url), 302)
    }

    // 已登录但非管理员用户重定向到主页
    return NextResponse.redirect(new URL('/chat', request.url), 302)
  }

  logAuthAction('管理员权限验证通过', {
    pathname,
    isAuthenticated: true,
    userRole: context.userRole || null,
  })

  return null
}

/**
 * 判断是否应该跳过认证检查
 */
function shouldSkipAuthCheck(pathname: string): boolean {
  return (
    // 静态资源
    RouteClassifier.shouldSkipMiddleware(pathname) ||
    // 公开 API 路由
    RouteClassifier.isPublicRoute(pathname) ||
    // 已经在认证相关页面
    pathname.startsWith('/api/auth/')
  )
}

/**
 * 认证中间件工厂
 * 根据不同需求创建定制的认证中间件
 */
export function createAuthMiddleware(
  options: {
    mode?: 'full' | 'fast' | 'admin-only'
    skipPaths?: string[]
    requireAuth?: boolean
  } = {}
): EnhancedMiddlewareFunction {
  const { mode = 'full', skipPaths = [], requireAuth = false } = options

  return async (request: NextRequest, context: MiddlewareContext): Promise<MiddlewareResult> => {
    const { pathname } = context

    // 检查自定义跳过路径
    if (skipPaths.some(path => pathname.startsWith(path))) {
      return null
    }

    // 根据模式选择中间件
    switch (mode) {
      case 'fast':
        return fastAuthMiddleware(request, context)
      case 'admin-only':
        return adminAuthMiddleware(request, context)
      case 'full':
      default:
        return authMiddleware(request, context)
    }
  }
}

/**
 * 认证状态检查器
 * 提供同步的认证状态检查，基于已设置的上下文
 */
export const authChecker = {
  /**
   * 检查是否已认证
   */
  isAuthenticated(context: MiddlewareContext): boolean {
    return context.isAuthenticated === true
  },

  /**
   * 检查是否为管理员
   */
  isAdmin(context: MiddlewareContext): boolean {
    return context.isAdmin === true
  },

  /**
   * 检查是否完成公司资料
   */
  hasCompanyProfile(context: MiddlewareContext): boolean {
    return (context as any).hasCompanyProfile === true
  },

  /**
   * 检查用户角色
   */
  hasRole(context: MiddlewareContext, role: string): boolean {
    return context.userRole === role
  },

  /**
   * 获取用户ID
   */
  getUserId(context: MiddlewareContext): string | null {
    return (context as any).session?.user?.id || null
  },
}
