'use client'

/**
 * useKeyboardShortcuts - 键盘快捷键管理Hook
 *
 * 基于现代chat应用的快捷键最佳实践：
 * - Enter: 发送消息
 * - Shift+Enter: 换行
 * - Escape: 清空输入或关闭对话框
 * - Ctrl/Cmd+K: 聚焦到输入框
 * - Ctrl/Cmd+L: 清除聊天记录
 * - Ctrl/Cmd+/: 显示快捷键帮助
 */

import { useCallback, useEffect } from 'react'

interface KeyboardShortcutsOptions {
  /** 是否启用快捷键 */
  enabled?: boolean
  /** 发送消息回调 */
  onSend?: () => void
  /** 清空输入回调 */
  onClearInput?: () => void
  /** 聚焦输入框回调 */
  onFocusInput?: () => void
  /** 清除聊天记录回调 */
  onClearChat?: () => void
  /** 显示帮助回调 */
  onShowHelp?: () => void
  /** 是否在开发环境下显示快捷键日志 */
  debug?: boolean
}

interface KeyboardShortcutsResult {
  /** 处理输入框的键盘事件 */
  handleInputKeyDown: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void
  /** 快捷键列表 */
  shortcuts: Array<{
    key: string
    description: string
    action: string
  }>
}

export const useKeyboardShortcuts = ({
  enabled = true,
  onSend,
  onClearInput,
  onFocusInput,
  onClearChat,
  onShowHelp,
  debug = false,
}: KeyboardShortcutsOptions = {}): KeyboardShortcutsResult => {
  // 🎯 快捷键配置
  const shortcuts = [
    { key: 'Enter', description: '发送消息', action: 'send' },
    { key: 'Shift+Enter', description: '换行', action: 'newline' },
    { key: 'Escape', description: '清空输入', action: 'clear' },
    { key: 'Ctrl+K / Cmd+K', description: '聚焦输入框', action: 'focus' },
    { key: 'Ctrl+L / Cmd+L', description: '清除聊天记录', action: 'clearChat' },
    { key: 'Ctrl+/ / Cmd+/', description: '显示快捷键帮助', action: 'help' },
  ]

  // 🎯 检测操作系统
  const isMac = typeof window !== 'undefined' && /Mac|iPod|iPhone|iPad/.test(navigator.platform)

  // 🎯 处理输入框键盘事件
  const handleInputKeyDown = useCallback(
    (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
      if (!enabled) return

      const { key, ctrlKey, metaKey, shiftKey } = e
      const isModifierPressed = isMac ? metaKey : ctrlKey

      if (debug) {
        // eslint-disable-next-line no-console
        console.log('⌨️ Keyboard event:', {
          key,
          ctrlKey,
          metaKey,
          shiftKey,
          isModifierPressed,
          isMac,
        })
      }

      // Enter键：发送消息（不按Shift时）
      if (key === 'Enter' && !shiftKey) {
        e.preventDefault()
        onSend?.()
        return
      }

      // Shift+Enter：换行（默认行为，不需要特殊处理）
      if (key === 'Enter' && shiftKey) {
        // 让默认行为发生（换行）
        return
      }

      // Escape：清空输入
      if (key === 'Escape') {
        e.preventDefault()
        onClearInput?.()
        return
      }

      // Ctrl/Cmd+K：聚焦输入框
      if (key === 'k' && isModifierPressed) {
        e.preventDefault()
        onFocusInput?.()
        return
      }

      // Ctrl/Cmd+L：清除聊天记录
      if (key === 'l' && isModifierPressed) {
        e.preventDefault()
        onClearChat?.()
        return
      }

      // Ctrl/Cmd+/：显示帮助
      if (key === '/' && isModifierPressed) {
        e.preventDefault()
        onShowHelp?.()
        return
      }
    },
    [enabled, onSend, onClearInput, onFocusInput, onClearChat, onShowHelp, isMac, debug]
  )

  // 🎯 全局键盘事件监听
  useEffect(() => {
    if (!enabled) return

    const handleGlobalKeyDown = (e: KeyboardEvent) => {
      const { key, ctrlKey, metaKey, target } = e
      const isModifierPressed = isMac ? metaKey : ctrlKey

      // 避免在输入框内触发全局快捷键
      const isInputElement =
        target instanceof HTMLInputElement ||
        target instanceof HTMLTextAreaElement ||
        (target as HTMLElement)?.contentEditable === 'true'

      if (isInputElement) return

      if (debug) {
        // eslint-disable-next-line no-console
        console.log('⌨️ Global keyboard event:', {
          key,
          ctrlKey,
          metaKey,
          isModifierPressed,
          target: target?.constructor.name,
        })
      }

      // Ctrl/Cmd+K：聚焦输入框
      if (key === 'k' && isModifierPressed) {
        e.preventDefault()
        onFocusInput?.()
        return
      }

      // Ctrl/Cmd+L：清除聊天记录
      if (key === 'l' && isModifierPressed) {
        e.preventDefault()
        onClearChat?.()
        return
      }

      // Ctrl/Cmd+/：显示帮助
      if (key === '/' && isModifierPressed) {
        e.preventDefault()
        onShowHelp?.()
        return
      }
    }

    document.addEventListener('keydown', handleGlobalKeyDown)

    return () => {
      document.removeEventListener('keydown', handleGlobalKeyDown)
    }
  }, [enabled, onFocusInput, onClearChat, onShowHelp, isMac, debug])

  return {
    handleInputKeyDown,
    shortcuts,
  }
}
