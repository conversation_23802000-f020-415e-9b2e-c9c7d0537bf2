{
  "extends": ["next/core-web-vitals", "prettier"],
  "rules": {
    // 基础规则
    "prefer-const": "error",
    "no-var": "error",
    "no-console": "warn",
    "no-debugger": "error",
    "no-unused-vars": [
      "warn",
      {
        "argsIgnorePattern": "^_",
        "varsIgnorePattern": "^_"
      }
    ],

    // React规则
    "react/jsx-uses-react": "off",
    "react/react-in-jsx-scope": "off",
    "react/prop-types": "off",
    "react-hooks/exhaustive-deps": "error"
  },
  "ignorePatterns": [
    "node_modules/",
    ".next/",
    "out/",
    "build/",
    "dist/",
    "public/*.html",
    "docs/",
    "migration-docs/",
    "**.md",
    "**/README*",
    "**/Todo*",
    "**/Dockerfile*",
    "**/docker-compose*",
    "**/.dockerignore",
    "**/deploy.sh",
    "**/quick_deploy.sh",
    "**/build_push_image.sh",
    "**/package-lock.json",
    "**/pnpm-lock.yaml",
    "**/tsconfig.tsbuildinfo",
    "**/.cursor/",
    "**/.vscode/",
    "**/.idea/",
    "**/*.log",
    "**/coverage/"
  ]
}
