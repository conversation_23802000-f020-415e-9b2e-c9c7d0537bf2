/**
 * Better Auth 客户端配置
 * 基于官方文档：https://www.better-auth.com/docs/concepts/client
 * 适用于 Next.js 前端项目
 */

import { createAuthClient } from 'better-auth/react'

// 获取正确的baseURL，确保在开发环境中使用完整URL
const getBaseURL = () => {
  if (process.env.NEXT_PUBLIC_AUTH_URL) {
    return process.env.NEXT_PUBLIC_AUTH_URL
  }

  // 在客户端环境中，使用当前域名构建完整URL
  if (typeof window !== 'undefined') {
    return `${window.location.origin}/api/auth`
  }

  // 服务端环境默认值（开发环境）
  return 'http://localhost:3000/api/auth'
}

export const authClient = createAuthClient({
  baseURL: getBaseURL(),
  fetchOptions: {
    credentials: 'include',
  },
})

/**
 * 导出所有官方 API 方法，供直接使用
 */
export const {
  // React Hooks (仅在 React 组件中使用) - 重命名避免冲突
  useSession: useBetterAuthSession,

  // 认证方法 (可在任何地方使用)
  signUp,
  signIn,
  signOut,
  getSession,

  // 用户管理
  updateUser,

  // 邮箱相关
  sendVerificationEmail,
  verifyEmail,
  forgetPassword,
  resetPassword,

  // 错误代码常量
  $ERROR_CODES,
} = authClient

/**
 * 便利工具函数 (可选使用)
 */
export const authUtils = {
  /**
   * 检查是否已认证
   */
  isAuthenticated: async (): Promise<boolean> => {
    const { data } = await authClient.getSession()
    return !!data?.session
  },

  /**
   * 获取当前用户
   */
  getCurrentUser: async () => {
    const { data } = await authClient.getSession()
    return data?.user || null
  },

  /**
   * 获取友好的错误消息
   */
  getErrorMessage: (error: any, locale = 'zh-CN'): string => {
    if (!error) return ''

    const errorMessages: Record<string, Record<string, string>> = {
      INVALID_EMAIL_OR_PASSWORD: {
        'zh-CN': '邮箱或密码错误',
        en: 'Invalid email or password',
      },
      USER_ALREADY_EXISTS: {
        'zh-CN': '用户已存在',
        en: 'User already exists',
      },
      WEAK_PASSWORD: {
        'zh-CN': '密码强度不足',
        en: 'Password is too weak',
      },
      EMAIL_NOT_VERIFIED: {
        'zh-CN': '邮箱未验证',
        en: 'Email not verified',
      },
      RATE_LIMIT_EXCEEDED: {
        'zh-CN': '请求过于频繁，请稍后再试',
        en: 'Too many requests, please try again later',
      },
    }

    // 如果错误有 code 属性，使用对应的消息
    if (error.code && errorMessages[error.code]) {
      return errorMessages[error.code][locale] || errorMessages[error.code]['en']
    }

    // 返回原始错误消息
    return error.message || (locale === 'zh-CN' ? '未知错误' : 'Unknown error')
  },
}

// 导出类型定义（使用 Better Auth 的类型推断）
export type Session = typeof authClient.$Infer.Session
export type User = (typeof authClient.$Infer.Session)['user']

// 默认导出客户端实例，供直接使用官方API
export default authClient
