/**
 * 问卷配置文件
 * 定义问卷的问题、选项和流程
 */

import { PollQuestion, PollStep } from '@/types/poll'

/**
 * 问卷问题配置
 */
export const POLL_QUESTIONS: PollQuestion[] = [
  {
    step: PollStep.COMPANY_TYPE,
    questionKey: 'companyType',
    options: [
      { value: 'electronics', labelKey: 'poll.questions.companyType.options.electronics' },
      { value: 'dairy', labelKey: 'poll.questions.companyType.options.dairy' },
      { value: 'automotive', labelKey: 'poll.questions.companyType.options.automotive' },
      { value: 'fruit', labelKey: 'poll.questions.companyType.options.fruit' },
      { value: 'autoParts', labelKey: 'poll.questions.companyType.options.autoParts' },
    ],
  },
  {
    step: PollStep.REGION,
    questionKey: 'region',
    options: [
      { value: 'vietnam', labelKey: 'poll.questions.region.options.vietnam' },
      { value: 'japan', labelKey: 'poll.questions.region.options.japan' },
      { value: 'korea', labelKey: 'poll.questions.region.options.korea' },
      { value: 'southeastAsia', labelKey: 'poll.questions.region.options.southeastAsia' },
      {
        value: 'southeastAsiaRegion',
        labelKey: 'poll.questions.region.options.southeastAsiaRegion',
      },
    ],
  },
  {
    step: PollStep.BUSINESS_MODE,
    questionKey: 'businessMode',
    options: [
      { value: 'policyChange', labelKey: 'poll.questions.businessMode.options.policyChange' },
      { value: 'marketTrend', labelKey: 'poll.questions.businessMode.options.marketTrend' },
      { value: 'acquisition', labelKey: 'poll.questions.businessMode.options.acquisition' },
      { value: 'operatingData', labelKey: 'poll.questions.businessMode.options.operatingData' },
      {
        value: 'industryInvestment',
        labelKey: 'poll.questions.businessMode.options.industryInvestment',
      },
    ],
  },
  {
    step: PollStep.SPECIFIC_NEEDS,
    questionKey: 'specificNeeds',
    options: [
      { value: 'uncertain', labelKey: 'poll.questions.specificNeeds.options.uncertain' },
      { value: 'preparing', labelKey: 'poll.questions.specificNeeds.options.preparing' },
      { value: 'confirmed', labelKey: 'poll.questions.specificNeeds.options.confirmed' },
      { value: 'expanding', labelKey: 'poll.questions.specificNeeds.options.expanding' },
    ],
  },
]

/**
 * 根据步骤获取问题配置
 */
export function getQuestionByStep(step: PollStep): PollQuestion | undefined {
  return POLL_QUESTIONS.find(q => q.step === step)
}

/**
 * 获取问题标题的翻译键
 */
export function getQuestionTitleKey(questionKey: string): string {
  return `poll.questions.${questionKey}.title`
}

/**
 * 问卷总步数
 */
export const TOTAL_STEPS = POLL_QUESTIONS.length

/**
 * 检查是否为最后一步
 */
export function isLastStep(step: PollStep): boolean {
  return step === PollStep.SPECIFIC_NEEDS
}

/**
 * 获取下一步
 */
export function getNextStep(currentStep: PollStep): PollStep | null {
  if (currentStep < PollStep.SPECIFIC_NEEDS) {
    return (currentStep + 1) as PollStep
  }
  return null
}

/**
 * 获取上一步
 */
export function getPreviousStep(currentStep: PollStep): PollStep | null {
  if (currentStep > PollStep.COMPANY_TYPE) {
    return (currentStep - 1) as PollStep
  }
  return null
}
