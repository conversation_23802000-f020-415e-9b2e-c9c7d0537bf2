# 🚀 多AI对话管理界面开发计划 (基于现有架构)

📋 项目架构
现有架构

- 国际化系统完整 - 使用next-intl without routing，支持中英日三语
- 认证系统成熟 - Better Auth + session管理
- UI组件库完整 - shadcn/ui全套组件已配置
- HTTP请求系统 - axios + 认证拦截器 + API代理
- 状态管理就绪 - Zustand + React Query
- 实时通信支持 - Socket.IO Client
  技术选型
  {
  "核心依赖": {
  "ai": "^3.3.0",
  "@ai-sdk/react": "^0.0.x",
  "react-hook-form": "^7.48.0",
  "zod": "^3.22.0",
  "react-pdf": "^7.5.0",
  "react-window": "^1.8.8",
  "react-markdown": "^9.0.0"
  }

🏗️ 第一阶段：核心架构搭建 (2-3

1. 状态管理
   创建 Zustand stores：
   // src/stores/chat-store.ts
   interface ChatState {
   currentTopicId: string | null;
   messages: ChatMessage[];
   isLoading: boolean;
   streamingMessage: string;
   // ... 其他状态

// src/stores/topics-store.ts  
interface TopicsState {
topics: ChatTopic[];
selectedTopic: ChatTopic | null;
filter: 'all' | 'starred' | 'recent';
searchQuery: string;
// ... 其他状态

2. 类型定义
   // src/lib/types/chat.ts
   interface ChatTopic {
   id: string;
   title: string;
   description?: string;
   isStarred: boolean;
   createdAt: Date;
   updatedAt: Date;
   messageCount: number;
   lastMessage?: string;
   tags: string[];

interface ChatMessage {
id: string;
topicId: string;
content: string;
role: 'user' | 'assistant';
type: 'text' | 'checkpoint' | 'report';
metadata?: MessageMetadata;
createdAt: Date;
isStreaming?: boolean;

3. API服务
   基于现有的 axios 配置，创建聊天相关API：
   // src/lib/api/chat.ts
   export const chatAPI = {
   getTopics: () => get<ApiResponse<ChatTopic[]>>('/proxy/chat/topics'),
   createTopic: (data: CreateTopicData) => post<ApiResponse<ChatTopic>>('/proxy/chat/topics', data),
   sendMessage: (topicId: string, message: string) => post<ApiResponse<ChatMessage>>('/proxy/chat/send', { topicId, message }),
   // ... 其他API

🎨 第二阶段：核心组件开发 (3-4 4. 聊天主界面
替换现有的 src/app/(dashboard)/chat/page.tsx：
// 聊天界面现在是单栏布局，主题侧边栏已整合到全局的 AppSidebar 中
export default function ChatPage() {
return (
<div className="h-full">
<ChatInterface className="h-full" />
</div>
)

5. 主题管理功能 (集成于 AppSidebar)
   // 主题管理功能将被整合到全局侧边栏 src/components/common/app-sidebar.tsx 中
   // 以下组件将构成主题管理的核心功能
   // src/components/chat/topic-sidebar.tsx
   interface TopicSidebarProps {
   className?: string;

export function TopicSidebar({ className }: TopicSidebarProps) {
const { topics, selectedTopic, filter, searchQuery } = useTopicsStore();

return (
<div className={cn("flex flex-col h-full", className)}>
<TopicHeader />
<TopicSearch />
<TopicFilter />
<TopicList />
</div>
);

6. 聊天界面核心
   // src/components/chat/chat-interface.tsx
   export function ChatInterface({ className }: ChatInterfaceProps) {
   const { currentTopicId, messages, isLoading } = useChatStore();

return (
<div className={cn("flex flex-col", className)}>
<ChatHeader />
<MessageList className="flex-1" />
<MessageInput />
</div>
);

7. Vercel AI SDK
   // src/app/api/chat/route.ts
   export async function POST(req: Request) {
   const { messages, topicId } = await req.json();

// 转发到后端服务
const response = await fetch(`${process.env.BACKEND_URL}/chat/stream`, {
method: 'POST',
headers: {
'Content-Type': 'application/json',
'Authorization': `Bearer ${getAuthToken(req)}`,
},
body: JSON.stringify({ messages, topicId }),
});

return new Response(response.body, {
headers: { 'Content-Type': 'text/plain' },
});

🔧 第三阶段：高级功能开发 (2-3 8. 动态表单系统 (Checkpoin
// src/components/chat/checkpoint-renderer.tsx
interface CheckpointRendererProps {
schema: JSONSchema;
onSubmit: (values: Record<string, any>) => void;

export function CheckpointRenderer({ schema, onSubmit }: CheckpointRendererProps) {
// 基于JSON Schema动态生成表单
// 支持slider、select、input等shadcn/ui组件

9. 流式消息
   // src/components/chat/streaming-message.tsx
   export function StreamingMessage({ message }: { message: ChatMessage }) {
   const { messages, input, handleSubmit, isLoading } = useChat({
   api: '/api/chat',
   initialMessages: [message],
   });

return (
<div className="message-container">
<TypewriterEffect content={message.content} />
{message.type === 'checkpoint' && (
<CheckpointRenderer schema={message.metadata?.checkpointData?.schema} />
)}
</div>
);

10. 多模态

- 文件上传组件
- 图片预览功能
- 语音输入(可
  🎯 第四阶段：用户体验优化 (1-2

1. 国际化
   利用现有的 next-intl 系统，为聊天界面添加多语言支持：
   // src/lib/i18n/messages/zh.json
   {
   "chat": {
   "newTopic": "新建主题",
   "searchPlaceholder": "搜索对话...",
   "sendMessage": "发送消息",
   "typing": "正在输入...",
   "starred": "收藏的对话",
   "recent": "最近对话"
   }

2. 性能

- 使用 React.memo 优化组件渲染
- 实现虚拟滚动 (react-window)
- 消息分页加载
- 图片懒

1. 响应式

- 移动端适配
- 侧边栏折叠功能
- 触摸手势
  📁 最终目录
  src/
  ├── app/
  │ ├── (dashboard)/
  │ │ ├── chat/
  │ │ │ ├── page.tsx # 主聊天界面
  │ │ │ └── [topicId]/page.tsx # 特定主题页面
  │ │ └── layout.tsx
  │ └── api/
  │ └── chat/route.ts # Vercel AI SDK 代理
  ├── components/chat/
  │ ├── topic-sidebar.tsx # 主题侧边栏 (集成于AppSidebar)
  │ ├── topic-header.tsx # 主题头部
  │ ├── topic-list.tsx # 主题列表
  │ ├── topic-search.tsx # 主题搜索
  │ ├── chat-interface.tsx # 聊天界面
  │ ├── chat-header.tsx # 聊天头部
  │ ├── message-list.tsx # 消息列表
  │ ├── message-item.tsx # 消息项
  │ ├── message-input.tsx # 消息输入
  │ ├── streaming-message.tsx # 流式消息
  │ ├── checkpoint-renderer.tsx # 动态表单
  │ └── file-upload.tsx # 文件上传
  ├── lib/
  │ ├── api/
  │ │ ├── chat.ts # 聊天API
  │ │ └── topics.ts # 主题API
  │ ├── types/
  │ │ └── chat.ts # 聊天类型定义
  │ └── utils/
  │ └── chat-utils.ts # 聊天工具函数
  ├── stores/
  │ ├── chat-store.ts # 聊天状态管理
  │ └── topics-store.ts # 主题状态管理
  └── hooks/
  ├── use-chat.ts # 聊天Hook
  └── use-topics.ts # 主题Ho
  🔑 关键实现

1. 与现有架构的完美

- 复用现有的认证系统和用户session
- 使用现有的HTTP请求拦截器
- 遵循现有的API代理模式
- 保持现有的国际化

1. 状态管理

- 使用 Zustand 管理聊天和主题状态
- 结合 React Query 进行数据缓存
- 实现乐观更新提升用户

1. 流式响应

- 通过 Vercel AI SDK 的 useChat Hook
- API 代理层转发到后端服务
- 前端实现打字机

1. 多语言

- 利用现有的 next-intl 配置
- 支持界面元素的动态语言切换
- 保持用户语言偏好
  这个计划充分利用了现有架构的优势，避免了重复建设，同时确保了与现有系统的完美集
