import { useState } from 'react'
import { But<PERSON> } from '../ui/button'
import { Dialog, DialogContent } from '../ui/dialog'
import { <PERSON>, MousePointer } from 'lucide-react'
import Image from 'next/image'
import riskIcon from '@/assets/risk.png'
import marketIcon from '@/assets/market.png'
import businessIcon from '@/assets/business.png'

interface ProjectCreationDialogProps {
  isOpen: boolean
  onClose: () => void
}

const ProjectCreationDialog = ({ isOpen, onClose }: ProjectCreationDialogProps) => {
  const handleJoinProject = () => {
    console.log("Joining project group...")
    // 这里可以添加加入项目的逻辑
  }

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent 
        className="!max-w-none !max-h-none border-0 p-0"
        style={{ 
          width: '833px', 
          height: '422px', 
          background: '#F1F5F9',
          boxShadow: '0px 0px 0.5px 0px #42474C80, 0px 4px 8px 0px #42474C1A, 0px 4px 40px 0px #EEEEEE'
        }}
      >
        {/* Close button */}
        <Button 
          variant="ghost" 
          size="icon" 
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 z-10"
          onClick={onClose}
        >
          <X className="h-5 w-5" />
        </Button>

        {/* Content container */}
        <div className="h-full flex items-center justify-center">
          <div 
            className="rounded-xl p-6"
            style={{ width: '400px', height: '344px', background: 'white' }}
          >
            {/* Avatar row */}
            <div className="flex justify-center items-center gap-8 mb-8">
              {/* Avatar 1 - 风险risk */}
              <div className="flex flex-col items-center">
                <div className="w-25 h-25 mb-3">
                  <Image 
                    src={riskIcon} 
                    alt="风险risk" 
                    width={100} 
                    height={100} 
                  />
                </div>
                <span className="text-sm text-gray-700 font-medium">风险risk</span>
              </div>

              {/* Avatar 2 - 商机owen */}
              <div className="flex flex-col items-center">
                <div className="w-25 h-25 mb-3">
                  <Image 
                    src={businessIcon} 
                    alt="商机owen" 
                    width={100} 
                    height={100} 
                  />
                </div>
                <span className="text-sm text-gray-700 font-medium">商机owen</span>
              </div>

              {/* Avatar 3 - 市场mark */}
              <div className="flex flex-col items-center">
                <div className="w-25 h-25 mb-3">
                  <Image 
                    src={marketIcon} 
                    alt="市场mark" 
                    width={100} 
                    height={100} 
                  />
                </div>
                <span className="text-sm text-gray-700 font-medium">市场mark</span>
              </div>
            </div>

            {/* Title */}
            <div className="mb-6 text-center">
              <h2 className="text-gray-800 font-medium text-base leading-relaxed">
                正在创建项目群聊
              </h2>
            </div>

            {/* Join button */}
            <div className="flex justify-center">
              <Button
                className="bg-gray-600 hover:bg-gray-700 text-white px-8 py-2 rounded-full text-sm font-medium transition-colors"
                onClick={handleJoinProject}
              >
                建群中....
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default ProjectCreationDialog