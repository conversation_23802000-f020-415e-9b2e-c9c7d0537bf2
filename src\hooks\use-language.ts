'use client'

import { useState } from 'react'
import { useSession } from '@/components/providers/session-provider'
import { localeNames, backendLanguageMap, type Locale } from '@/lib/i18n/config'

interface UseLanguageReturn {
  currentLanguage: string | null
  currentLocale: Locale | null
  availableLanguages: Record<string, string>
  isChanging: boolean
  changeLanguage: (language: string) => Promise<void>
  error: string | null
}

/**
 * 语言切换Hook
 * 提供语言切换功能和状态管理
 */
export function useLanguage(): UseLanguageReturn {
  const { user, changeLanguage: sessionChangeLanguage } = useSession()
  const [isChanging, setIsChanging] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // 获取当前用户语言
  const currentLanguage = (user as any)?.language || null

  // 获取当前locale
  const currentLocale = currentLanguage
    ? (Object.entries(backendLanguageMap).find(
        ([_, lang]) => lang === currentLanguage
      )?.[0] as Locale) || null
    : null

  // 可用语言列表（后端语言字段 -> 显示名称）
  const availableLanguages = {
    chinese: '中文',
    english: 'English',
    japanese: '日本語',
  }

  // 切换语言方法
  const changeLanguage = async (language: string) => {
    if (language === currentLanguage) {
      console.log('语言未发生变化，跳过切换')
      return
    }

    setIsChanging(true)
    setError(null)

    try {
      // 调用SessionProvider中的changeLanguage方法
      await sessionChangeLanguage(language)
      console.log(`✅ 语言切换成功: ${currentLanguage} → ${language}`)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '语言切换失败'
      setError(errorMessage)
      console.error('❌ 语言切换失败:', err)
      throw err
    } finally {
      setIsChanging(false)
    }
  }

  return {
    currentLanguage,
    currentLocale,
    availableLanguages,
    isChanging,
    changeLanguage,
    error,
  }
}
