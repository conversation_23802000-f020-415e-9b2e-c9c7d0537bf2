/**
 * WebSocket 消息类型定义
 * 设计原则：Python 后端只提供数据，前端处理所有业务逻辑
 */

// ============================================================================
// 基础 WebSocket 消息接口 (极简版)
// ============================================================================

export interface BaseWebSocketMessage {
  // 消息唯一标识 - 前端生成的UI ID (必填)
  id: string

  // 群聊标识
  groupChatId: string
  // 会话标识
  sessionId: string
  // 用户唯一标识
  userId: string
  // 公司/组织标识
  organizationId: string

  payload: MessagePayload

  // 错误信息 (可选, 当 messageType 为 'error' 时出现)
  error?: string

  // 消息时间戳
  timestamp: string

  // UI相关元数据 - 前端管理
  metadata?: {
    streamingState?: {
      isComplete: boolean
      accumulatedText?: string
    }
    [key: string]: any
  }

  // 消息状态 - UI显示状态
  status?: 'sending' | 'sent' | 'delivered' | 'failed'
}

// ============================================================================
// Payload 联合类型定义
// ============================================================================

export type MessagePayload = StreamingPayload | CheckpointPayload | ReportPayload | ErrorPayload

// ============================================================================
// 1. 流式内容输出 Payload (极简版)
// ============================================================================

export interface StreamingPayload {
  type: 'streaming'
  delta: string // 当前文本片段
  isComplete: boolean // 是否完成
}

// ============================================================================
// 2. 用户表单交互 Payload (极简版)
// ============================================================================

export interface CheckpointPayload {
  type: 'checkpoint'
  fields: FormField[] // Python 只提供字段数据
}

// 表单字段定义 (极简版)
export interface FormField {
  id: string // 字段ID
  label: string // 字段标签
  type:
    | 'text'
    | 'textarea'
    | 'select'
    | 'multiselect'
    | 'radio'
    | 'checkbox'
    | 'number'
    | 'date'
    | 'file'
  required: boolean // 是否必填
  defaultValue?: any // 默认值
  options?: FormFieldOption[] // 选项数据 (仅限选择类型字段)
}

// 表单字段选项 (极简版)
export interface FormFieldOption {
  label: string // 显示文本
  value: any // 选项值
}

// ============================================================================
// 3. 最终报告展示 Payload (极简版)
// ============================================================================

export interface ReportPayload {
  type: 'report'
  content: string // 报告内容 (Markdown 格式)
}

// ============================================================================
// 4. 错误信息 Payload
// ============================================================================

export interface ErrorPayload {
  type: 'error'
  code: string // 错误代码
  message: string // 错误描述
}

// ============================================================================
// 5. 文件上传 Payload
// ============================================================================

export interface FilePayload {
  type: 'file'
  files: FileInfo[] // 文件信息列表
  context?: string // 文件上下文描述
}

// 文件信息定义
export interface FileInfo {
  id: string // 文件唯一标识
  name: string // 文件名
  size: number // 文件大小（字节）
  type: string // MIME 类型
  url?: string // 文件 URL（如果已上传）
  preview?: string // 预览内容（如果适用）
  metadata?: Record<string, any> // 额外元数据
}

// ============================================================================
// 工作流步骤枚举
// ============================================================================

export enum WorkflowStep {
  QUERY_PARSING_START = 'query_parsing_start',
  COUNTRY_CLASSIFICATION = 'country_classification',
  OPPORTUNITY_DIMENSION_SELECTION = 'opportunity_dimension_selection',
  DATA_COLLECTION = 'data_collection',
  ANALYSIS_PROCESSING = 'analysis_processing',
  REPORT_GENERATION = 'report_generation',
  WORKFLOW_COMPLETED = 'workflow_completed',
}

// ============================================================================
// 消息类型枚举
// ============================================================================

export enum WebSocketMessageType {
  STREAMING = 'streaming',
  CHECKPOINT = 'checkpoint',
  REPORT = 'report',
  ERROR = 'error',
}

// ============================================================================
// 类型保护函数
// ============================================================================

export function isStreamingPayload(payload: MessagePayload): payload is StreamingPayload {
  return payload.type === 'streaming'
}

export function isCheckpointPayload(payload: MessagePayload): payload is CheckpointPayload {
  return payload.type === 'checkpoint'
}

export function isReportPayload(payload: MessagePayload): payload is ReportPayload {
  return payload.type === 'report'
}

export function isErrorPayload(payload: MessagePayload): payload is ErrorPayload {
  return payload.type === 'error'
}

// ============================================================================
// 实用工具类型
// ============================================================================

export type StreamingMessage = BaseWebSocketMessage & { payload: StreamingPayload }
export type CheckpointMessage = BaseWebSocketMessage & { payload: CheckpointPayload }
export type ReportMessage = BaseWebSocketMessage & { payload: ReportPayload }
export type ErrorMessage = BaseWebSocketMessage & { payload: ErrorPayload }
