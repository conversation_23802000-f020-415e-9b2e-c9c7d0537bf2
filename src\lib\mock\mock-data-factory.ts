/**
 * Mock数据工厂 - 完全遵循BaseWebSocketMessage标准
 * 设计原则：严格按照websocket-event-type.ts定义生成Mock数据
 * 重要：StreamingPayload只有delta和isComplete，前端负责累积文本
 */

import type {
  BaseWebSocketMessage,
  StreamingPayload,
  CheckpointPayload,
  ReportPayload,
  ErrorPayload,
  FormField,
  FormFieldOption,
  CheckpointMessage,
  ReportMessage,
  ErrorMessage,
} from '@/types/websocket-event-type'

// ============================================================================
// 基础Mock配置
// ============================================================================

export interface MockConfig {
  groupChatId?: string | undefined
  sessionId?: string | undefined
  userId?: string | undefined
  organizationId?: string | undefined
}

// 🎯 定义专门的AI助手ID常量，确保全局一致性
export const MOCK_AI_ASSISTANT_ID = 'mock-assistant-bot'

const DEFAULT_MOCK_CONFIG = {
  groupChatId: 'mock-group-001',
  sessionId: 'mock-session-001',
  userId: MOCK_AI_ASSISTANT_ID, // 使用常量确保一致性
  organizationId: 'mock-org-001',
}

// ============================================================================
// Mock数据工厂类
// ============================================================================

export class MockDataFactory {
  /**
   * 创建基础WebSocket消息结构
   */
  static createBaseMessage(config?: MockConfig): Omit<BaseWebSocketMessage, 'payload'> {
    // 过滤掉 undefined 值，避免覆盖默认配置
    const filteredConfig = config ? Object.fromEntries(
      Object.entries(config).filter(([_, value]) => value !== undefined)
    ) : {}
    const finalConfig = { ...DEFAULT_MOCK_CONFIG, ...filteredConfig }

    // 🔍 调试信息：检查配置传递
    if (process.env.NODE_ENV === 'development') {
      console.log('🏭 MockDataFactory.createBaseMessage:', {
        inputConfig: config,
        finalConfig,
        userId: finalConfig.userId,
      })
    }

    return {
      id: this.generateId('msg'),
      groupChatId: finalConfig.groupChatId,
      sessionId: finalConfig.sessionId,
      userId: finalConfig.userId,
      organizationId: finalConfig.organizationId,
      timestamp: new Date().toISOString(),
    }
  }

  /**
   * 🎯 专门创建AI助手消息的方法，确保始终使用正确的助手ID
   */
  static createAssistantBaseMessage(config?: Omit<MockConfig, 'userId'>): Omit<BaseWebSocketMessage, 'payload'> {
    // 强制使用AI助手ID，忽略传入的userId配置
    const assistantConfig: MockConfig = {
      ...config,
      userId: MOCK_AI_ASSISTANT_ID, // 强制使用AI助手ID
    }

    const baseMessage = this.createBaseMessage(assistantConfig)

    // 🔍 调试信息：确认助手消息ID
    if (process.env.NODE_ENV === 'development') {
      console.log('🤖 MockDataFactory.createAssistantBaseMessage:', {
        messageId: baseMessage.id,
        userId: baseMessage.userId,
        isAssistant: baseMessage.userId === MOCK_AI_ASSISTANT_ID,
      })
    }

    return baseMessage
  }

  // ============================================================================
  // 流式消息工厂
  // ============================================================================

  /**
   * 创建标准流式消息 - 严格遵循StreamingPayload定义  
   * 🎯 优化：默认创建AI助手流式消息，除非明确指定为用户消息
   */
  static createStreamingMessage(options: {
    delta: string // 🎯 当前文本片段（必需）
    isComplete: boolean // 🎯 是否完成（必需）
    config?: MockConfig | undefined
    isAssistantMessage?: boolean | undefined // 🎯 新增：是否为助手消息（默认true）
  }): BaseWebSocketMessage {
    const { isAssistantMessage = true } = options

    // 🎯 根据消息类型选择创建方法
    const baseMessage = isAssistantMessage 
      ? this.createAssistantBaseMessage(options.config)
      : this.createBaseMessage(options.config)

    // 🎯 关键：StreamingPayload只有delta和isComplete，没有accumulated
    const payload: StreamingPayload = {
      type: 'streaming',
      delta: options.delta,
      isComplete: options.isComplete,
    }

    return {
      ...baseMessage,
      payload,
    }
  }

  /**
   * 创建流式文本序列 - 🎯 正确设计：基于isComplete字段判断累积
   * 关键：只有最后一条消息的isComplete=true，其他都是false
   * 设计原则：每个chunk都是独立消息，前端通过isComplete判断气泡合并
   */
  static createStreamingSequence(
    text: string,
    chunkSize: number = 3,
    config?: MockConfig
  ): BaseWebSocketMessage[] {
    const messages: BaseWebSocketMessage[] = []

    // 🎯 生成统一的streamingGroupId用于调试和追踪
    const streamingGroupId = this.generateId('streaming-group')
    const baseTimestamp = new Date().toISOString()

    // 将文本分割成块
    const chunks = this.splitTextIntoChunks(text, chunkSize)

    chunks.forEach((chunk, index) => {
      const isComplete = index === chunks.length - 1 // 🎯 只有最后一块是complete

      // 🎯 正确设计：每个chunk都是独立的消息（有自己的ID）
      const message = this.createStreamingMessage({
        delta: chunk,
        isComplete,
        ...(config && { config }),
      })

      // 🎯 为同一序列的消息添加grouping信息用于调试
      message.metadata = {
        ...message.metadata,
        streamingGroup: {
          groupId: streamingGroupId,
          chunkIndex: index,
          totalChunks: chunks.length,
          isFirstChunk: index === 0,
          isLastChunk: index === chunks.length - 1,
        }
      }

      // 🎯 使用相同的基础时间戳，但添加微小偏移确保顺序
      message.timestamp = new Date(new Date(baseTimestamp).getTime() + index).toISOString()

      messages.push(message)
    })

    // 🎯 调试信息
    if (process.env.NODE_ENV === 'development') {
      console.log('🏭 Created streaming sequence:', {
        groupId: streamingGroupId,
        totalChunks: chunks.length,
        text: text.substring(0, 30) + '...'
      })
    }

    return messages
  }

  // ============================================================================
  // 表单消息工厂
  // ============================================================================

  /**
   * 创建表单消息 - 🎯 优化：使用助手消息创建方法
   */
  static createCheckpointMessage(fields: FormField[], config?: MockConfig): BaseWebSocketMessage {
    const baseMessage = this.createAssistantBaseMessage(config)

    const payload: CheckpointPayload = {
      type: 'checkpoint',
      fields: fields.map(field => ({ ...field })), // 深拷贝
    }

    return {
      ...baseMessage,
      payload,
    }
  }

  /**
   * 创建简单表单
   */
  static createSimpleForm(config?: MockConfig): BaseWebSocketMessage {
    const fields: FormField[] = [
      {
        id: 'name',
        label: '姓名',
        type: 'text',
        required: true,
        defaultValue: '',
      },
      {
        id: 'email',
        label: '邮箱',
        type: 'text',
        required: true,
        defaultValue: '',
      },
      {
        id: 'message',
        label: '留言',
        type: 'textarea',
        required: false,
        defaultValue: '',
      },
    ]

    return this.createCheckpointMessage(fields, config)
  }

  /**
   * 创建复杂表单
   */
  static createComplexForm(config?: MockConfig): BaseWebSocketMessage {
    const fields: FormField[] = [
      {
        id: 'company',
        label: '公司名称',
        type: 'text',
        required: true,
        defaultValue: '',
      },
      {
        id: 'industry',
        label: '行业类型',
        type: 'select',
        required: true,
        options: [
          { label: '科技互联网', value: 'tech' },
          { label: '金融服务', value: 'finance' },
          { label: '制造业', value: 'manufacturing' },
          { label: '零售电商', value: 'retail' },
          { label: '其他', value: 'other' },
        ],
      },
      {
        id: 'size',
        label: '公司规模',
        type: 'radio',
        required: true,
        options: [
          { label: '1-50人', value: 'small' },
          { label: '51-200人', value: 'medium' },
          { label: '201-1000人', value: 'large' },
          { label: '1000+人', value: 'enterprise' },
        ],
      },
      {
        id: 'services',
        label: '感兴趣的服务',
        type: 'checkbox',
        required: false,
        options: [
          { label: '数据分析', value: 'analytics' },
          { label: '市场调研', value: 'research' },
          { label: '商业咨询', value: 'consulting' },
          { label: '技术支持', value: 'support' },
        ],
      },
      {
        id: 'budget',
        label: '预算范围（万元）',
        type: 'number',
        required: false,
        defaultValue: 10,
      },
      {
        id: 'timeline',
        label: '期望完成时间',
        type: 'date',
        required: false,
      },
    ]

    return this.createCheckpointMessage(fields, config)
  }

  // ============================================================================
  // 报告消息工厂
  // ============================================================================

  /**
   * 创建报告消息 - 🎯 优化：使用助手消息创建方法
   */
  static createReportMessage(content: string, config?: MockConfig): BaseWebSocketMessage {
    const baseMessage = this.createAssistantBaseMessage(config)

    const payload: ReportPayload = {
      type: 'report',
      content,
    }

    return {
      ...baseMessage,
      payload,
    }
  }

  /**
   * 创建简短报告
   */
  static createShortReport(config?: MockConfig): BaseWebSocketMessage {
    const content = `# 分析报告摘要

## 执行概览
本次分析已成功完成，数据质量良好，结果可信度高。

## 关键发现
- **数据完整性**: 98.5%
- **处理效率**: 优秀  
- **准确率**: 94.2%

## 主要建议
1. 持续优化数据收集流程
2. 加强质量监控机制
3. 定期评估分析模型

---
*报告生成时间: ${new Date().toLocaleString()}*`

    return this.createReportMessage(content, config)
  }

  /**
   * 创建详细报告
   */
  static createDetailedReport(config?: MockConfig): BaseWebSocketMessage {
    const content = `# 综合分析报告

## 1. 概述

本报告基于最新收集的数据进行深度分析，涵盖多个维度的关键指标和趋势洞察。

## 2. 数据分析

### 2.1 数据质量评估

| 指标 | 数值 | 状态 |
|------|------|------|
| 完整性 | 98.5% | ✅ 优秀 |
| 准确性 | 94.2% | ✅ 良好 |
| 及时性 | 97.8% | ✅ 优秀 |
| 一致性 | 96.1% | ✅ 良好 |

### 2.2 趋势分析

\`\`\`javascript
const trendData = {
  growth: "+15.3%",
  period: "Q4 2024",
  confidence: "high"
}
\`\`\`

## 3. 关键洞察

### 3.1 市场表现
- 整体市场表现稳定向好
- 用户活跃度持续提升
- 转化率超出预期

### 3.2 用户行为
- 移动端使用率占比 68%
- 平均会话时长增长 12%
- 用户满意度评分 4.2/5.0

## 4. 风险评估

### 高风险因素
- 竞争环境加剧
- 成本压力上升

### 中风险因素  
- 技术更新速度
- 政策变化影响

### 低风险因素
- 团队能力稳定
- 基础设施完善

## 5. 行动建议

### 短期行动（1-3个月）
1. **优化用户体验**
   - 提升移动端性能
   - 简化操作流程
   
2. **增强数据能力**
   - 扩展数据源
   - 优化分析算法

### 中期规划（3-12个月）
1. **市场拓展**
   - 进入新的细分市场
   - 建立合作伙伴关系
   
2. **产品创新**
   - 开发新功能模块
   - 引入AI辅助工具

### 长期战略（1-3年）
1. **生态构建**
   - 平台化发展
   - 建立行业标准
   
2. **国际化**
   - 海外市场布局
   - 本地化适配

## 6. 结论

综合分析结果显示，当前发展态势良好，关键指标表现优异。建议继续保持现有策略方向，同时关注市场变化，及时调整策略以应对挑战。

---
*报告生成时间: ${new Date().toLocaleString()}*
*数据更新时间: ${new Date().toLocaleString()}*`

    return this.createReportMessage(content, config)
  }

  // ============================================================================
  // 错误消息工厂
  // ============================================================================

  /**
   * 创建错误消息 - 🎯 优化：使用助手消息创建方法
   */
  static createErrorMessage(
    code: string,
    message: string,
    config?: MockConfig
  ): BaseWebSocketMessage {
    const baseMessage = this.createAssistantBaseMessage(config)

    const payload: ErrorPayload = {
      type: 'error',
      code,
      message,
    }

    return {
      ...baseMessage,
      payload,
    }
  }

  /**
   * 创建网络错误
   */
  static createNetworkError(config?: MockConfig): BaseWebSocketMessage {
    return this.createErrorMessage('NETWORK_ERROR', '网络连接超时，请检查网络设置后重试', config)
  }

  /**
   * 创建验证错误
   */
  static createValidationError(config?: MockConfig): BaseWebSocketMessage {
    return this.createErrorMessage(
      'VALIDATION_ERROR',
      '输入数据格式不正确，请检查后重新提交',
      config
    )
  }

  /**
   * 创建服务器错误
   */
  static createServerError(config?: MockConfig): BaseWebSocketMessage {
    return this.createErrorMessage('SERVER_ERROR', '服务器处理异常，我们正在努力修复中', config)
  }

  // ============================================================================
  // 工具方法
  // ============================================================================

  /**
   * 将文本分割成指定大小的块
   */
  private static splitTextIntoChunks(text: string, maxChunkSize: number): string[] {
    const chunks: string[] = []
    let currentIndex = 0

    while (currentIndex < text.length) {
      // 随机块大小，但不超过最大值
      const chunkSize = Math.min(
        Math.floor(Math.random() * maxChunkSize) + 1,
        text.length - currentIndex
      )

      chunks.push(text.slice(currentIndex, currentIndex + chunkSize))
      currentIndex += chunkSize
    }

    return chunks
  }

  /**
   * 生成随机ID
   */
  static generateId(prefix: string = 'mock'): string {
    return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 更新消息配置
   */
  static updateMessageConfig(
    message: BaseWebSocketMessage,
    config: Partial<MockConfig>
  ): BaseWebSocketMessage {
    // 过滤掉 undefined 值
    const filteredConfig = Object.fromEntries(
      Object.entries(config).filter(([_, value]) => value !== undefined)
    )
    return {
      ...message,
      ...filteredConfig,
      timestamp: new Date().toISOString(),
    }
  }
}
