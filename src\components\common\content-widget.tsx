'use client'

import * as React from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { cn } from '@/lib/utils'
import { useSidebar } from '@/components/ui/sidebar'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import UnifiedDialog from './public-dialog'
import { ContextMenu, ContextMenuTrigger, ContextMenuContent, ContextMenuItem } from '@/components/ui/context-menu'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { ChevronDown, ChevronRight, ExternalLink, Trash2, Loader2, X } from 'lucide-react'
// 导入API服务
import { fetchSubscribedTopics, unsubscribeTopic, createTopic } from '@/lib/news-service'
import Image from 'next/image'
import smallLogo from '@/assets/small-logo.svg'

type ContentType = 'news' | 'tender'

interface ContentWidgetProps {
  type: ContentType
  className?: string
}

// 配置对象
const WIDGET_CONFIG = {
  news: {
    title: '每日资讯榜 Top 10',
    route: '/news',
    tooltipTitle: '每日资讯榜TOP10'
  },
  tender: {
    title: '每日招投标榜 Top 10',
    route: '/tender', 
    tooltipTitle: '每日招投标榜TOP10'
  }
}

export function ContentWidget({ type, className }: ContentWidgetProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { state } = useSidebar()
  const currentTopicId = searchParams.get('topicId')
  const [isExpanded, setIsExpanded] = React.useState(false)
  const [topics, setTopics] = React.useState<{[key: string]: string}>({})
  const [loading, setLoading] = React.useState(true)
  const [error, setError] = React.useState<string | null>(null)
  const [isAddTopicModalOpen, setIsAddTopicModalOpen] = React.useState(false)
  const [newTopicName, setNewTopicName] = React.useState('')
  const [deletingTopicId, setDeletingTopicId] = React.useState<string | null>(null)
  const [isCreatingTopic, setIsCreatingTopic] = React.useState(false)
  const [hasCreateError, setHasCreateError] = React.useState(false)
  const [createErrorMessage, setCreateErrorMessage] = React.useState('')
  
  // 检查侧边栏是否收起
  const isCollapsed = state === 'collapsed'
  
  // 获取配置
  const config = WIDGET_CONFIG[type]

  // 加载话题列表的函数
  const loadTopics = React.useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      
      let response: {[key: string]: string}
      
      if (type === 'news') {
        response = await fetchSubscribedTopics()
      } else {
        // 招投标使用占位符数据
        response = {
          '1': '基础设施建设',
          '2': '政府采购项目',
          '3': '工程承包项目',
          '4': '建筑工程招标',
          '5': '设备采购项目'
        }
      }
      
      // 添加数据验证和类型检查
      console.log(`${type} API响应数据:`, response)
      
      // 确保数据是正确的格式
      if (response && typeof response === 'object') {
        // 过滤掉非字符串值，确保所有值都是字符串
        const validatedTopics: {[key: string]: string} = {}
        Object.entries(response).forEach(([key, value]) => {
          if (typeof value === 'string') {
            validatedTopics[key] = value
          } else {
            console.warn(`跳过非字符串话题值: ${key} =`, value)
          }
        })
        setTopics(validatedTopics)
      } else {
        console.warn(`${type} API返回的数据格式不正确:`, response)
        setTopics({})
      }
    } catch (error) {
      console.error(`获取${type}话题列表失败:`, error)
      setError(`获取${type}话题列表失败`)
      setTopics({})
    } finally {
      setLoading(false)
    }
  }, [type])

  // 获取用户关注的话题列表
  React.useEffect(() => {
    loadTopics()
  }, [loadTopics])

  // 处理页面点击
  const handleHeaderClick = () => {
    router.push(config.route)
  }

  // 处理话题点击
  const handleTopicClick = (topicId: string) => {
    router.push(`${config.route}?topicId=${topicId}`)
  }

  // 处理添加话题
  const handleAddTopic = async () => {
    if (!newTopicName.trim()) {
      return
    }

    try {
      setIsCreatingTopic(true)
      setHasCreateError(false)
      setCreateErrorMessage('')
      console.log(`正在创建新${type}话题:`, newTopicName)

      if (type === 'news') {
        // 调用创建话题API
        const result = await createTopic({
          description: `用户创建的话题: ${newTopicName.trim()}`,
          input_text: newTopicName.trim()
        })

        console.log('创建话题成功:', result)

        // 创建成功后重新加载话题列表
        await loadTopics()

        // 跳转到新创建的话题
        if (result.topic_id) {
          router.push(`${config.route}?topicId=${result.topic_id}`)
        }

        // 清空输入并关闭弹框
        setNewTopicName('')
        setIsAddTopicModalOpen(false)
      } else {
        // 招投标类型暂时使用模拟逻辑
        console.log(`模拟添加新${type}话题:`, newTopicName)
        // 清空输入并关闭弹框
        setNewTopicName('')
        setIsAddTopicModalOpen(false)
      }
    } catch (error) {
      console.error(`创建${type}话题失败:`, error)
      // 显示错误状态
      setHasCreateError(true)
      setCreateErrorMessage(type === 'news' ? '没有相关招标' : '创建失败')
    } finally {
      setIsCreatingTopic(false)
    }
  }

  // 处理删除话题
  const handleDeleteTopic = async (topicId: string) => {
    try {
      setDeletingTopicId(topicId)
      console.log(`正在删除${type}话题:`, topicId)
      
      let success: boolean
      if (type === 'news') {
        success = await unsubscribeTopic(topicId)
      } else {
        // 招投标模拟删除成功
        success = true
      }
      
      if (success) {
        console.log(`删除${type}话题成功`)
        // 删除成功后重新加载话题列表
        await loadTopics()
        
        // 跳转到第一个话题
        const updatedTopics = await (type === 'news' ? fetchSubscribedTopics() : Promise.resolve({
          '1': '基础设施建设',
          '2': '政府采购项目'
        }))
        const topicIds = Object.keys(updatedTopics)
        if (topicIds.length > 0) {
          const firstTopicId = topicIds[0]
          router.push(`${config.route}?topicId=${firstTopicId}`)
        } else {
          // 如果没有话题了，跳转到主页
          router.push(config.route)
        }
      } else {
        console.error(`删除${type}话题失败`)
      }
    } catch (error) {
      console.error(`删除${type}话题时发生错误:`, error)
    } finally {
      setDeletingTopicId(null)
    }
  }

  // 收起状态下的简化显示
  if (isCollapsed) {
    return (
      <div className={cn('flex flex-col gap-2 p-2', className)} style={{background: '#F1F5F9'}}>
        {/* Logo图标 - 48x48尺寸，去除边框 */}
        <div 
          onClick={handleHeaderClick}
          className="w-12 h-12 rounded-full flex items-center justify-center cursor-pointer hover:opacity-80"
          style={{background: '#F1F5F9'}}
          title={config.tooltipTitle}
        >
          <Image src={smallLogo} alt="logo" className="w-8 h-8" />
        </div>
        
        {/* 话题简化显示 */}
        {Object.entries(topics).map(([id, name]) => {
          // 确保name是字符串
          const displayName = typeof name === 'string' ? name : String(name || '')
          const firstChar = displayName.charAt(0) || '?'
          const isSelected = currentTopicId === id
          return (
            <div
              key={id}
              onClick={() => handleTopicClick(id)}
              className={cn(
                "w-8 h-8 rounded-lg flex items-center justify-center cursor-pointer transition-colors",
                isSelected
                  ? "bg-blue-100 text-blue-800 border-2 border-blue-300"
                  : "bg-white hover:bg-gray-100 text-[#8e8e93]"
              )}
              title={displayName}
            >
              <span className="text-xs font-medium">{firstChar}</span>
            </div>
          )
        })}
      </div>
    )
  }

  // 展开状态下的完整显示
  return (
    <div className={cn('flex-1 px-4 pt-2 space-y-4', className)} style={{background: '#F1F5F9'}}>
      {/* 内容榜 Section */}
      <div>
        <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
          <CollapsibleTrigger asChild>
            <div 
              className="flex items-center justify-between mb-3 cursor-pointer"
              onClick={(e) => {
                // 既展开菜单又跳转页面
                handleHeaderClick()
                // Collapsible的默认行为会处理展开/收起
              }}
            >
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 rounded-full flex items-center justify-center" style={{background: '#F1F5F9'}}>
                  <Image src={smallLogo} alt="logo" className="w-6 h-6" />
                </div>
                <span className="font-medium text-[#000000]">
                  {config.title}
                </span>
              </div>
              <div>
                {isExpanded ? (
                  <ChevronDown className="h-4 w-4 text-[#8e8e93]" />
                ) : (
                  <ChevronRight className="h-4 w-4 text-[#8e8e93]" />
                )}
              </div>
            </div>
          </CollapsibleTrigger>
          
          <CollapsibleContent>
            <div className="ml-8 space-y-2">
              {loading ? (
                <div className="text-sm text-[#8e8e93]">加载中...</div>
              ) : error ? (
                <div className="text-sm text-red-500">{error}</div>
              ) : Object.keys(topics).length === 0 ? (
                <div className="text-sm text-[#8e8e93]">暂无关注的话题</div>
              ) : (
                <>
                  {Object.entries(topics).map(([id, name]) => {
                    // 确保name是字符串
                    const displayName = typeof name === 'string' ? name : String(name || '')
                    const isSelected = currentTopicId === id
                    return (
                      <ContextMenu key={id}>
                        <ContextMenuTrigger asChild>
                          <div
                            className={cn(
                              "text-sm cursor-pointer px-2 py-1 rounded-md transition-colors",
                              isSelected 
                                ? "bg-blue-100 text-blue-800 font-medium" 
                                : "text-[#8e8e93] hover:text-[#000000] hover:bg-gray-100"
                            )}
                            onClick={() => handleTopicClick(id)}
                          >
                            {displayName}
                          </div>
                        </ContextMenuTrigger>
                        <ContextMenuContent>
                          <ContextMenuItem
                            variant="destructive"
                            disabled={deletingTopicId === id}
                            onClick={(e) => {
                              e.stopPropagation()
                              handleDeleteTopic(id)
                            }}
                          >
                            {deletingTopicId === id ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <Trash2 className="h-4 w-4" />
                            )}
                            删除话题
                          </ContextMenuItem>
                        </ContextMenuContent>
                      </ContextMenu>
                    )
                  })}
                  {/* 添加话题按钮 */}
                  <div
                    className="text-sm text-[#8e8e93] hover:text-[#000000] cursor-pointer"
                    onClick={() => setIsAddTopicModalOpen(true)}
                  >
                    +
                  </div>
                </>
              )}
            </div>
          </CollapsibleContent>
        </Collapsible>
      </div>
      
      {/* 添加话题弹框 */}
      <UnifiedDialog
        isOpen={isAddTopicModalOpen}
        onClose={() => {
          if (!isCreatingTopic) {
            setIsAddTopicModalOpen(false)
            setNewTopicName('')
            setHasCreateError(false)
            setCreateErrorMessage('')
          }
        }}
        type={type === 'news' ? 'topic' : 'chat'}
        inputValue={newTopicName}
        onInputChange={setNewTopicName}
        onSubmit={handleAddTopic}
        isLoading={isCreatingTopic}
        hasError={hasCreateError}
        errorMessage={createErrorMessage}
        suggestions={type === 'news' ?
          ["科技新闻", "财经资讯", "体育动态"] :
          ["自动化项目", "农业设备招标", "高铁项目"]
        }
      />
    </div>
  )
}