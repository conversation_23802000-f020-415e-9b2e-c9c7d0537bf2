---
description: 
globs: 
alwaysApply: true
---
# 📘 Next.js 15 开发规范指南

---

## 1. 路由结构与动态段

* 所有页面统一放置于 `app/[locale]/...`，实现单一语言结构，避免代码冗余。
* 使用动态路由与可选捕获段处理复杂情况，例如：

```txt
app/[locale]/posts/[slug]/page.tsx
app/[locale]/products/[[...category]]/page.tsx
```

* 在 `page.tsx` 内通过 `generateStaticParams()` 预定义所有 locale 和动态 segment 参数，启用 SSG 并支持 i18n。

* 动态段（例如 `[slug]`、`[...param]`）在 `params` 中可得到，为使用者提供完整类型支持 ([youtube.com][1], [nextjs.org][2], [reddit.com][3], [nextjs.org][4])。

---

## 2. i18n 国际化结构建议

* 使用 `next-intl` 管理国际化路由、Provider 与 middleware。
* 路由定义推荐：

```ts
export const routing = defineRouting({
  locales: ['en','fr'],
  defaultLocale: 'en',
  localePrefix: 'as-needed',
});
```

* Middleware 处理 locale 自动重写，无需手动管理 Accept-Language：

```ts
import createMiddleware from 'next-intl/middleware';
export default createMiddleware(routing);
export const config = { matcher: ['/((?!api|_next|.*\\..*).*)'] };
```

* 在所有页面中加入 `generateStaticParams()` 保证预构建：

```ts
export async function generateStaticParams() {
  return routing.locales.map(locale=>({ locale }));
}
```

* 页面中使用 `useTranslations('namespace')` 获取翻译。

---

## 3. Middleware 使用规范

* 至少包含一个 `middleware.ts`，放置于项目根或 `src/`。
* Export 必须包括一个 `middleware(request: NextRequest)` 函数和 `config.matcher` 匹配路由逻辑 ([nextjs.org][2], [nextjs.org][5])。
* Middleware 可用于：

  * 路由重定向与路径重写
  * 统一设置 Header、Cookie 配置
  * SSR 日志埋点或特性标记
* 禁止在 middleware 中执行数据库或复杂计算；应仅用于轻量逻辑 ([nextjs.org][5], [nextjs.org][6])。

```ts
export function middleware(req) {
  // e.g. locale 重写逻辑 / auth 重定向
  return NextResponse.next();
}
export const config = { matcher: ['/((?!_next|api|.*\\..*).*)'] };
```

---

## 4. Route Handlers（API Routes）

* 在 `app/.../route.ts` 中定义 Route Handler，可处理 GET/POST 等 HTTP 方法 ([nextjs.org][2])。
* 规范例子：

```ts
// app/api/items/route.ts
export const dynamic = 'force-static';
export async function GET(req: Request) {
  const data = await fetch(...);
  return Response.json({ data });
}
export async function POST(req: Request) { /* 创建操作 */ }
```

* 推荐：GET 可配置静态缓存 (`dynamic='force-static'`)；POST 等任意 HTTP 均不缓存默认即可。

---

## 5. Server vs Client Component 使用规范

* 所有页面与布局默认为 Server Components，除非需要交互性。
* 使用 `'use client'` 标注交互组件，并尽可能把它们拆分为微组件，确保最小化客户端 JavaScript ([nextjs.org][2], [nextjs.org][7], [nextjs.org][8])。
* Server Component 适用于：数据获取、渲染静态内容、隐藏 secret。
* Client Component 适用于：事件处理、状态管理、浏览器 API、第三方组件封装。
* 使用 `server-only` 模块隔离仅服务器环境逻辑，并阻止错误导入至客户端 ([nextjs.org][8])。

推荐结构示例：

```
app/[locale]/
  layout.tsx        // Server
  page.tsx          // Server
  ui/
    LikeButton.tsx  // 'use client'
```

---

## 6. `next.config.js` 常用配置建议

```ts
import { defineConfig } from 'next';
import createNextIntlPlugin from 'next-intl/plugin';

export default defineConfig({
  experimental: {
    appDir: true,
  },
  i18n: {
    locales: ['en','fr'],
    defaultLocale: 'en',
  },
  plugins: [createNextIntlPlugin()],
});
```

* 确保启用 `appDir` 支持 App Router。
* 若使用 `next-intl`，建议加载 plugin 自动管理 locale 资源目录。
* 明确配置 `i18n`，提供基础校验机制。

---

## 7. 文件系统约定与最佳实践

### 7.1 特殊文件约定

* 使用Next.js App Router的特殊文件约定：

```txt
app/[locale]/
  layout.tsx        // 共享布局
  page.tsx          // 页面组件
  loading.tsx       // 加载状态
  error.tsx         // 错误处理
  not-found.tsx     // 404页面
  template.tsx      // 重新渲染的布局
  default.tsx       // 并行路由回退
```

* 每个特殊文件都有明确的用途和生命周期。

### 7.2 数据获取规范

* Server Components中直接进行数据获取：

```ts
// 推荐：在Server Component中直接获取数据
export default async function Page() {
  const data = await fetch('https://api.example.com/data')
  const posts = await data.json()

  return <PostList posts={posts} />
}
```

* 避免在Server Components中调用Route Handlers。
* 使用`fetch`的缓存选项控制数据新鲜度。

### 7.3 静态生成优化

* 对于动态路由，必须使用`generateStaticParams`：

```ts
export async function generateStaticParams() {
  const posts = await fetch('https://api.example.com/posts')
    .then(res => res.json())

  return posts.map(post => ({
    slug: post.slug,
  }))
}
```

* 结合国际化使用：

```ts
export async function generateStaticParams() {
  return locales.map(locale => ({ locale }))
}
```

---

## 8. 性能优化与安全规范

### 8.1 组件优化策略

* 将`'use client'`边界尽可能放在组件树的底层：

```ts
// 好的做法：只在需要交互的组件使用'use client'
function ServerPage() {
  return (
    <div>
      <Header />  {/* Server Component */}
      <InteractiveButton />  {/* Client Component */}
    </div>
  )
}
```

* 避免将敏感数据传递给Client Components。

### 8.2 数据安全

* Server Components中避免暴露敏感数据：

```ts
// 错误：暴露完整用户对象
export default async function Page() {
  const user = await getUser() // 包含敏感信息
  return <Profile user={user} />  // 全部传给客户端
}

// 正确：只传递必要数据
export default async function Page() {
  const user = await getUser()
  const safeUserData = {
    name: user.name,
    avatar: user.avatar
  }
  return <Profile user={safeUserData} />
}
```

### 8.3 缓存策略

* 合理使用fetch缓存选项：

```ts
// 静态数据
const staticData = await fetch('https://api.example.com/static', {
  cache: 'force-cache'
})

// 动态数据
const dynamicData = await fetch('https://api.example.com/dynamic', {
  cache: 'no-store'
})

// 定时重新验证
const revalidatedData = await fetch('https://api.example.com/data', {
  next: { revalidate: 3600 } // 1小时
})
```

---

## 9. 错误处理与加载状态

### 9.1 错误边界

* 使用`error.tsx`处理错误：

```ts
'use client'

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  return (
    <div>
      <h2>Something went wrong!</h2>
      <button onClick={() => reset()}>Try again</button>
    </div>
  )
}
```

### 9.2 加载状态

* 使用`loading.tsx`提供即时加载反馈：

```ts
export default function Loading() {
  return <div>Loading...</div>
}
```

* 结合Suspense实现流式渲染：

```ts
import { Suspense } from 'react'

export default function Page() {
  return (
    <Suspense fallback={<Loading />}>
      <AsyncComponent />
    </Suspense>
  )
}
```

---

## 🌟 总结约束与执行细则

1. **静态代码目录规范**：统一放于 `app/[locale]/...`，默认语言不维护重复代码。
2. **动态段模式支持**：覆盖单页、Catch-all 等复杂路由。
3. **Middleware 必配**：实现轻量逻辑，不滥用 auth。
4. **Route Handler 建议使用**：支持 RESTful API 及缓存策略。
5. **组件边界清晰**：默认为 Server，需要 interactivity 才变 Client。
6. **文件系统约定**：遵循Next.js特殊文件约定，合理组织代码结构。
7. **数据获取优化**：Server Components直接获取数据，避免不必要的API调用。
8. **静态生成**：动态路由必须使用`generateStaticParams`。
9. **性能安全**：最小化客户端JavaScript，保护敏感数据。
10. **错误处理**：完善的错误边界和加载状态。

---

[1]: https://www.youtube.com/watch?v=kAAt7077FkY&utm_source=chatgpt.com "Your Complete Guide To Middleware In Next.js 15 - YouTube"
[2]: https://nextjs.org/docs/app/getting-started/route-handlers-and-middleware?utm_source=chatgpt.com "Getting Started: Route Handlers and Middleware - Next.js"
[3]: https://www.reddit.com/r/nextjs/comments/1h6o0ci/when_to_use_server_vs_client_components_in_nextjs/?utm_source=chatgpt.com "When to Use Server vs. Client Components in Next.js? - Reddit"
[4]: https://nextjs.org/docs/app/api-reference/file-conventions/dynamic-routes?utm_source=chatgpt.com "File-system conventions: Dynamic Segments | Next.js"
[5]: https://nextjs.org/docs/app/api-reference/file-conventions/middleware?utm_source=chatgpt.com "File-system conventions: middleware.js | Next.js"
[6]: https://nextjs.org/docs/14/app/building-your-application/routing/middleware?utm_source=chatgpt.com "Middleware - Routing - Next.js"
[7]: https://nextjs.org/learn/react-foundations/server-and-client-components?utm_source=chatgpt.com "Server and Client Components - React Foundations - Next.js"
[8]: https://nextjs.org/docs/app/getting-started/server-and-client-components?utm_source=chatgpt.com "Getting Started: Server and Client Components - Next.js"
