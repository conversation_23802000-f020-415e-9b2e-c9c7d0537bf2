'use client'

/**
 * TypingIndicator - 现代化的打字指示器
 *
 * 基于最佳实践设计，提供流畅的动画和多种样式
 */

import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Bot } from 'lucide-react'

interface TypingIndicatorProps {
  /** 是否显示 */
  visible: boolean
  /** 打字者名称 */
  name?: string
  /** 头像URL */
  avatarUrl?: string | undefined
  /** 显示模式 */
  mode?: 'bubble' | 'dots' | 'wave'
  /** 自定义样式 */
  className?: string
}

export const TypingIndicator = ({
  visible,
  name = 'AI助手',
  avatarUrl,
  mode = 'bubble',
  className,
}: TypingIndicatorProps) => {
  if (!visible) return null

  // 气泡动画变体
  const bubbleVariants = {
    hidden: { opacity: 0, y: 20, scale: 0.8 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 20,
      },
    },
    exit: {
      opacity: 0,
      y: -10,
      scale: 0.9,
      transition: { duration: 0.2 },
    },
  }

  // 点动画变体
  const dotVariants = {
    initial: { y: 0 },
    animate: { y: [-4, 0, -4] },
  }

  // 波浪动画变体
  const waveVariants = {
    initial: { scaleY: 1 },
    animate: { scaleY: [1, 1.5, 1] },
  }

  const renderDots = () => (
    <div className="flex items-center space-x-1">
      {[0, 1, 2].map(i => (
        <motion.div
          key={i}
          className="w-2 h-2 bg-muted-foreground rounded-full"
          variants={dotVariants}
          initial="initial"
          animate="animate"
          transition={{
            duration: 1.4,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: i * 0.2,
          }}
        />
      ))}
    </div>
  )

  const renderWave = () => (
    <div className="flex items-end space-x-1 h-4">
      {[0, 1, 2, 1, 0].map((height, i) => (
        <motion.div
          key={i}
          className="w-1 bg-muted-foreground rounded-full"
          style={{ height: `${8 + height * 4}px` }}
          variants={waveVariants}
          initial="initial"
          animate="animate"
          transition={{
            duration: 1.2,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: i * 0.1,
          }}
        />
      ))}
    </div>
  )

  if (mode === 'dots') {
    return (
      <motion.div
        className={cn('flex items-center justify-center py-4', className)}
        variants={bubbleVariants}
        initial="hidden"
        animate="visible"
        exit="exit"
      >
        <div className="flex items-center space-x-2 text-muted-foreground text-sm">
          <span>{name} 正在输入</span>
          {renderDots()}
        </div>
      </motion.div>
    )
  }

  if (mode === 'wave') {
    return (
      <motion.div
        className={cn('flex items-center justify-center py-4', className)}
        variants={bubbleVariants}
        initial="hidden"
        animate="visible"
        exit="exit"
      >
        <div className="flex items-center space-x-3">
          <Avatar className="w-6 h-6">
            {avatarUrl && <AvatarImage src={avatarUrl} />}
            <AvatarFallback className="bg-secondary text-secondary-foreground">
              <Bot className="w-3 h-3" />
            </AvatarFallback>
          </Avatar>
          {renderWave()}
        </div>
      </motion.div>
    )
  }

  // 📱 移动端优化的默认气泡模式
  return (
    <motion.div
      className={cn('flex gap-2 sm:gap-3 max-w-[90%] sm:max-w-[85%]', className)}
      variants={bubbleVariants}
      initial="hidden"
      animate="visible"
      exit="exit"
    >
      {/* 📱 移动端优化的头像 */}
      <Avatar className="w-6 h-6 sm:w-8 sm:h-8 flex-shrink-0">
        {avatarUrl && <AvatarImage src={avatarUrl} />}
        <AvatarFallback className="bg-secondary text-secondary-foreground">
          <Bot className="w-4 h-4" />
        </AvatarFallback>
      </Avatar>

      {/* 消息气泡 */}
      <div className="flex flex-col gap-1 flex-1 min-w-0">
        <div className="flex items-center gap-2 text-xs text-muted-foreground px-1">
          <span className="font-medium text-xs sm:text-sm">{name}</span>
        </div>

        <div className="rounded-2xl rounded-bl-md px-3 sm:px-4 py-2 sm:py-3 bg-muted min-h-[36px] sm:min-h-[44px] flex items-center">
          <div className="flex items-center space-x-1">
            {[0, 1, 2].map(i => (
              <motion.div
                key={i}
                className="w-2 h-2 bg-muted-foreground rounded-full"
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.5, 1, 0.5],
                }}
                transition={{
                  duration: 1.4,
                  repeat: Infinity,
                  ease: 'easeInOut',
                  delay: i * 0.2,
                }}
              />
            ))}
          </div>
        </div>
      </div>
    </motion.div>
  )
}
