/**
 * Next.js 中间件 - 重构版本
 * 基于模块化架构，实现职责分离和高可维护性
 *
 * 架构说明：
 * 1. 认证中间件 - 处理用户认证状态检查
 * 2. 业务中间件 - 处理业务逻辑和路由重定向
 * 3. 安全中间件 - 设置安全头和 CORS
 *
 * 解决的问题：
 * - 消除多次重定向（根路径直接重定向到最终目标）
 * - 职责分离（认证、业务、安全逻辑分开）
 * - 提升可维护性和可测试性
 */

import { NextRequest, NextResponse } from 'next/server'
import { composeEnhancedMiddleware } from './middleware/utils/middleware-composer'
import { RouteClassifier } from './middleware/utils/route-matcher'
import { authMiddleware } from './middleware/auth/auth-middleware'
import { defaultBusinessMiddleware } from './middleware/business/route-handler'
import { defaultSecurityMiddleware } from './middleware/security/security-headers'

/**
 * 主中间件函数
 * Next.js 识别的标准入口点
 */
export async function middleware(request: NextRequest): Promise<NextResponse> {
  const { pathname } = request.nextUrl

  // 性能优化：减少开发环境日志输出

  // 早期跳过静态资源，提升性能
  if (RouteClassifier.shouldSkipMiddleware(pathname)) {
    return NextResponse.next()
  }

  try {
    // 使用增强中间件组合器，按优先级执行
    const composedMiddleware = composeEnhancedMiddleware(
      [
        // 1. 安全头中间件（最高优先级）
        // 为所有响应添加安全头，无论是否重定向
        defaultSecurityMiddleware,

        // 2. 认证中间件（高优先级）
        // 检查用户认证状态，设置认证上下文
        // 不处理重定向逻辑，只负责认证状态管理
        authMiddleware,

        // 3. 业务路由中间件（核心逻辑）
        // 基于认证上下文执行业务重定向
        // 这里解决了原有的多次重定向问题
        defaultBusinessMiddleware,
      ],
      {
        enableDebugLogs: process.env.NODE_ENV === 'development',
        enableTiming: process.env.NODE_ENV === 'development',
        errorHandling: 'continue', // 中间件出错时继续执行，提升健壮性
      }
    )

    // 执行组合的中间件
    return await composedMiddleware(request)
  } catch (error) {
    console.error('[Middleware v2.0] 中间件执行失败:', error)

    // 错误降级：返回带基本安全头的响应
    const response = NextResponse.next()

    // 添加基本安全头
    response.headers.set('X-Frame-Options', 'DENY')
    response.headers.set('X-Content-Type-Options', 'nosniff')
    response.headers.set('X-XSS-Protection', '1; mode=block')

    return response
  }
}

/**
 * 中间件配置
 * 定义哪些路径需要经过中间件处理
 * 使用Next.js官方推荐的matcher模式
 */
export const config = {
  matcher: [
    /*
     * 匹配所有路径，除了：
     * - api路由（/api开头）
     * - _next（Next.js内部路由）
     * - 静态资源文件（包含.的文件）
     * - 监控路由（/monitoring）
     */
    '/((?!api|_next|monitoring|.*\\..*).*)',
  ],
}

/**
 * 中间件工具函数导出
 * 提供给其他模块使用的工具函数
 */
export { middlewareUtils } from './middleware/utils/legacy-utils'

/**
 * 中间件系统初始化完成
 * 架构组件：认证、业务、安全中间件已就绪
 */

/**
 * 重构说明和改进点：
 *
 * 🎯 解决的核心问题：
 * 1. 消除重定向链条 - 根路径直接重定向到最终目标页面
 * 2. 职责分离 - 认证、业务、安全逻辑独立处理
 * 3. 提升性能 - 减少不必要的计算和网络请求
 * 4. 增强可维护性 - 模块化架构便于测试和维护
 *
 * 🔄 重定向流程优化：
 * 原有流程: / -> 中间件重定向 -> /chat -> 客户端重定向 -> /chat/1
 * 新流程: / -> 中间件直接重定向 -> /chat/1 (通过chatRouteOptimizer)
 *
 * 📊 性能提升：
 * - 静态资源早期跳过，减少不必要的处理
 * - 认证结果缓存，避免重复网络请求
 * - 条件执行，只在需要时运行特定中间件
 *
 * 🧪 可测试性：
 * - 每个中间件可独立测试
 * - 清晰的输入输出和副作用
 * - 模拟友好的依赖注入
 *
 * 🛡️ 错误处理：
 * - 优雅降级策略
 * - 详细的错误日志
 * - 不影响用户体验的错误恢复
 */
