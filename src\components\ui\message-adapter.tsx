/**
 * 消息适配器 - 将WebSocket消息转换为StreamingMessageBubble组件所需的props
 * 简化数据转换逻辑，专注于streaming文本场景
 */

import type { BaseWebSocketMessage } from '@/types/websocket-event-type'
import { isStreamingPayload } from '@/types/websocket-event-type'
import type { StreamingMessageBubbleProps } from './streaming-message-bubble'

export interface MessageAdapterOptions {
  /** 当前用户ID，用于判断消息角色 */
  currentUserId: string
  /** 显示设置 */
  showAvatar?: boolean
  showTimestamp?: boolean
  /** 头像URL */
  userAvatarUrl?: string | undefined
  assistantAvatarUrl?: string | undefined
  /** 交互功能 */
  enableCopy?: boolean
  onCopy?: ((content: string) => void) | undefined
}

/**
 * 将WebSocket消息转换为StreamingMessageBubble的props
 */
export function adaptWebSocketMessage(
  message: BaseWebSocketMessage,
  options: MessageAdapterOptions
): StreamingMessageBubbleProps | null {
  const {
    currentUserId,
    showAvatar = true,
    showTimestamp = true,
    userAvatarUrl,
    assistantAvatarUrl,
    enableCopy = true,
    onCopy,
  } = options

  // 只处理streaming类型的消息
  if (!isStreamingPayload(message.payload)) {
    return null
  }

  // 判断消息角色
  // Mock数据的userId是'mock-assistant-bot'，应该显示为assistant
  // 真实用户消息的userId会匹配currentUserId
  const role = message.userId === currentUserId ? 'user' : 'assistant'

  // 提取streaming内容
  const payload = message.payload
  const content = message.metadata?.streamingState?.accumulatedText || payload.delta || ''
  const isStreaming = !payload.isComplete

  // 如果没有内容且未完成，不渲染
  if (!content && !payload.isComplete) {
    return null
  }

  return {
    content,
    isStreaming,
    role,
    id: message.id,
    timestamp: new Date(message.timestamp),
    showAvatar,
    showTimestamp,
    userAvatarUrl,
    assistantAvatarUrl,
    enableCopy,
    onCopy,
  }
}

/**
 * 批量转换消息列表
 */
export function adaptMessageList(
  messages: BaseWebSocketMessage[],
  options: MessageAdapterOptions
): StreamingMessageBubbleProps[] {
  return messages
    .map(message => adaptWebSocketMessage(message, options))
    .filter((props): props is StreamingMessageBubbleProps => props !== null)
}

/**
 * 检查消息是否为streaming类型
 */
export function isStreamingMessage(message: BaseWebSocketMessage): boolean {
  return isStreamingPayload(message.payload)
}

/**
 * 获取消息的显示内容
 */
export function getMessageContent(message: BaseWebSocketMessage): string {
  if (isStreamingPayload(message.payload)) {
    return message.metadata?.streamingState?.accumulatedText || message.payload.delta || ''
  }
  return ''
}

/**
 * 检查消息是否正在streaming
 */
export function isMessageStreaming(message: BaseWebSocketMessage): boolean {
  if (isStreamingPayload(message.payload)) {
    return !message.payload.isComplete
  }
  return false
}
