/**
 * 认证工具函数
 * 提供认证相关的底层工具和缓存机制
 */

import { NextRequest } from 'next/server'

/**
 * 用户会话数据类型
 */
export interface UserSession {
  user: {
    id: string
    email: string
    name?: string
    role?: string
    language?: string
  }
  organization?: {
    id: string
    hasCompanyProfile?: boolean
    role?: string
  }
  expires: string
}

/**
 * 认证检查结果
 */
export interface AuthCheckResult {
  isAuthenticated: boolean
  session: UserSession | null
  error?: string
}

/**
 * Session 缓存
 * 避免重复的网络请求，提升性能
 */
class SessionCache {
  private cache = new Map<string, { data: UserSession | null; timestamp: number }>()
  private readonly TTL = 60000 // 1分钟缓存

  /**
   * 生成缓存键
   */
  private getCacheKey(sessionToken: string): string {
    // 使用 session token 的前16位作为缓存键（避免敏感信息泄露）
    return sessionToken.substring(0, 16)
  }

  /**
   * 获取缓存的会话数据
   */
  get(sessionToken: string): UserSession | null | undefined {
    const key = this.getCacheKey(sessionToken)
    const cached = this.cache.get(key)

    if (!cached) {
      return undefined
    }

    // 检查是否过期
    if (Date.now() - cached.timestamp > this.TTL) {
      this.cache.delete(key)
      return undefined
    }

    return cached.data
  }

  /**
   * 设置缓存
   */
  set(sessionToken: string, session: UserSession | null): void {
    const key = this.getCacheKey(sessionToken)
    this.cache.set(key, {
      data: session,
      timestamp: Date.now(),
    })
  }

  /**
   * 清除缓存
   */
  clear(): void {
    this.cache.clear()
  }

  /**
   * 清除过期缓存
   */
  cleanup(): void {
    const now = Date.now()
    for (const [key, value] of this.cache.entries()) {
      if (now - value.timestamp > this.TTL) {
        this.cache.delete(key)
      }
    }
  }
}

// 全局会话缓存实例
const sessionCache = new SessionCache()

// 定期清理过期缓存
if (typeof globalThis !== 'undefined') {
  setInterval(() => {
    sessionCache.cleanup()
  }, 300000) // 5分钟清理一次
}

/**
 * 检查 session cookie 是否存在且格式正确
 */
export function hasValidSessionCookie(request: NextRequest): boolean {
  // 优先检查主 cookie: better-auth.session_token
  let sessionCookie = request.cookies.get('better-auth.session_token')

  // 备用 cookie 检查: auth.session_token
  if (!sessionCookie?.value) {
    sessionCookie = request.cookies.get('auth.session_token')
  }

  if (!sessionCookie?.value) {
    return false
  }

  // Better Auth token 格式验证: sessionId.encryptedData
  const parts = sessionCookie.value.split('.')
  if (parts.length !== 2) {
    return false
  }

  // 基础长度检查
  const [sessionId, encryptedData] = parts
  return sessionId.length > 10 && encryptedData.length > 10
}

/**
 * 获取 session token
 */
export function getSessionToken(request: NextRequest): string | null {
  // 优先检查主 cookie
  const primaryCookie = request.cookies.get('better-auth.session_token')
  if (primaryCookie?.value) {
    return primaryCookie.value
  }

  // 备用 cookie
  const backupCookie = request.cookies.get('auth.session_token')
  if (backupCookie?.value) {
    return backupCookie.value
  }

  return null
}

/**
 * 获取用户会话
 * 使用 Better Auth 适配器，提供高性能的会话验证
 *
 * 这个函数现在使用专门为中间件环境设计的 Better Auth 适配器
 * 解决了直接使用 fetch 的问题，并提供了更好的缓存和错误处理
 */
export async function getUserSession(request: NextRequest): Promise<AuthCheckResult> {
  // 使用 Better Auth 适配器
  const { getBetterAuthSession } = await import('./better-auth-adapter')
  return getBetterAuthSession(request)
}

/**
 * 检查用户是否为管理员
 */
export function isAdminUser(session: UserSession | null): boolean {
  if (!session?.user?.role) {
    return false
  }

  const userRole = session.user.role.toLowerCase()
  return userRole === 'admin' || userRole === 'super_admin'
}

/**
 * 检查用户是否完成了公司资料
 */
export function hasCompanyProfile(session: UserSession | null): boolean {
  return session?.organization?.hasCompanyProfile === true
}

/**
 * 获取用户角色信息
 */
export function getUserRole(session: UserSession | null): string | null {
  return session?.user?.role || null
}

/**
 * 获取组织角色信息
 */
export function getOrganizationRole(session: UserSession | null): string | null {
  return session?.organization?.role || null
}

/**
 * 认证上下文构建器
 */
export function buildAuthContext(authResult: AuthCheckResult) {
  const { isAuthenticated, session } = authResult

  return {
    isAuthenticated,
    session,
    userRole: getUserRole(session),
    organizationRole: getOrganizationRole(session),
    isAdmin: isAdminUser(session),
    hasCompanyProfile: hasCompanyProfile(session),
  }
}

/**
 * 日志工具 - 认证相关
 */
export function logAuthAction(
  action: string,
  context: {
    pathname: string
    isAuthenticated: boolean
    userRole?: string | null
    userId?: string | null
    error?: string | null
  }
): void {
  if (process.env.NODE_ENV === 'development') {
    console.log(`[Auth] ${action}`, {
      path: context.pathname,
      authenticated: context.isAuthenticated,
      role: context.userRole,
      userId: context.userId ? `${context.userId.substring(0, 8)}...` : undefined,
      error: context.error,
    })
  }
}

/**
 * 性能监控 - 认证耗时
 */
export function measureAuthTime<T>(operation: string, fn: () => Promise<T>): Promise<T> {
  const startTime = Date.now()

  return fn().finally(() => {
    const duration = Date.now() - startTime
    if (process.env.NODE_ENV === 'development' && duration > 100) {
      console.warn(`[Auth Performance] ${operation} took ${duration}ms`)
    }
  })
}
