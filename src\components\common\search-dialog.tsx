import { useState } from 'react'
import UnifiedDialog from './public-dialog'

interface SearchDialogProps {
  isOpen: boolean
  onClose: () => void
}

const SearchDialog = ({ isOpen, onClose }: SearchDialogProps) => {
  const [searchQuery, setSearchQuery] = useState("")

  const suggestions = ["自动化项目", "农业设备招标", "高铁项目"]

  const handleSearch = () => {
    // 处理搜索逻辑
    console.log('搜索:', searchQuery)
  }

  return (
    <UnifiedDialog
      isOpen={isOpen}
      onClose={onClose}
      type="chat"
      inputValue={searchQuery}
      onInputChange={setSearchQuery}
      onSubmit={handleSearch}
      suggestions={suggestions}
    />
  )
}

export default SearchDialog