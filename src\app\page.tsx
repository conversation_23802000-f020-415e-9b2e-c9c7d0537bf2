'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'

/**
 * 根路径 - 已认证用户的默认页面
 *
 * 注意：此页面现在为受保护路由，只有已登录用户能访问
 * 未登录用户会被中间件重定向到 /login
 * 已登录用户访问此页面时，自动重定向到 /chat
 */
export default function HomePage() {
  const router = useRouter()

  useEffect(() => {
    // 已认证用户默认重定向到聊天页面
    router.push('/chat')
  }, [router])

  // 显示加载状态，实际上用户会被立即重定向
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
        <p className="text-muted-foreground">正在跳转到聊天页面...</p>
      </div>
    </div>
  )
}
