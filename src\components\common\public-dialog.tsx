import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Dialog, DialogContent } from '../ui/dialog'
import { X, ChevronRight, AlertTriangle } from 'lucide-react'
import Image from 'next/image'
import tenderIcon from '@/assets/tender.png'
import informationIcon from '@/assets/information.png'

interface UnifiedDialogProps {
  isOpen: boolean
  onClose: () => void
  type: 'chat' | 'topic'
  // 通用props
  inputValue?: string
  onInputChange?: (value: string) => void
  onSubmit?: () => void
  suggestions?: string[]
  isLoading?: boolean
  hasError?: boolean
  errorMessage?: string
}

const UnifiedDialog = ({
  isOpen,
  onClose,
  type,
  inputValue = '',
  onInputChange,
  onSubmit,
  suggestions = [],
  isLoading = false,
  hasError = false,
  errorMessage = '没有相关招标'
}: UnifiedDialogProps) => {

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && onSubmit) {
      onSubmit()
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent 
        className="!max-w-none !max-h-none border-0 p-0"
        style={{ 
          width: '833px', 
          height: '422px', 
          background: '#F1F5F9',
          boxShadow: '0px 0px 0.5px 0px #42474C80, 0px 4px 8px 0px #42474C1A, 0px 4px 40px 0px #EEEEEE'
        }}
      >
        {/* Close button */}
        <Button 
          variant="ghost" 
          size="icon" 
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 z-10"
          onClick={onClose}
        >
          <X className="h-5 w-5" />
        </Button>

        {/* 按照Figma设计稿的精确布局 */}
        <div className="relative w-full h-full">
          {/* Avatar - 位置: x:161, y:90, 尺寸: 53x53 */}
          <div
            className="absolute rounded-full bg-[#DBEAFE] flex items-center justify-center overflow-hidden"
            style={{
              left: '161px',
              top: '90px',
              width: '53px',
              height: '53px'
            }}
          >
            <Image
              src={type === 'chat' ? tenderIcon : informationIcon}
              alt={type === 'chat' ? 'Tender' : 'Information'}
              width={53}
              height={53}
              className="object-cover"
            />
          </div>

          {/* 聊天气泡 - 位置: x:220, y:93, 尺寸: 208.32x45.03 */}
          <div
            className="absolute rounded-2xl rounded-tl-sm flex items-center justify-center"
            style={{
              left: '220px',
              top: '93px',
              width: '208.32px',
              height: '45.03px',
              background: '#E7EEFE'
            }}
          >
            <p
              className="text-[#252525] text-sm font-normal leading-[1.43]"
              style={{ fontFamily: 'Inter' }}
            >
              {type === 'chat' ? '在看新的招投标吗？' : '请重新输入招投标主题'}
            </p>
          </div>

                     {/* 输入框容器 - 位置: x:230, y:172, 尺寸: 398x50 */}
           <div
             className="absolute rounded-[80px] flex items-center px-6"
             style={{
               left: '230px',
               top: '172px',
               width: '398px',
               height: '50px',
               background: hasError ? '#FFDDDD' : (isLoading ? '#CCF5B5' : '#FFFFFF')
             }}
           >
                         <Input
               type="text"
               placeholder="输入的请求文字变成浅灰9CA3AF"
               value={inputValue}
               onChange={(e) => onInputChange?.(e.target.value)}
               onKeyDown={handleKeyDown}
               disabled={isLoading}
               className="flex-1 bg-transparent border-none text-[#5d5d5d] placeholder:text-[#9ca3af] focus-visible:ring-0 focus-visible:ring-offset-0 focus:border-none focus:shadow-none shadow-none text-base"
               style={{ fontFamily: 'Inter', boxShadow: 'none' }}
             />

            {/* 提交按钮 - 位置: x:585, y:179, 尺寸: 37x37 */}
                         <Button
               size="icon"
               className="bg-[#5d5d5d] hover:bg-[#252525] rounded-full shrink-0 disabled:opacity-50"
               style={{
                 width: '37px',
                 height: '37px',
                 border: hasError ? '1px solid #FFDDDD' : (isLoading ? '1px solid #CCF5B5' : '1px solid #FFFFFF')
               }}
               onClick={onSubmit}
               disabled={isLoading}
             >
              <ChevronRight className="h-4 w-4 text-white" />
            </Button>
          </div>

          {/* 状态显示区域 - 位置: x:355-373, y:259-264 */}
          {(isLoading || hasError) && (
            <div
              className="absolute flex items-center justify-center gap-3"
              style={{
                left: '355px',
                top: '259px',
                width: '123px',
                height: '19px'
              }}
            >
              {isLoading ? (
                <>
                  {/* Loading 图标 - 位置: x:355, y:259, 尺寸: 18x19 */}
                  <div className="flex items-center gap-1">
                    <div className="w-[18px] h-[19px] flex items-center justify-center">
                      <div className="w-2 h-2 bg-[#8BA6E8] rounded-full animate-pulse"></div>
                      <div className="w-2 h-2 bg-[#EFEFEF] rounded-full ml-1"></div>
                    </div>
                  </div>
                  {/* 加载文字 - 位置: x:373, y:264, 尺寸: 96x10 */}
                  <span
                    className="text-[#252525] text-sm font-normal leading-[1.43] text-center"
                    style={{ fontFamily: 'Noto Sans' }}
                  >
                    话题生成中
                  </span>
                </>
                             ) : hasError && (
                 <>
                   {/* 错误图标 - 位置: x:356, y:261, 尺寸: 16x16 */}
                   <AlertTriangle
                     className="text-[#09090B]"
                     style={{ width: '16px', height: '16px' }}
                   />
                   {/* 错误文字 - 位置: x:373, y:264, 尺寸: 96x10 */}
                   <span
                     className="text-[#252525] text-sm font-normal leading-[1.43] text-center"
                     style={{ fontFamily: 'Noto Sans' }}
                   >
                     {errorMessage}
                   </span>
                 </>
               )}
            </div>
          )}

          {/* 推荐话题 - 只在非加载和非错误状态显示 */}
          {!isLoading && !hasError && (
            <div
              className="absolute"
              style={{
                left: '230px',
                top: '240px',
                width: '398px'
              }}
            >
              <p className="text-gray-500 text-sm font-medium mb-3">
                猜你喜欢
              </p>
              <div className="flex flex-wrap gap-2">
                {suggestions.map((suggestion, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    className="rounded-full border-gray-200 text-black hover:bg-blue-100 hover:border-blue-300"
                    style={{ background: '#E7EEFE' }}
                    onClick={() => onInputChange?.(suggestion)}
                  >
                    {suggestion}
                  </Button>
                ))}
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default UnifiedDialog