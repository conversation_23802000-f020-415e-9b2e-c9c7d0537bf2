# 🎯 Mock模式优化开发指南

## 📋 目标与背景

### 当前问题

- ChatContainer中手动切换两个不同的Hook (`useChatStandard` 和 `useChatMock`)
- Mock模式管理复杂，需要手动控制状态
- 开发调试体验不佳，缺少统一的调试界面
- 代码重复，两个Hook的接口适配需要大量条件判断

### 优化目标

创建一个更优雅简洁的mock模式调试系统，提升开发体验和代码可维护性。

## 🏗️ 架构设计

### 核心思想

1. **统一接口**：创建统一的聊天Hook，内部自动处理模式切换
2. **全局管理**：使用Context管理全局mock模式状态（仅开发环境）
3. **场景预设**：提供预设的调试场景，快速切换测试状态
4. **Mock会话管理**：支持导入导出Mock调试会话，便于复现问题（仅Mock数据）

### 架构图

```
ChatModeProvider (Context)
├── useChatUnified (统一Hook)
│   ├── useChatStandard (生产模式)
│   └── useChatMock (调试模式)
├── MockController (Mock控制器)
└── EnhancedDevToolsPanel (增强调试面板)
```

## 📂 文件结构与实现细节

### 1. 全局模式管理

#### 🆕 `src/contexts/chat-mode-context.tsx`

**功能**: 全局Mock模式状态管理
**要创建的组件/函数**:

```typescript
// 主要接口
interface ChatModeContextType {
  mode: 'standard' | 'mock'
  setMode: (mode: 'standard' | 'mock') => void
  mockConfig: MockConfig
  setMockConfig: (config: MockConfig) => void
  isDebugMode: boolean
  setIsDebugMode: (debug: boolean) => void
}

// 要创建的组件
const ChatModeProvider: React.FC<{ children: React.ReactNode }>
const useChatMode: () => ChatModeContextType
```

**实现要点**:

- 使用 `createContext` 创建Context
- **仅在开发环境**使用 `sessionStorage` 持久化Mock模式状态
- 提供默认的Mock配置（仅开发环境）
- 生产环境强制使用 `standard` 模式，无持久化

#### 🆕 `src/types/mock-types.ts`

**功能**: Mock模式类型定义
**要创建的类型**:

```typescript
interface MockConfig {
  latency: number // 模拟延迟
  errorRate: number // 错误率
  autoResponse: boolean // 自动响应
  scenarios: MockScenario[] // 预设场景
}

interface MockScenario {
  id: string
  name: string
  description: string
  messages: MockMessage[]
  config: Partial<MockConfig>
}

interface MockSession {
  id: string
  name: string
  messages: ChatMessage[] // 仅用于Mock数据导入导出
  config: MockConfig
  timestamp: Date
}
```

### 2. 统一聊天Hook

#### 🆕 `src/lib/hooks/useChatUnified.ts`

**功能**: 统一两个现有Hook的接口，自动模式切换
**要创建的函数**:

```typescript
// 主要Hook函数
export function useChatUnified(options: ChatOptions = {}): ChatHookReturn {
  const { mode, mockConfig } = useChatMode()
  const standardHook = useChatStandard(options)
  const mockHook = useChatMock()

  return mode === 'mock' ? mockHook : standardHook
}
```

**实现要点**:

- 保持与现有Hook完全相同的API接口
- 内部自动切换Hook实现
- 透明处理模式切换，不影响上层组件
- 提供性能监控和调试信息

#### 🔄 `src/lib/hooks/useChatMock.ts` (增强现有文件)

**需要增强的函数**:

```typescript
// 现有函数需要增强
export function useChatMock(): ChatHookReturn {
  // 新增：支持配置化Mock行为
  const { mockConfig } = useChatMode()

  // 新增：场景预设加载
  const loadScenario = (scenario: MockScenario) => {
    // 实现场景加载逻辑
  }

  // 新增：会话导入导出
  const exportSession = (): MockSession => {
    // 实现会话导出逻辑
  }

  const importSession = (session: MockSession) => {
    // 实现会话导入逻辑
  }

  // 返回增强的接口
  return {
    ...existingReturn,
    loadScenario,
    exportSession,
    importSession,
  }
}
```

### 3. Mock控制器

#### 🆕 `src/lib/mock-controller.ts`

**功能**: 集中管理Mock行为和场景
**要创建的类/函数**:

```typescript
// 主要控制器类
export class MockController {
  // 场景管理
  static getPresetScenarios(): MockScenario[]
  static loadScenario(scenarioId: string): void
  static createCustomScenario(config: MockScenarioConfig): MockScenario

  // 实时控制
  static simulateLatency(ms: number): void
  static injectError(type: 'network' | 'server' | 'validation'): void
  static toggleAutoResponse(enabled: boolean): void

  // Mock会话管理（仅开发环境Mock数据）
  static exportCurrentMockSession(): MockSession
  static importMockSession(session: MockSession): void
  static saveMockSession(name: string): void
  static loadMockSession(sessionId: string): void

  // 数据生成
  static generateMockMessage(type: MessageType): ChatMessage
  static generateMockConversation(length: number): ChatMessage[]
}
```

**预设场景定义**:

```typescript
const PRESET_SCENARIOS = {
  NORMAL_CHAT: {
    id: 'normal-chat',
    name: '正常对话',
    description: '模拟正常的AI对话流程',
    config: { latency: 1000, errorRate: 0 },
  },
  ERROR_HANDLING: {
    id: 'error-handling',
    name: '错误处理',
    description: '模拟各种错误场景',
    config: { latency: 500, errorRate: 0.3 },
  },
  PERFORMANCE_TEST: {
    id: 'performance-test',
    name: '性能测试',
    description: '模拟高负载场景',
    config: { latency: 2000, errorRate: 0.1 },
  },
  LONG_CONVERSATION: {
    id: 'long-conversation',
    name: '长对话',
    description: '模拟长时间对话场景',
    config: { latency: 800, errorRate: 0.05 },
  },
}
```

### 4. 调试面板升级

#### 🔄 `src/components/dev/DevToolsPanel.tsx` (升级现有文件)

**需要升级的组件**:

```typescript
// 现有组件需要添加的Props
interface DevToolsPanelProps {
  // 现有props...

  // 新增props
  currentMode: 'standard' | 'mock'
  onModeChange: (mode: 'standard' | 'mock') => void
  mockConfig: MockConfig
  onMockConfigChange: (config: MockConfig) => void
  presetScenarios: MockScenario[]
  onLoadScenario: (scenario: MockScenario) => void
  onExportSession: () => void
  onImportSession: (session: MockSession) => void
}
```

**需要添加的功能模块**:

1. **模式切换器**: 一键切换Standard/Mock模式（仅开发环境）
2. **场景选择器**: 快速加载预设场景
3. **配置面板**: 调整延迟、错误率等参数
4. **Mock会话管理**: 导入/导出/保存Mock会话（仅Mock数据）
5. **实时监控**: 性能指标和连接状态
6. **快速操作**: 添加消息、模拟错误等

### 5. 主要组件更新

#### 🔄 `src/components/chat/ChatContainer.tsx` (简化现有文件)

**需要修改的部分**:

```typescript
// 原有代码 (需要删除/简化)
const [isMockMode, setIsMockMode] = useState(process.env.NODE_ENV === 'development')
const realChatHook = useChatStandard(groupId ? { groupId } : {})
const mockChatHook = useChatMock()
const chatHook = isMockMode ? mockChatHook : realChatHook

// 新代码 (简化后)
const chatHook = useChatUnified(groupId ? { groupId } : {})
```

**需要修改的函数**:

- `handleToggleMockMode`: 删除，移至Context管理
- `handleAddMockMessage`: 简化，通过统一Hook调用
- `handleLoadDemoConversation`: 简化，通过统一Hook调用
- `handleClearMessages`: 简化，通过统一Hook调用
- `handleSimulateError`: 简化，通过统一Hook调用

## 🚀 实施步骤

### 第一阶段: 基础架构 (1-2天)

1. **创建类型定义** (`src/types/mock-types.ts`)
2. **创建Context系统** (`src/contexts/chat-mode-context.tsx`)
3. **创建统一Hook** (`src/lib/hooks/useChatUnified.ts`)

### 第二阶段: Mock控制器 (2-3天)

1. **创建Mock控制器** (`src/lib/mock-controller.ts`)
2. **增强现有useChatMock** (添加配置支持)
3. **实现场景预设系统**

### 第三阶段: 调试面板 (2-3天)

1. **升级DevToolsPanel** (添加新功能)
2. **创建场景选择器组件**
3. **实现会话管理功能**

### 第四阶段: 集成与优化 (1-2天)

1. **更新ChatContainer** (简化代码)
2. **添加Context Provider到应用根部**
3. **测试和调试**

## 📋 检查清单

### 开发前检查

- [ ] 理解现有架构和代码结构
- [ ] 确认TypeScript类型定义
- [ ] 准备测试数据和场景

### 开发过程检查

- [ ] 保持现有API接口兼容性
- [ ] 添加适当的错误处理
- [ ] 实现性能监控
- [ ] 添加调试日志

### 完成后检查

- [ ] 所有现有功能正常工作
- [ ] 新功能按预期工作
- [ ] 性能没有明显下降
- [ ] 代码质量符合项目标准

## 🎯 预期收益

### 开发体验提升

- **一键切换**: 快速在生产/调试模式间切换（仅开发环境）
- **场景预设**: 快速加载常见调试场景
- **Mock会话管理**: 保存和复现Mock问题场景（仅Mock数据）
- **实时监控**: 直观的性能和状态监控

### 代码质量提升

- **代码简化**: ChatContainer逻辑减少50%+
- **统一接口**: 消除重复的Hook适配代码
- **可维护性**: 清晰的架构和职责分离
- **可扩展性**: 易于添加新的Mock功能

### 调试效率提升

- **快速复现**: 通过Mock会话导入快速复现问题（仅Mock数据）
- **场景测试**: 系统化测试各种边界情况
- **性能分析**: 实时监控和性能指标
- **错误模拟**: 方便测试错误处理逻辑

## 💡 最佳实践

1. **渐进式迁移**: 先创建新组件，再逐步替换旧代码
2. **向后兼容**: 确保现有功能不受影响
3. **性能优化**: 使用React.memo和useCallback优化渲染
4. **错误处理**: 添加完善的错误边界和降级方案
5. **测试覆盖**: 为关键功能添加单元测试

## 🔧 技术细节

### Context状态管理

```typescript
// 使用useReducer管理复杂状态
const [state, dispatch] = useReducer(chatModeReducer, initialState)

// 使用useMemo优化Context value
const contextValue = useMemo(
  () => ({
    mode: state.mode,
    setMode: mode => dispatch({ type: 'SET_MODE', payload: mode }),
    ...otherValues,
  }),
  [state.mode, ...otherDependencies]
)
```

### Hook性能优化

```typescript
// 使用useMemo缓存计算结果
const processedMessages = useMemo(() => {
  return messages.map(processMessage)
}, [messages])

// 使用useCallback稳定函数引用
const handleSubmit = useCallback(
  e => {
    // 处理逻辑
  },
  [dependencies]
)
```

### 类型安全

```typescript
// 使用泛型增强类型安全
interface ChatHookReturn<T = ChatMessage> {
  messages: T[]
  sendMessage: (message: string) => Promise<void>
  // 其他属性...
}
```

## 🔍 数据处理原则

### 生产环境数据处理

- **标准模式**: 所有数据来自真实的WebSocket连接和后端API
- **无持久化**: 不在本地存储任何聊天数据
- **实时同步**: 数据完全依赖后端状态，每次重新连接都获取最新数据

### 开发环境Mock数据处理

- **Mock模式**: 仅在开发环境下可用，使用本地生成的测试数据
- **SessionStorage**: 仅用于保存Mock模式状态和调试配置
- **Mock会话**: 支持导入导出Mock数据，便于复现调试场景
- **清晰分离**: Mock数据和真实数据完全隔离，不会互相影响

### 模式切换逻辑

```typescript
// 模式判断逻辑
const mode = process.env.NODE_ENV === 'development' ? (debugMode ? 'mock' : 'standard') : 'standard' // 生产环境强制使用standard模式
```

这个开发指南提供了详细的实施路径和技术细节，可以帮助开发团队高效地完成Mock模式优化工作。
