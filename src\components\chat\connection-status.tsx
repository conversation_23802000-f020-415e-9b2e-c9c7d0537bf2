/**
 * 连接状态组件 - 专门显示连接状态和相关信息
 * 
 * 功能：连接状态显示、重连控制、调试信息
 * 依赖：ConnectionStatus枚举、connection-store
 * 性能：轻量级状态显示组件
 * 
 * 数据流：Connection Store -> ConnectionStatus -> UI Display
 */

'use client'

import React from 'react'
import { cn } from '@/lib/utils'
import { Wifi, WifiOff, Loader2, AlertCircle, Settings, type LucideIcon } from 'lucide-react'

// UI组件
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'

// 类型和状态
import { ConnectionStatus as ConnectionStatusEnum } from '@/lib/connection/types'
import type { ChatSession } from '@/stores/session-store'

// ============================================================================
// 组件接口定义
// ============================================================================

export interface ConnectionStatusProps {
  /** 连接状态 */
  connectionStatus: ConnectionStatusEnum
  isConnected: boolean
  
  /** 会话信息 */
  currentSession: ChatSession | null | undefined
  messageCount: number
  
  /** 控制选项 */
  showControls?: boolean
  showDetailedStatus?: boolean
  
  /** 事件回调 */
  onReconnect?: () => void
  onOpenSettings?: () => void
  
  /** 样式配置 */
  className?: string
  variant?: 'minimal' | 'detailed' | 'debug'
}

// ============================================================================
// 连接状态配置
// ============================================================================

interface StatusConfig {
  icon: LucideIcon
  text: string
  color: string
  bgColor: string
  description: string
}

const getStatusConfig = (status: ConnectionStatusEnum): StatusConfig => {
  switch (status) {
    case ConnectionStatusEnum.CONNECTED:
      return {
        icon: Wifi,
        text: '已连接',
        color: 'text-green-500',
        bgColor: 'bg-green-50',
        description: '连接正常，可以正常发送消息',
      }
    case ConnectionStatusEnum.CONNECTING:
      return {
        icon: Loader2,
        text: '连接中...',
        color: 'text-yellow-500',
        bgColor: 'bg-yellow-50',
        description: '正在建立连接，请稍候',
      }
    case ConnectionStatusEnum.RECONNECTING:
      return {
        icon: Loader2,
        text: '重连中...',
        color: 'text-blue-500',
        bgColor: 'bg-blue-50',
        description: '正在尝试重新连接',
      }
    case ConnectionStatusEnum.DISCONNECTED:
      return {
        icon: WifiOff,
        text: '已断开',
        color: 'text-red-500',
        bgColor: 'bg-red-50',
        description: '连接已断开，无法发送消息',
      }
    case ConnectionStatusEnum.ERROR:
      return {
        icon: AlertCircle,
        text: '连接错误',
        color: 'text-red-500',
        bgColor: 'bg-red-50',
        description: '连接发生错误，请检查网络',
      }
    default:
      return {
        icon: WifiOff,
        text: '未连接',
        color: 'text-gray-500',
        bgColor: 'bg-gray-50',
        description: '尚未建立连接',
      }
  }
}

// ============================================================================
// 最小化状态显示
// ============================================================================

const MinimalStatus: React.FC<{
  statusConfig: StatusConfig
  isConnected: boolean
}> = ({ statusConfig, isConnected }) => {
  const { icon: StatusIcon, text, color, bgColor } = statusConfig

  // 只在连接有问题时显示
  if (isConnected) {
    return null
  }

  return (
    <Badge
      variant="outline"
      className={cn('flex items-center space-x-1', bgColor)}
    >
      <StatusIcon className={cn('w-3 h-3', color)} />
      <span className={color}>{text}</span>
    </Badge>
  )
}

// ============================================================================
// 详细状态显示
// ============================================================================

const DetailedStatus: React.FC<{
  statusConfig: StatusConfig
  currentSession: ChatSession | null | undefined
  messageCount: number
}> = ({ statusConfig, currentSession, messageCount }) => {
  const { icon: StatusIcon, text, color, bgColor, description } = statusConfig

  return (
    <div className="flex flex-col gap-1">
      <div className="flex items-center space-x-2">
        <Badge
          variant="outline"
          className={cn('flex items-center space-x-1', bgColor)}
        >
          <StatusIcon className={cn('w-3 h-3', color)} />
          <span className={color}>{text}</span>
        </Badge>
        
        {currentSession && messageCount > 0 && (
          <span className="text-sm text-muted-foreground">
            对话进行中 ({messageCount} 条消息)
          </span>
        )}
      </div>
      
      <p className="text-xs text-muted-foreground">{description}</p>
    </div>
  )
}

// ============================================================================
// 调试状态显示
// ============================================================================

const DebugStatus: React.FC<{
  statusConfig: StatusConfig
  currentSession: ChatSession | null | undefined
  messageCount: number
  connectionStatus: ConnectionStatusEnum
}> = ({ statusConfig, currentSession, messageCount, connectionStatus }) => {
  const { icon: StatusIcon, text, color, bgColor } = statusConfig

  return (
    <div className="bg-gray-50 border rounded-lg p-3 space-y-2">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <StatusIcon className={cn('w-4 h-4', color)} />
          <span className={cn('font-medium', color)}>{text}</span>
        </div>
        <Badge variant="outline" className="text-xs">
          {connectionStatus}
        </Badge>
      </div>
      
      <div className="grid grid-cols-2 gap-2 text-xs text-muted-foreground">
        <div>
          <span className="font-medium">会话ID:</span>
          <div className="truncate">{currentSession?.sessionId || '无'}</div>
        </div>
        <div>
          <span className="font-medium">群聊ID:</span>
          <div className="truncate">{currentSession?.groupChatId || '无'}</div>
        </div>
        <div>
          <span className="font-medium">消息数量:</span>
          <div>{messageCount}</div>
        </div>
        <div>
          <span className="font-medium">会话状态:</span>
          <div>{currentSession?.isActive ? '活跃' : '非活跃'}</div>
        </div>
      </div>
    </div>
  )
}

// ============================================================================
// 主要组件
// ============================================================================

export const ConnectionStatus: React.FC<ConnectionStatusProps> = ({
  connectionStatus,
  isConnected,
  currentSession,
  messageCount,
  showControls = false,
  showDetailedStatus = false,
  onReconnect,
  onOpenSettings,
  className,
  variant = 'minimal',
}) => {
  const statusConfig = getStatusConfig(connectionStatus)

  const handleReconnect = () => {
    if (onReconnect) {
      onReconnect()
    } else if (process.env.NODE_ENV === 'development') {
      console.warn('🔌 重新连接按钮点击 - 请提供onReconnect回调')
    }
  }

  const handleOpenSettings = () => {
    if (onOpenSettings) {
      onOpenSettings()
    } else if (process.env.NODE_ENV === 'development') {
      console.log('⚙️ 设置按钮点击')
    }
  }

  // ============================================================================
  // 渲染不同变体
  // ============================================================================

  const renderStatusContent = () => {
    switch (variant) {
      case 'detailed':
        return (
          <DetailedStatus
            statusConfig={statusConfig}
            currentSession={currentSession}
            messageCount={messageCount}
          />
        )
      
      case 'debug':
        return (
          <DebugStatus
            statusConfig={statusConfig}
            currentSession={currentSession}
            messageCount={messageCount}
            connectionStatus={connectionStatus}
          />
        )
      
      case 'minimal':
      default:
        return (
          <MinimalStatus
            statusConfig={statusConfig}
            isConnected={isConnected}
          />
        )
    }
  }

  const renderControls = () => {
    if (!showControls) return null

    return (
      <div className="flex items-center space-x-1 sm:space-x-2">
        {/* 重连按钮 - 只在断开连接时显示 */}
        {connectionStatus === ConnectionStatusEnum.DISCONNECTED && (
          <Button
            size="sm"
            variant="outline"
            onClick={handleReconnect}
            className="h-8"
          >
            重新连接
          </Button>
        )}
        
        {/* 设置按钮 */}
        <Button 
          size="sm" 
          variant="ghost"
          onClick={handleOpenSettings}
          className="h-8 w-8 p-0"
        >
          <Settings className="w-4 h-4" />
        </Button>
      </div>
    )
  }

  // ============================================================================
  // 主要渲染
  // ============================================================================

  return (
    <div className={cn(
      'flex items-center justify-between p-3 sm:p-4 border-b bg-background/80 backdrop-blur-sm',
      className
    )}>
      <div className="flex items-center space-x-2 sm:space-x-3 flex-1">
        {renderStatusContent()}
      </div>

      {renderControls()}
    </div>
  )
}

// ============================================================================
// 状态Hook
// ============================================================================

export const useConnectionStatusDisplay = (connectionStatus: ConnectionStatusEnum) => {
  const config = getStatusConfig(connectionStatus)
  
  return {
    ...config,
    isHealthy: connectionStatus === ConnectionStatusEnum.CONNECTED,
    isConnecting: [ConnectionStatusEnum.CONNECTING, ConnectionStatusEnum.RECONNECTING].includes(connectionStatus),
    hasError: [ConnectionStatusEnum.ERROR, ConnectionStatusEnum.DISCONNECTED].includes(connectionStatus),
  }
}

// ============================================================================
// 导出
// ============================================================================

ConnectionStatus.displayName = 'ConnectionStatus'

export default ConnectionStatus