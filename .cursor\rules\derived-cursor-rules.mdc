---
description: AI rules derived by <PERSON><PERSON><PERSON><PERSON> from the project AI interaction history
globs: *
---

## Headers

## TECH STACK
- Frontend Framework: Next.js 15 + React 18 + TypeScript
- State Management: Zustand + Immer
- UI Component Library: Radix UI + TailwindCSS 4
- Authentication System: JWT Token + Custom Authentication Flow
- Deployment: Docker + Standalone Mode
- Internationalization: next-intl

## PROJECT DOCUMENTATION & CONTEXT SYSTEM
- Follow @global.mdc principles for thinking, breaking down tasks, and coding.
- Use `feedback mcp` to provide feedback when a dialogue ends or when there are any questions.
- When the AI asks for feedback, provide it.
- Adhere to @derived-cursor-rules.mdc requirements for routing and i18n.

## PROJECT STRUCTURE
- `src/app/`: Next.js App Router
  - `src/app/[locale]/`: Internationalized routes
    - `src/app/[locale]/(auth)/`: Authentication related pages
  - `src/app/embed/[page]/`: Core iframe embedding page
  - `src/app/api/`: API routes
- `src/components/`: Component library
  - `src/components/embed/`: Iframe-specific components
  - `src/components/ui/`: Base UI components
  - `src/components/providers/`: Global providers
- `src/lib/`: Utility library
  - `src/lib/middleware/`: Middleware system
  - `src/lib/auth/`: Authentication related
  - `src/lib/embed/`: Iframe embedding logic
- `src/stores/`: Zustand state management

## WORKFLOW & RELEASE RULES
- When building for production, set `NEXT_PUBLIC_EXCLUDE_UI_GALLERY=true` to exclude the UI gallery.

## CODING STYLE
- File Naming:
  - Page files: `page.tsx`, `layout.tsx`, `loading.tsx`, `error.tsx`
  - Component files: `kebab-case` (e.g., `user-profile.tsx`, `data-table.tsx`)
  - Utility files: `camelCase` (e.g., `authUtils.ts`, `apiHelpers.ts`)
- Component Structure:
  - Use the recommended component structure with props interface.
- State Management:
  - Priority: React State > Zustand > Context
    - Local state: `useState`, `useReducer`
    - Global state: Zustand stores
    - Theme/Authentication: React Context

## DEBUGGING
- Enable detailed logs with `DEBUG=* pnpm run dev`.
- Enable MCP debugging with `MCP_DEBUG=true pnpm run dev`.
- Display user-friendly error messages for SSL certificate errors.
- When debugging, strictly adhere to @global.mdc principles for thinking, task breakdown, and coding.
- When encountering a bug, analyze @middleware.ts and @DEVELOPMENT_GUIDE.md for relevant information.
- After identifying the issue, use `feedback mcp` to report your findings and proposed solutions.

## DEPLOYMENT
- Dockerized deployment using standalone mode.
- Use multi-stage builds to optimize image size.
- Different security policies for development and production environments.

## SECURITY
- Multi-layered security protection:
  - Domain whitelist validation
  - IP access control
  - Token format validation
  - CSP security policy
- Development environment support:
  - `localhost` debugging mode
  - Flexible port configuration
  - Detailed debugging logs
- Production environment hardening:
  - Strict CORS policy
  - Access log recording
  - Error monitoring reports

## ERROR HANDLING
- Handle API errors, especially authentication-related errors, by checking the response codes of `/api/user/get_current`. When the response code is one of the following, clear the token and redirect to the login page:
    - `ERROR_CODE_UNAUTHORIZED = 401` (Unauthorized)
    - `ERROR_CODE_NO_PERMISSION = 402` (No permission)
    - `ERROR_CODE_USER_NOT_LOGIN = 601` (User not logged in)
    - `ERROR_CODE_USER_DELETED = 603` (User has been deleted)

## MIDDLEWARE
- `src/lib/middleware/embed-security.ts`: Security validation for embed paths.
- All middleware in `src/lib/middleware` should be documented with their purpose and calling conditions.
- Integrate `next-intl` middleware for internationalization.

## BEST PRACTICES
- Single responsibility architecture.
- Progressive security policies.
- Performance optimization (adaptive, debounce, caching).
- Global error monitoring and graceful degradation.
- Before developing new features, understand existing components and tools to avoid duplication.
- Use existing UI components.

## I18N INTERNATIONALIZATION
- Supported languages: Chinese, English, Japanese.
- Use `next-intl` library for internationalization.
- Create language files in `src/lib/i18n/messages/{locale}.json`.
- Use `getUserLocale` to get user's preferred language.
- Implement API call to update user's language preference.

## DEVELOPMENT GUIDELINES
### New Page Development Process

#### 🎯 Development Prerequisites

Before starting to develop a new page, please understand:

1. **Authentication mechanism** - Determine whether the page requires login verification.
2. **Routing structure** - Choose the appropriate directory to place the page, ensuring it's under the `[locale]` directory for internationalized routes.
3. **Layout inheritance** - Understand the existing layout configuration.
4. **State management** - Determine whether global state is needed.

#### 📝 Step 1: Determine the Page Type

##### 🔓 Public Pages (No Login Required)

```bash
# Applicable Scenarios: Marketing pages, help documentation, public displays
src/app/[locale]/your-page/page.tsx
```

##### 🔐 Pages Requiring Authentication

```bash
# Applicable Scenarios: User dashboards, personal settings
src/app/[locale]/(dashboard)/your-page/page.tsx  # Need to create the (dashboard) group first
```

##### 🖼️ iframe Embedding Pages

```bash
# Applicable Scenarios: Third-party embedding, independent applications
src/app/embed/your-page/page.tsx
```

#### 📝 Step 2: Create Basic Page Files

##### Public Page Example

```typescript
// src/app/[locale]/your-page/page.tsx
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Your Page Title',
  description: 'Page Description',
}

export default function YourPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Your Page Title</h1>
      <p className="text-gray-600">Page Content...</p>
    </div>
  )
}
```

##### Pages Requiring Authentication Example

```typescript
// src/app/[locale]/(dashboard)/your-page/page.tsx
'use client'

import { useAuth } from '@/lib/stores/auth-store'
import { useEffect } from 'react'
import { redirect } from 'next/navigation'

export default function ProtectedPage() {
  const { user, isAuthenticated } = useAuth()

  useEffect(() => {
    if (!isAuthenticated) {
      redirect('/login')
    }
  }, [isAuthenticated])

  if (!isAuthenticated) {
    return <div>Loading...</div>
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Welcome, {user?.name}</h1>
      {/* Page Content */}
    </div>
  )
}
```

##### iframe Embedding Page Example

```typescript
// src/app/embed/your-page/page.tsx
'use client'

import { IframeContainer } from '@/components/embed/iframe-container'
import { useSearchParams } from 'next/navigation'

export default function EmbedPage() {
  const searchParams = useSearchParams()
  const token = searchParams.get('token')

  return (
    <IframeContainer token={token}>
      <div className="p-4">
        <h1 className="text-2xl font-bold mb-4">Embedded Page Content</h1>
        {/* Your Embedded Content */}
      </div>
    </IframeContainer>
  )
}
```

#### 📝 Step 3: Configure Routing and Layout

##### Custom Layout (Optional)

```typescript
// src/app/[locale]/your-page/layout.tsx
export default function YourPageLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="min-h-screen bg-gray-50">
      <nav className="bg-white shadow-sm">
        {/* Page-Specific Navigation */}
      </nav>
      <main className="container mx-auto">
        {children}
      </main>
    </div>
  )
}
```

##### Dynamic Routing (If Needed)

```typescript
// src/app/[locale]/your-page/[id]/page.tsx
interface Props {
  params: Promise<{ id: string }>
}

export default async function DynamicPage({ params }: Props) {
  const { id } = await params
  
  return (
    <div>
      <h1>Page ID: {id}</h1>
    </div>
  )
}
```

#### 📝 Step 4: Add Styles and Components

##### Using Existing UI Components

```typescript
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'

export default function StyledPage() {
  return (
    <Card className="max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Sample Form</CardTitle>
      </CardHeader>
      <CardContent>
        <Input placeholder="Enter content..." className="mb-4" />
        <Button className="w-full">Submit</Button>
      </CardContent>
    </Card>
  )
}
```

##### Creating Page-Specific Components

```typescript
// src/components/your-page/your-component.tsx
interface YourComponentProps {
  title: string
  content: string
}

export function YourComponent({ title, content }: YourComponentProps) {
  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <h2 className="text-xl font-semibold mb-2">{title}</h2>
      <p className="text-gray-600">{content}</p>
    </div>
  )
}
```

#### 📝 Step 5: Integrate State Management (If Needed)

##### Using the Existing Auth Store

```typescript
'use client'

import { useAuth } from '@/lib/stores/auth-store'

export default function AuthAwarePage() {
  const { user, login, logout, isAuthenticated } = useAuth()

  return (
    <div>
      {isAuthenticated ? (
        <div>
          <p>Welcome, {user?.name}</p>
          <button onClick={logout}>Logout</button>
        </div>
      ) : (
        <button onClick={() => login('token')}>Login</button>
      )}
    </div>
  )
}
```

##### Creating Page-Specific State (Optional)

```typescript
// src/lib/stores/your-page-store.ts
import { create from 'zustand'
import { immer from 'zustand/middleware/immer'

interface YourPageState {
  data: any[]
  loading: boolean
  fetchData: () => Promise<void>
}

export const useYourPageStore = create<YourPageState>()(
  immer((set, get) => ({
    data: [],
    loading: false,
    fetchData: async () => {
      set((state) => {
        state.loading = true
      })
      
      try {
        // API Call
        const response = await fetch('/api/your-data')
        const data = await response.json()
        
        set((state) => {
          state.data = data
          state.loading = false
          
        })
      } catch (error) {
        set((state) => {
          state.loading = false
        })
      }
    },
  }))
)
```

### Authentication and Routing Mechanisms

#### Authentication Process

##### AuthProvider Global Check

```typescript
// src/components/providers/auth-provider.tsx
// Automatically checks the authentication status for the following paths:
// ✅ Skip: /embed/*, /ui-gallery, /login
// 🔐 Check: All other paths
```

##### Middleware Security Verification

```typescript
// src/lib/middleware/embed-security.ts
// Handles domain whitelisting, IP control, token verification
```

##### Page-Level Protection

```typescript
// Use the ProtectedRoute component or custom useEffect checks
```

#### Routing Configuration

##### Adding New Public Paths

```typescript
// In AuthProvider, add the path to skip
if (pathname.startsWith('/embed/') || 
    pathname.startsWith('/ui-gallery') ||
    pathname.startsWith('/your-public-page')) {
  // Skip authentication check
}
```

##### Adding New Protected Paths

```typescript
// Add authentication checks to the page
'use client'

import { useAuth } from '@/lib/stores/auth-store'
import { useEffect } from 'react'
import { redirect } from 'next/navigation'

export default function ProtectedPage() {
  const { isAuthenticated } = useAuth()

  useEffect(() => {
    if (!isAuthenticated) {
      redirect('/login')
    }
  }, [isAuthenticated])

  // Page content...
}
```

### Key Configuration Files

#### Next.js Configuration

```typescript
// next.config.ts - Main configuration items:
// - output: 'standalone'           // Docker deployment
// - assetPrefix                    // Static resource prefix
// - env                           // Environment variables
// - headers()                     // CORS and security headers
// - redirects()                   // Redirection rules
// - webpack()                     // Build optimization
```

#### Middleware Configuration

```typescript
// src/middleware.ts - Route interception:
// - Embed path handling
// - ui-gallery public access
// - Security verification and logging
```

#### Environment Configuration

```bash
# Example development environment variables
NEXT_PUBLIC_API_BASE_URL=https://dev.goglobalsp.com
NEXT_PUBLIC_APP_ENV=development
NEXT_PUBLIC_EMBED_ENABLED=true
NEXT_PUBLIC_EXCLUDE_UI_GALLERY=false
```

### Development Best Practices

#### Code Style

##### File Naming

```bash
# Page Files
page.tsx, layout.tsx, loading.tsx, error.tsx

# Component Files (kebab-case)
user-profile.tsx, data-table.tsx

# Utility Files (camelCase)
authUtils.ts, apiHelpers.ts
```

##### Component Structure

```typescript
// Recommended component structure
import { type ComponentProps } from 'react'

interface YourComponentProps {
  title: string
  optional?: boolean
}

export function YourComponent({ title, optional = false }: YourComponentProps) {
  // State and Logic
  
  return (
    <div className="component-styles">
      {/* JSX Content */}
    </div>
  )
}
```

##### State Management

```typescript
// Priority: React State > Zustand > Context
// 1. Local State - useState, useReducer
// 2. Global State - Zustand stores
// 3. Theme/Authentication - React Context
```

#### Performance Optimization

##### Component Lazy Loading

```typescript
import dynamic from 'next/dynamic'

const HeavyComponent = dynamic(() => import('./heavy-component'), {
  loading: () => <p>Loading...</p>,
  ssr: false
})
```

##### Image Optimization

```typescript
import Image from 'next/image'

<Image
  src="/your-image.jpg"
  alt="Description"
  width={500}
  height={300}
  priority // First screen image
/>
```

#### Testing Strategies

##### Component Testing

```typescript
// __tests__/your-component.test.tsx
import { render, screen } from '@testing-library/react'
import { YourComponent } from '../your-component'

test('renders component correctly', () => {
  render(<YourComponent title="Test" />)
  expect(screen.getByText('Test')).toBeInTheDocument()
})
```

##### API Testing

```typescript
// Use Jest Mock for API testing
jest.mock('next/navigation', () => ({
  useSearchParams: () => new URLSearchParams('token=test')
}))
```

### Troubleshooting

#### Problem 1: Page Unreachable (401/403)

**Symptom**: New page displays an authentication error or access is denied.

**Solution**:

```typescript
// 1. Check AuthProvider configuration
// src/components/providers/auth-provider.tsx
if (pathname.startsWith('/your-page')) {
  // Add public access skip
}

// 2. Check middleware configuration
// src/lib/middleware/embed-security.ts
if (pathname.startsWith('/your-page')) {
  return NextResponse.next()
}
```

#### Problem 2: Styles Not Applied

**Symptom**: TailwindCSS class names do not work.

**Solution**:

```bash
# 1. Check the content configuration in tailwind.config.js
content: [
  './src/**/*.{js,ts,jsx,tsx,mdx}',
]

# 2. Restart the development server
pnpm run dev

# 3. Clear the cache
pnpm run clean
```

#### Problem 3: API Call Failed

**Symptom**: /api route returns an error or timeout.

**Solution**:

```typescript
// 1. Check the API base URL configuration
// .env.local
NEXT_PUBLIC_API_BASE_URL=https://dev.goglobalsp.com

// 2. Check proxy configuration
// src/app/api/[...proxy]/route.ts
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL

// 3. Check CORS configuration
// next.config.ts headers()
```

#### Problem 4: Hot Reload Not Working

**Symptom**: Page does not automatically refresh after code modification.

**Solution**:

```bash
# 1. Check file saving
# Ensure the file is actually saved

# 2. Restart the development server
pnpm run dev

# 3. Clear the Next.js cache
rm -rf .next
pnpm run dev

# 4. Check file permissions
ls -la src/
```

#### Problem 5: TypeScript Errors

**Symptom**: Type checking fails or import errors.

**Solution**:

```typescript
// 1. Check tsconfig.json paths configuration
"paths": {
  "@/*": ["./src/*"]
}

// 2. Restart the TypeScript server
// VSCode: Cmd+Shift+P -> "TypeScript: Restart TS Server"

// 3. Check type imports
import { type ComponentProps } from 'react'
```

### Getting Help

#### Development Tools

- **Development Server**: `pnpm run dev`
- **Build Test**: `pnpm run build`
- **Type Check**: `pnpm run type-check`
- **Code Formatting**: `pnpm run format`

#### Debugging Mode

```bash
# Enable detailed logs
DEBUG=* pnpm run dev

# Enable MCP debugging
MCP_DEBUG=true pnpm run dev
```

#### Documentation References

- [Next.js 15 Documentation](mdc:https:/nextjs.org/docs)
- [TailwindCSS Documentation](mdc:https:/tailwindcss.com/docs)
- [Zustand Documentation](mdc:https:/zustand-demo.pmnd.rs)
- [Radix UI Documentation](mdc:https:/www.radix-ui.com)

### Summary

By following this guide, you can:

1. ✅ **Quickly create new pages** - Choose the appropriate directory and configuration according to the type.
2. ✅ **Correctly handle authentication** - Understand the differences between public, protected, and iframe pages.
3. ✅ **Follow best practices** - Use project-agreed code standards and architecture.
4. ✅ **Solve common problems** - Quickly locate and fix problems in development.

Remember: Before developing new features, understand the existing components and tools to avoid reinventing the wheel!

### User Authentication and Session Management

- Always validate `/api/user/get_current` response codes for authentication status.
- Clear token and redirect to login page on these codes:
    - `ERROR_CODE_UNAUTHORIZED = 401`
    - `ERROR_CODE_NO_PERMISSION = 402`
    - `ERROR_CODE_USER_NOT_LOGIN = 601`
    - `ERROR_CODE_USER_DELETED = 603`

### Routing

- No `/workspace` page exists. Remove all references and redirects to it.
- All pages except `/embed` must be placed under the `/[locale]` directory to support internationalization.
- The root page `/` must redirect to the default locale using server-side redirection (`redirect` from `next/navigation`).

### Typescript

- Ensure all type definitions are accurate and complete to avoid runtime errors.
- Use `npx tsc --noEmit` to check for type errors.

### Bug Fixing Guidelines

- When debugging, strictly adhere to @global.mdc principles for thinking, task breakdown, and coding.
- When encountering a bug, analyze @middleware.ts and @DEVELOPMENT_GUIDE.md for relevant information.
- After identifying the issue, use `feedback mcp` to report your findings and proposed solutions.

### AuthProvider Path Skipping Logic

- The AuthProvider in `src/components/providers/auth-provider.tsx` must skip authentication checks for the `/login` path in addition to `/embed/` and `/ui-gallery`. This ensures that the login page functions correctly and avoids infinite redirects.  When using internationalized routes, ensure that AuthProvider correctly skips `/login` by checking for paths like `/[locale]/login`.
  ```typescript
  // src/components/providers/auth-provider.tsx
  if (pathname.startsWith('/embed/') ||
      pathname.startsWith('/ui-gallery/') ||
      pathname.startsWith('/login') ||
      pathname.startsWith('/[locale]/login')) {
      // Skip authentication check
  }
  ```

### Path Authentication Strategy

| Path Pattern | Authentication Required | AuthProvider Handling | Description                       |
|--------------|-------------------------|-----------------------|-----------------------------------|
| `/embed/*`   | Independent Token       | ✅ Skip               | iframe internal uses independent token verification |
| `/[locale]/ui-gallery`| No Authentication       | ✅ Skip               | Public UI component showcase page |
| `/[locale]/login`      | No Authentication       | ✅ Skip               | Login page does not require authentication |
| Other paths  | Requires JWT            | 🔒 Execute Check      | Requires valid login token          |

### AuthProvider State Management

- To prevent infinite redirects on the login page, ensure the auth store state is consistent when skipping authentication checks in `src/components/providers/auth-provider.tsx`. Specifically, if no token is present, clear the authentication state using `clearAuth()`.
  ```typescript
  // src/components/providers/auth-provider.tsx
  if (pathname.startsWith('/embed/') || 
      pathname.startsWith('/ui-gallery') ||
      pathname.startsWith('/login')) {
    console.log('🔓 AuthProvider: 跳过认证检查 -', pathname)
    
    // 🔧 Ensure auth store state is consistent, preventing async checks by PublicRoute etc.
    const token = tokenManager.getToken()
    if (!token) {
      // If no token, ensure authentication state is cleared
      clearAuth()
    }
    
    setIsInitialized(true)
    return
  }
  ```

### Internationalization Configuration

- Ensure `next-intl` middleware is correctly integrated for handling internationalized routes.
- The `[locale]` layout must be correctly configured to wrap the application with `NextIntlClientProvider`.
- The `getRequestConfig` function in `src/lib/i18n/request.ts` must correctly return the locale.
- The middleware.ts must correctly handle internationalization.

### Internationalization Routing Structure
- Project routes should follow a consistent internationalization (i18n) structure using Next.js App Router:
    - All public and protected pages should be placed under the `/[locale]` directory.
    - The root `/` path should redirect to the default locale using server-side redirection in `src/app/page.tsx`.
    - Use `next-intl` middleware to handle locale detection and routing.

### Root level redirection
- Since all pages now live under the `/[locale]` directory, a root level page (`src/app/page.tsx`) handles the redirection to the default locale.
- The `src/app/page.tsx` file should use `redirect` from `next/navigation` to perform a server-side redirect to the default locale, specified in `src/lib/i18n/config.ts`
- The `src/app/page.tsx` should not include any client-side logic or `useEffect` hooks for redirection.

### I18n middleware integration
- Integrate `next-intl`'s middleware in `middleware.ts` to handle locale detection and routing.
- The middleware should correctly rewrite requests to include the locale.