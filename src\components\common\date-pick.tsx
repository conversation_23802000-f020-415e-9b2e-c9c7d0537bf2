"use client"

import * as React from "react"
import { CalendarIcon } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

function formatDate(date: Date | undefined) {
  if (!date) {
    return ""
  }

  return date.toLocaleDateString("en-US", {
    day: "2-digit",
    month: "long",
    year: "numeric",
  })
}

function isValidDate(date: Date | undefined) {
  if (!date) {
    return false
  }
  return !isNaN(date.getTime())
}

// 格式化日期为中文格式
function formatDateChinese(date: Date | undefined) {
  if (!date) {
    return ""
  }

  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
  const weekday = weekdays[date.getDay()]

  return `${year}年${month}月${day}日 ${weekday}`
}

// 检查日期是否在一个星期前到当前日期之间
function isWithinWeekRange(date: Date): boolean {
  const oneWeekAgo = new Date()
  oneWeekAgo.setDate(oneWeekAgo.getDate() - 7)
  const today = new Date()
  
  // 设置时间为当天的开始和结束，确保比较准确
  oneWeekAgo.setHours(0, 0, 0, 0)
  today.setHours(23, 59, 59, 999)
  date.setHours(12, 0, 0, 0) // 设置选中日期为中午，避免时区问题
  
  return date >= oneWeekAgo && date <= today
}



// 新的组件：限制只能选择一个星期前的日期
export function WeekAgoDatePicker() {
  const [open, setOpen] = React.useState(false)
  const [date, setDate] = React.useState<Date | undefined>(new Date())
  const [month, setMonth] = React.useState<Date | undefined>(date)
  const [value, setValue] = React.useState(formatDateChinese(date))

  return (
    <div className="flex items-center space-x-2">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="ghost"
            className="h-auto p-0 text-xs text-gray-400 hover:text-gray-600 hover:bg-transparent"
          >
            <CalendarIcon className="h-3 w-3 mr-1" />
            {value}
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="w-auto overflow-hidden p-0"
          align="end"
          alignOffset={-8}
          sideOffset={10}
        >
          <Calendar
            mode="single"
            selected={date}
            onSelect={(date: Date | undefined) => {
              if (date && isWithinWeekRange(date)) {
                setDate(date)
                setValue(formatDateChinese(date))
                setOpen(false)
              }
            }}
            month={month || new Date()}
            onMonthChange={setMonth}
            disabled={(date) => !isWithinWeekRange(date)}
            className="[&_.rdp-day_disabled]:opacity-30 [&_.rdp-day_disabled]:cursor-not-allowed"
          />
        </PopoverContent>
      </Popover>
    </div>
  )
}
