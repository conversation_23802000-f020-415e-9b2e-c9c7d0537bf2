/**
 * StreamingPayload处理器
 * 处理流式文本累积、状态管理、完成检测
 */

import type { StreamingMessage, StreamingPayload } from '@/types/websocket-event-type'

export interface StreamingState {
  sessionId: string
  messageId: string
  accumulated: string
  isComplete: boolean
  startedAt: Date
  completedAt?: Date
  lastUpdateAt: Date
  delta?: string // 添加delta字段支持
}

export interface StreamingUpdateEvent {
  sessionId: string
  messageId: string
  delta: string
  accumulated: string
  isComplete: boolean
  state: StreamingState
}

export interface StreamingCompleteEvent {
  sessionId: string
  messageId: string
  finalText: string
  duration: number
  state: StreamingState
}

export interface StreamingUpdateListener {
  (event: StreamingUpdateEvent): void
}

export interface StreamingCompleteListener {
  (event: StreamingCompleteEvent): void
}

export interface StreamingErrorListener {
  (error: Error, sessionId: string, messageId?: string): void
}

export class StreamingProcessor {
  private activeStreams: Map<string, StreamingState> = new Map()
  private updateListeners: Set<StreamingUpdateListener> = new Set()
  private completeListeners: Set<StreamingCompleteListener> = new Set()
  private errorListeners: Set<StreamingErrorListener> = new Set()

  // 处理流式消息
  processStreamingMessage(message: StreamingMessage): void {
    try {
      const { sessionId, payload } = message
      const messageId = this.generateMessageId(sessionId, message.timestamp)

      this.processStreamingPayload(sessionId, messageId, payload)
    } catch (error) {
      this.handleError(error as Error, message.sessionId)
    }
  }

  // 处理StreamingPayload数据
  private processStreamingPayload(
    sessionId: string,
    messageId: string,
    payload: StreamingPayload
  ): void {
    const streamKey = `${sessionId}-${messageId}`
    const now = new Date()

    // 获取或创建流式状态
    let state = this.activeStreams.get(streamKey)

    if (!state) {
      // 创建新的流式状态
      state = {
        sessionId,
        messageId,
        accumulated: '',
        isComplete: false,
        startedAt: now,
        lastUpdateAt: now,
      }
      this.activeStreams.set(streamKey, state)
    }

    // StreamingPayload只有delta和isComplete，累积文本由前端管理
    state.accumulated += payload.delta

    // 更新状态
    state.isComplete = payload.isComplete
    state.lastUpdateAt = now

    if (payload.isComplete) {
      state.completedAt = now
    }

    // 触发更新事件
    this.notifyUpdate({
      sessionId,
      messageId,
      delta: payload.delta,
      accumulated: state.accumulated,
      isComplete: payload.isComplete,
      state: { ...state },
    })

    // 如果流式完成，触发完成事件
    if (payload.isComplete) {
      const duration = state.completedAt!.getTime() - state.startedAt.getTime()

      this.notifyComplete({
        sessionId,
        messageId,
        finalText: state.accumulated,
        duration,
        state: { ...state },
      })

      // 清理已完成的流
      this.cleanupStream(streamKey)
    }
  }

  // 生成消息ID
  private generateMessageId(sessionId: string, timestamp: string): string {
    // 使用sessionId和时间戳生成唯一的消息ID
    return `${sessionId}-${new Date(timestamp).getTime()}`
  }

  // 获取活跃流状态
  getActiveStream(sessionId: string, messageId: string): StreamingState | null {
    const streamKey = `${sessionId}-${messageId}`
    return this.activeStreams.get(streamKey) || null
  }

  // 获取会话的所有活跃流
  getSessionStreams(sessionId: string): StreamingState[] {
    const streams: StreamingState[] = []
    this.activeStreams.forEach(state => {
      if (state.sessionId === sessionId) {
        streams.push({ ...state })
      }
    })
    return streams
  }

  // 强制完成流（用于错误处理或清理）
  forceCompleteStream(sessionId: string, messageId: string): void {
    const streamKey = `${sessionId}-${messageId}`
    const state = this.activeStreams.get(streamKey)

    if (state && !state.isComplete) {
      state.isComplete = true
      state.completedAt = new Date()

      const duration = state.completedAt.getTime() - state.startedAt.getTime()

      this.notifyComplete({
        sessionId,
        messageId,
        finalText: state.accumulated,
        duration,
        state: { ...state },
      })

      this.cleanupStream(streamKey)
    }
  }

  // 取消流
  cancelStream(sessionId: string, messageId: string): void {
    const streamKey = `${sessionId}-${messageId}`
    this.cleanupStream(streamKey)
  }

  // 清理过期的流（超过指定时间未更新）
  cleanupStaleStreams(maxAge: number = 5 * 60 * 1000): void {
    // 5分钟
    const now = new Date()
    const staleSteams: string[] = []

    this.activeStreams.forEach((state, key) => {
      const age = now.getTime() - state.lastUpdateAt.getTime()
      if (age > maxAge) {
        staleSteams.push(key)
      }
    })

    staleSteams.forEach(key => {
      this.cleanupStream(key)
    })
  }

  // 事件监听器管理
  addUpdateListener(listener: StreamingUpdateListener): void {
    this.updateListeners.add(listener)
  }

  removeUpdateListener(listener: StreamingUpdateListener): void {
    this.updateListeners.delete(listener)
  }

  addCompleteListener(listener: StreamingCompleteListener): void {
    this.completeListeners.add(listener)
  }

  removeCompleteListener(listener: StreamingCompleteListener): void {
    this.completeListeners.delete(listener)
  }

  addErrorListener(listener: StreamingErrorListener): void {
    this.errorListeners.add(listener)
  }

  removeErrorListener(listener: StreamingErrorListener): void {
    this.errorListeners.delete(listener)
  }

  // 清理所有监听器
  clearAllListeners(): void {
    this.updateListeners.clear()
    this.completeListeners.clear()
    this.errorListeners.clear()
  }

  // 获取统计信息
  getStats(): {
    activeStreams: number
    totalListeners: number
    oldestStreamAge: number | null
  } {
    const now = new Date()
    let oldestAge: number | null = null

    this.activeStreams.forEach(state => {
      const age = now.getTime() - state.startedAt.getTime()
      if (oldestAge === null || age > oldestAge) {
        oldestAge = age
      }
    })

    return {
      activeStreams: this.activeStreams.size,
      totalListeners:
        this.updateListeners.size + this.completeListeners.size + this.errorListeners.size,
      oldestStreamAge: oldestAge,
    }
  }

  // 私有方法：通知更新
  private notifyUpdate(event: StreamingUpdateEvent): void {
    this.updateListeners.forEach(listener => {
      try {
        listener(event)
      } catch (error) {
        // Error in streaming update listener
      }
    })
  }

  // 私有方法：通知完成
  private notifyComplete(event: StreamingCompleteEvent): void {
    this.completeListeners.forEach(listener => {
      try {
        listener(event)
      } catch (error) {
        // Error in streaming complete listener
      }
    })
  }

  // 私有方法：处理错误
  private handleError(error: Error, sessionId: string, messageId?: string): void {
    // Log streaming processor error for debugging
    this.errorListeners.forEach(listener => {
      try {
        listener(error, sessionId, messageId)
      } catch (listenerError) {
        // Error in streaming error listener
      }
    })
  }

  // 私有方法：清理流
  private cleanupStream(streamKey: string): void {
    this.activeStreams.delete(streamKey)
  }
}
