// lib/request.ts
import axios, { AxiosRequestConfig, AxiosResponse } from 'axios'
import { handleCriticalError, handleError } from './error-handler'

// :wrench: API响应格式类型定义
export interface ApiResponse<T = any> {
  code: number
  data: T
  success: boolean
  message: string
}
// 创建 axios 实例，配置基础地址和超时
export const request = axios.create({
  baseURL: '/api', // 使用本地API代理
  timeout: 15000, // 15秒超时
  withCredentials: true, // 支持跨域携带cookie
})
// 请求拦截器，设置默认请求头
request.interceptors.request.use(
  config => {
    // :wrench: 设置默认请求头
    config.headers['Content-Type'] = config.headers['Content-Type'] || 'application/json'
    console.log(':outbox_tray: API请求:', {
      url: config.url,
      method: config.method?.toUpperCase(),
      data: config.data,
    })
    return config
  },
  error => {
    console.error(':x: 请求拦截器错误:', error)
    return Promise.reject(error)
  }
)
// 响应拦截器，统一处理响应数据或错误，集成 toast 错误显示
request.interceptors.response.use(
  (response: AxiosResponse) => {
    console.log(':inbox_tray: API响应:', {
      url: response.config.url,
      status: response.status,
      data: response.data,
    })
    const data = response.data
    // :wrench: 检测并处理不同的响应格式
    // 1. 后端包装格式 { code, data, success, message }
    if (typeof data === 'object' && 'success' in data && 'code' in data) {
      if (data.success && data.code === 200) {
        // 成功响应，返回完整的包装格式
        return response
      } else {
        // API返回错误状态，使用新的错误处理系统
        const error = new Error(data.message || '请求失败')
        ;(error as any).code = data.code
        ;(error as any).response = data
        ;(error as any).isApiError = true

        // 集成错误处理 - 自动显示 toast
        handleError(error, {
          showToast: true,
          logError: true,
          customMessage: data.message || '操作失败，请重试',
        })

        throw error
      }
    }
    // 2. Better Auth 格式 { data?, error? } 或直接数据
    // 直接返回原始响应，让调用方处理
    return response
  },
  error => {
    console.error(':x: 网络或HTTP错误:', {
      message: error.message,
      status: error.response?.status,
      data: error.response?.data,
    })

    // 使用新的错误处理系统，根据错误严重程度决定处理方式
    const status = error.response?.status

    if (status >= 500) {
      // 服务器错误 - 显示友好提示并支持重试
      handleError(error, {
        showToast: true,
        logError: true,
        onRetry: () => {
          // 这里可以实现自动重试逻辑
          console.log('用户点击重试')
        },
      })
    } else if (status === 401) {
      // 认证错误 - 特殊处理
      console.warn(':closed_lock_with_key: 认证失败，Better Auth 将自动处理')
      handleError(error, {
        showToast: true,
        logError: true,
        customMessage: '登录已过期，请重新登录',
      })
      // Better Auth 使用 cookie 认证，无需手动清除 token
      // 可以在这里跳转到登录页面或触发重新认证
      // window.location.href = '/login'
    } else if (status === 403) {
      // 权限错误
      handleError(error, {
        showToast: true,
        logError: true,
        customMessage: '权限不足，无法执行此操作',
      })
    } else if (status >= 400) {
      // 客户端错误 - 显示具体错误信息
      handleError(error, {
        showToast: true,
        logError: true,
      })
    } else if (!error.response) {
      // 网络连接错误 - 高优先级错误处理
      handleCriticalError(error)
    } else {
      // 其他未知错误
      handleError(error, {
        showToast: true,
        logError: true,
      })
    }

    return Promise.reject(error)
  }
)
// :wrench: 封装方法 - 返回完整响应，由调用方决定如何处理
export function get<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
  return request.get<T>(url, config)
}
export function post<T = any>(
  url: string,
  data?: any,
  config?: AxiosRequestConfig
): Promise<AxiosResponse<T>> {
  return request.post<T>(url, data, config)
}
export function put<T = any>(
  url: string,
  data?: any,
  config?: AxiosRequestConfig
): Promise<AxiosResponse<T>> {
  return request.put<T>(url, data, config)
}
export function del<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
  return request.delete<T>(url, config)
}
// :wrench: 便利导出
export default request
