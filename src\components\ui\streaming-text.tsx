'use client'

import { useEffect, useState, useRef } from 'react'
import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'

export interface StreamingTextProps {
  /** 当前累积的完整文本 */
  text: string
  /** 是否正在流式输出 */
  isStreaming: boolean
  /** 打字机效果速度（毫秒/字符） */
  typingSpeed?: number
  /** 流式完成回调 */
  onStreamComplete?: () => void
  /** 自定义样式类 */
  className?: string
  /** 是否显示光标 */
  showCursor?: boolean
  /** 是否启用打字机效果 */
  enableTyping?: boolean
}

export const StreamingText = ({
  text,
  isStreaming,
  typingSpeed = 30,
  onStreamComplete,
  className,
  showCursor = true,
  enableTyping = true,
}: StreamingTextProps) => {
  const [displayedText, setDisplayedText] = useState('')
  const [currentIndex, setCurrentIndex] = useState(0)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const previousTextRef = useRef('')

  // 调试日志
  console.log('🎬 StreamingText component render:', {
    text: text,
    textLength: text?.length,
    isStreaming: isStreaming,
    displayedText: displayedText,
    displayedLength: displayedText?.length,
    enableTyping: enableTyping,
  })

  // 当文本更新时，处理增量显示
  useEffect(() => {
    if (!enableTyping) {
      setDisplayedText(text)
      return
    }

    // 如果是新的文本（比之前的文本长），开始打字机效果
    if (text.length > previousTextRef.current.length) {
      // 清除之前的定时器
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }

      // 从当前显示的位置开始继续打字
      const startIndex = displayedText.length
      let index = startIndex

      intervalRef.current = setInterval(() => {
        if (index < text.length) {
          setDisplayedText(text.slice(0, index + 1))
          setCurrentIndex(index + 1)
          index++
        } else {
          // 打字完成
          if (intervalRef.current) {
            clearInterval(intervalRef.current)
            intervalRef.current = null
          }

          // 如果流式已完成且文本也显示完成，触发回调
          if (!isStreaming && onStreamComplete) {
            onStreamComplete()
          }
        }
      }, typingSpeed)
    }

    previousTextRef.current = text

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [text, typingSpeed, enableTyping, displayedText.length, isStreaming, onStreamComplete])

  // 当流式完成但文本已经全部显示时，触发回调
  useEffect(() => {
    if (!isStreaming && displayedText === text && text.length > 0 && onStreamComplete) {
      onStreamComplete()
    }
  }, [isStreaming, displayedText, text, onStreamComplete])

  // 清理定时器
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [])

  return (
    <div className={cn('relative', className)}>
      <div className="whitespace-pre-wrap break-words">
        {displayedText}
        {/* 打字机光标 */}
        {showCursor && isStreaming && (
          <motion.span
            className="inline-block w-0.5 h-5 bg-current ml-1"
            animate={{ opacity: [1, 0] }}
            transition={{
              duration: 0.8,
              repeat: Infinity,
              repeatType: 'reverse',
            }}
          />
        )}
      </div>

      {/* 流式状态指示器 */}
      {isStreaming && (
        <div className="mt-2 flex items-center text-xs text-muted-foreground">
          <motion.div className="flex space-x-1" initial={{ opacity: 0 }} animate={{ opacity: 1 }}>
            <motion.div
              className="w-1 h-1 bg-current rounded-full"
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 0.6, repeat: Infinity, delay: 0 }}
            />
            <motion.div
              className="w-1 h-1 bg-current rounded-full"
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 0.6, repeat: Infinity, delay: 0.2 }}
            />
            <motion.div
              className="w-1 h-1 bg-current rounded-full"
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 0.6, repeat: Infinity, delay: 0.4 }}
            />
          </motion.div>
          <span className="ml-2">AI正在思考...</span>
        </div>
      )}
    </div>
  )
}

// 使用 StreamingProcessor 的 Hook
import { useEffect as useEffectHook, useState as useStateHook } from 'react'
import type { StreamingState } from '@/lib/websocket/streaming-processor'

export interface UseStreamingTextProps {
  /** 流式状态 */
  streamingState?: StreamingState
  /** 初始文本 */
  initialText?: string
}

export const useStreamingText = ({
  streamingState,
  initialText = '',
}: UseStreamingTextProps = {}) => {
  const [text, setText] = useStateHook(initialText)
  const [isStreaming, setIsStreaming] = useStateHook(false)

  useEffectHook(() => {
    if (streamingState) {
      setText(streamingState.accumulated)
      setIsStreaming(!streamingState.isComplete)
    }
  }, [streamingState])

  return {
    text,
    isStreaming,
    setText,
    setIsStreaming,
  }
}
