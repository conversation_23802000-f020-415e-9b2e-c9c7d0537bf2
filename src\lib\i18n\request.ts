/**
 * next-intl请求配置 - without i18n routing
 */

import { defaultLocale, locales, getUserLocale } from './config'
import { getRequestConfig } from 'next-intl/server'
import { headers, cookies } from 'next/headers'

export default getRequestConfig(async () => {
  // 尝试从多个来源获取locale，优先级：用户cookie > 浏览器语言 > 默认语言
  let locale = defaultLocale

  try {
    // 1. 优先从用户cookie获取语言设置
    const cookieStore = await cookies()
    const userLocaleCookie = cookieStore.get('user-locale')

    if (userLocaleCookie?.value && locales.includes(userLocaleCookie.value as any)) {
      locale = userLocaleCookie.value as typeof defaultLocale
      console.log(`[i18n] 使用用户cookie语言: ${locale}`)
      return {
        locale,
        messages: (await import(`./messages/${locale}.json`)).default,
      }
    }

    // 2. 如果没有用户cookie，从请求头获取浏览器偏好语言
    const headersList = await headers()
    const acceptLanguage = headersList.get('accept-language')

    if (acceptLanguage) {
      // 解析 Accept-Language 头
      const browserLocales = acceptLanguage
        .split(',')
        .map(lang => lang.split(';')[0].trim())
        .map(lang => {
          // 将浏览器语言代码映射到我们支持的语言
          if (lang.startsWith('zh')) return 'zh'
          if (lang.startsWith('en')) return 'en'
          if (lang.startsWith('ja')) return 'ja'
          return null
        })
        .filter(Boolean)

      // 使用第一个支持的语言
      if (browserLocales.length > 0 && locales.includes(browserLocales[0] as any)) {
        locale = browserLocales[0] as typeof defaultLocale
        console.log(`[i18n] 使用浏览器语言: ${locale}`)
      }
    }
  } catch (error) {
    console.warn('[i18n] Failed to detect locale, using default:', error)
    locale = defaultLocale
  }

  return {
    locale,
    messages: (await import(`./messages/${locale}.json`)).default,
  }
})
