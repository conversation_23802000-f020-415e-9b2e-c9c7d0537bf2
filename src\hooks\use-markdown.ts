/**
 * Markdown 渲染器 Hook
 * 基于 markdown-it 提供完整的 markdown 渲染功能
 */

'use client'

import { useMemo } from 'react'
import MarkdownIt from 'markdown-it'
import hljs from 'highlight.js'

// 导入高亮样式
import 'highlight.js/styles/github.css'

interface UseMarkdownOptions {
  /**
   * 是否启用 HTML 标签
   * @default true
   */
  html?: boolean
  /**
   * 是否启用链接自动识别
   * @default true
   */
  linkify?: boolean
  /**
   * 是否启用排版优化
   * @default true
   */
  typographer?: boolean
  /**
   * 是否启用代码高亮
   * @default true
   */
  highlight?: boolean
  /**
   * 是否启用换行转换
   * @default true
   */
  breaks?: boolean
}

const defaultOptions: UseMarkdownOptions = {
  html: true,
  linkify: true,
  typographer: true,
  highlight: true,
  breaks: true,
}

/**
 * 代码高亮函数
 */
const highlightCode = (str: string, lang: string) => {
  if (!lang || !hljs.getLanguage(lang)) {
    return `<pre class="markdown-code-block"><code>${str}</code></pre>`
  }

  try {
    const result = hljs.highlight(str, { language: lang, ignoreIllegals: true })
    return `<pre class="markdown-code-block"><code class="hljs language-${lang}">${result.value}</code></pre>`
  } catch (error) {
    console.warn('代码高亮失败:', error)
    return `<pre class="markdown-code-block"><code>${str}</code></pre>`
  }
}

/**
 * 使用 Markdown 渲染器
 */
export function useMarkdown(options: UseMarkdownOptions = {}) {
  const md = useMemo(() => {
    const config = { ...defaultOptions, ...options }

    // 创建 markdown-it 实例
    const mdInstance = new MarkdownIt({
      html: config.html,
      linkify: config.linkify,
      typographer: config.typographer,
      breaks: config.breaks,
      highlight: config.highlight ? highlightCode : undefined,
      langPrefix: 'language-',
    })

    // 自定义渲染规则

    // 表格样式
    mdInstance.renderer.rules.table_open = () => {
      return '<div class="markdown-table-wrapper"><table class="markdown-table">'
    }
    mdInstance.renderer.rules.table_close = () => {
      return '</table></div>'
    }

    // 代码块样式
    mdInstance.renderer.rules.code_block = (tokens, idx) => {
      const token = tokens[idx]
      const content = token.content
      return `<pre class="markdown-code-block"><code>${content}</code></pre>`
    }

    // 行内代码样式
    mdInstance.renderer.rules.code_inline = (tokens, idx) => {
      const token = tokens[idx]
      const content = token.content
      return `<code class="markdown-inline-code">${content}</code>`
    }

    return mdInstance
  }, [options])

  /**
   * 渲染 markdown 文本为 HTML
   */
  const renderMarkdown = (content: string): string => {
    try {
      return md.render(content)
    } catch (error) {
      console.error('Markdown 渲染失败:', error)
      return `<p class="markdown-error">渲染失败: ${error instanceof Error ? error.message : String(error)}</p>`
    }
  }

  /**
   * 渲染 markdown 文本为 HTML（内联模式）
   */
  const renderMarkdownInline = (content: string): string => {
    try {
      return md.renderInline(content)
    } catch (error) {
      console.error('Markdown 内联渲染失败:', error)
      return content
    }
  }

  /**
   * 验证 markdown 语法
   */
  const validateMarkdown = (content: string): boolean => {
    try {
      md.parse(content, {})
      return true
    } catch (error) {
      console.error('Markdown 语法错误:', error)
      return false
    }
  }

  /**
   * 提取 markdown 中的标题
   */
  const extractHeadings = (content: string): Array<{ level: number; text: string; id: string }> => {
    try {
      const tokens = md.parse(content, {})
      const headings: Array<{ level: number; text: string; id: string }> = []

      tokens.forEach((token, idx) => {
        if (token.type === 'heading_open') {
          const level = parseInt(token.tag.slice(1), 10)
          const nextToken = tokens[idx + 1]

          if (nextToken && nextToken.type === 'inline') {
            const text = nextToken.content
            const id = text.toLowerCase().replace(/[^a-z0-9\u4e00-\u9fa5]/g, '-')
            headings.push({ level, text, id })
          }
        }
      })

      return headings
    } catch (error) {
      console.error('提取标题失败:', error)
      return []
    }
  }

  return {
    renderMarkdown,
    renderMarkdownInline,
    validateMarkdown,
    extractHeadings,
    md, // 原始 markdown-it 实例，用于高级用法
  }
}

/**
 * 默认的 markdown 样式类名
 */
export const markdownClassNames = {
  container: 'markdown-content',
  heading: 'markdown-heading',
  paragraph: 'markdown-paragraph',
  list: 'markdown-list',
  listItem: 'markdown-list-item',
  table: 'markdown-table',
  tableWrapper: 'markdown-table-wrapper',
  codeBlock: 'markdown-code-block',
  inlineCode: 'markdown-inline-code',
  blockquote: 'markdown-blockquote',
  link: 'markdown-link',
  image: 'markdown-image',
  hr: 'markdown-hr',
  strong: 'markdown-strong',
  em: 'markdown-em',
  error: 'markdown-error',
} as const

/**
 * 预设的 markdown 配置
 */
export const markdownPresets = {
  // 基础配置
  basic: {
    html: false,
    linkify: true,
    typographer: true,
    highlight: false,
    breaks: true,
  },
  // 完整配置
  full: {
    html: true,
    linkify: true,
    typographer: true,
    highlight: true,
    breaks: true,
  },
  // 安全配置（用于用户生成内容）
  safe: {
    html: false,
    linkify: true,
    typographer: true,
    highlight: true,
    breaks: true,
  },
} as const

export default useMarkdown
