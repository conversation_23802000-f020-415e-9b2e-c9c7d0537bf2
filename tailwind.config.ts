import type { Config } from 'tailwindcss'

const config: Config = {
  content: [
    './src/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    '*.{js,ts,jsx,tsx,mdx}',
  ],
  safelist: [
    // Figma 设计系统类 - 确保这些类被包含在构建中
    'bg-figma-user-bg',
    'bg-figma-assistant-bg',
    'bg-figma-user-avatar',
    'text-figma-text',
    'text-figma-timestamp',
    'text-figma-username',
    'px-[22px]',
    'py-[14px]',
    'rounded-[26px]',
    'max-w-[606px]',
    'max-w-[714px]',
    'w-[32px]',
    'h-[32px]',
    'rounded-[9999px]',
    'text-[16px]',
    'text-[11px]',
    'font-[400]',
    'font-[500]',
    'leading-[1.5em]',
  ],
  theme: {
    extend: {
      screens: {
        xs: '475px',
      },
      fontFamily: {
        inter: ['Inter', 'sans-serif'], // 添加Inter字体
      },
      colors: {
        primary: 'oklch(0.8371 0.0649 267.5453)',
        secondary: 'oklch(0.9486 0.0228 266.9082)',
        // Figma 设计颜色 - 基于最新Figma设计规范
        'figma-assistant-bg': '#E7EEFE', // AI助手消息背景色
        'figma-user-bg': '#F7F7F7', // 用户消息背景色
        'figma-user-avatar': '#DBEAFE', // 用户头像背景色
        'figma-text': '#252525', // 主要文本颜色
        'figma-timestamp': '#9CA3AF', // 时间戳颜色
        'figma-username': '#252525', // 用户名颜色
      },
      spacing: {
        safe: 'env(safe-area-inset-bottom)',
      },
    },
  },
  plugins: [require('tailwindcss-animate'), require('@tailwindcss/typography')],
} as any
export default config
