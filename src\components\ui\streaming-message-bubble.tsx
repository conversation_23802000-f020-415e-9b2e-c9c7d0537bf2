'use client'

/**
 * StreamingMessageBubble - 简化的流式消息气泡组件
 * 专注于streaming文本场景，严格按照Figma设计实现
 * 大幅简化了原有unified-message的复杂结构
 */

import { useState, memo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { cn } from '@/lib/utils'

// 设计系统
import { getMessageBubbleClasses, getContainerClasses, tailwindClasses } from './design-tokens'

// 组件导入
import { TextGenerateEffect } from './text-generate-effect'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { User, Bot, Copy, Check } from 'lucide-react'

export interface StreamingMessageBubbleProps {
  /** 消息内容 */
  content: string
  /** 是否正在流式输出 */
  isStreaming: boolean
  /** 消息角色 */
  role: 'user' | 'assistant'
  /** 消息ID */
  id: string
  /** 时间戳 */
  timestamp?: Date
  /** 显示设置 */
  showAvatar?: boolean
  showTimestamp?: boolean
  /** 头像URL */
  userAvatarUrl?: string | undefined
  assistantAvatarUrl?: string | undefined
  /** 交互功能 */
  enableCopy?: boolean
  onCopy?: ((content: string) => void) | undefined
  /** 自定义样式 */
  className?: string
}

const StreamingMessageBubbleComponent = ({
  content,
  isStreaming,
  role,
  id,
  timestamp,
  showAvatar = true,
  showTimestamp: _showTimestamp = true,
  userAvatarUrl,
  assistantAvatarUrl,
  enableCopy = false,
  onCopy,
  className,
}: StreamingMessageBubbleProps) => {
  const [copied, setCopied] = useState(false)
  const [isHovered, setIsHovered] = useState(false)

  const isUser = role === 'user'
  const isAssistant = role === 'assistant'

  // 头像信息
  const avatarInfo = {
    src: isUser ? userAvatarUrl : assistantAvatarUrl,
    fallback: isUser ? <User className="w-4 h-4" /> : <Bot className="w-4 h-4" />,
    className: isUser ? tailwindClasses.userAvatarBg : 'bg-primary text-primary-foreground',
  }

  // 复制功能
  const handleCopy = async () => {
    if (!content.trim()) return

    try {
      await navigator.clipboard.writeText(content)
      setCopied(true)
      onCopy?.(content)

      // 2秒后重置复制状态
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('复制失败:', error)
    }
  }

  // 格式化时间戳
  const formatTimestamp = (date: Date) => {
    return date.toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
    })
  }

  return (
    <motion.div
      className={cn(getContainerClasses(role), className)}
      initial={{ opacity: 0, y: 20, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      transition={{ duration: 0.3 }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* 头像 */}
      {showAvatar && (
        <Avatar className={tailwindClasses.avatar}>
          {avatarInfo.src && <AvatarImage src={avatarInfo.src} />}
          <AvatarFallback className={avatarInfo.className}>{avatarInfo.fallback}</AvatarFallback>
        </Avatar>
      )}

      <div className="flex flex-col gap-1 flex-1 min-w-0">
        {/* 消息头部信息 - 匹配Figma布局 - 强制显示 */}
        <div
          className={cn(
            'flex items-center gap-2 px-1',
            isUser ? 'flex-row-reverse justify-start' : 'justify-start'
          )}
        >
          <span className={tailwindClasses.username}>
            {isUser ? 'You' : isAssistant ? '风险官 Rick' : 'System'}
          </span>
          {timestamp && (
            <span className={tailwindClasses.timestamp}>{formatTimestamp(timestamp)}</span>
          )}
        </div>

        {/* 🐛 临时调试信息 */}
        <div className="text-xs text-red-500 px-1 border border-red-300 bg-red-50 mb-1">
          DEBUG: role={role}, timestamp={timestamp ? 'exists' : 'null'}, id={id}
        </div>

        {/* 消息气泡 */}
        <div className={cn('relative flex', isUser ? 'justify-end' : 'justify-start')}>
          <div className={cn(getMessageBubbleClasses(role), 'relative group/bubble')}>
            {/* 消息内容 */}
            {isStreaming ? (
              <TextGenerateEffect
                words={content}
                isStreaming={isStreaming}
                showCursor={isStreaming}
                characterDelay={30}
                filter={false}
                className="text-inherit"
              />
            ) : (
              <div className="text-inherit">{content}</div>
            )}
          </div>

          {/* 交互按钮 */}
          <AnimatePresence>
            {isHovered && enableCopy && content.trim() && (
              <motion.div
                className={cn('absolute -bottom-8 flex gap-1', isUser ? 'left-0' : 'right-0')}
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.2 }}
              >
                <Button
                  size="sm"
                  variant="ghost"
                  className="h-6 w-6 p-0 bg-background shadow-sm border"
                  onClick={handleCopy}
                  title="复制"
                >
                  {copied ? (
                    <Check className="w-3 h-3 text-green-500" />
                  ) : (
                    <Copy className="w-3 h-3" />
                  )}
                </Button>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </motion.div>
  )
}

// 🎯 React.memo优化 - 简化的比较函数
export const StreamingMessageBubble = memo(
  StreamingMessageBubbleComponent,
  (prevProps, nextProps) => {
    // 只比较关键属性，避免过度优化
    return (
      prevProps.content === nextProps.content &&
      prevProps.isStreaming === nextProps.isStreaming &&
      prevProps.role === nextProps.role &&
      prevProps.id === nextProps.id &&
      prevProps.showAvatar === nextProps.showAvatar &&
      prevProps.showTimestamp === nextProps.showTimestamp
    )
  }
)

StreamingMessageBubble.displayName = 'StreamingMessageBubble'
