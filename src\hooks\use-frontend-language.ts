'use client'

import { useState, useTransition } from 'react'
import { useLocale } from 'next-intl'
import { useRouter } from 'next/navigation'
import { locales, type Locale, localeNames } from '@/lib/i18n/config'

interface UseFrontendLanguageReturn {
  currentLocale: Locale
  availableLanguages: Array<{ value: Locale; label: string; icon: string }>
  isChanging: boolean
  changeLanguage: (locale: Locale) => void
}

const languageIcons: Record<Locale, string> = {
  zh: '🇨🇳',
  en: '🇺🇸',
  ja: '🇯🇵',
}

/**
 * 纯前端语言切换Hook
 * 通过设置cookie和重新渲染实现语言切换，不刷新页面
 */
export function useFrontendLanguage(): UseFrontendLanguageReturn {
  const currentLocale = useLocale() as Locale
  const router = useRouter()
  const [isPending, startTransition] = useTransition()
  const [isChanging, setIsChanging] = useState(false)

  // 可用语言列表
  const availableLanguages = locales.map(locale => ({
    value: locale,
    label: localeNames[locale],
    icon: languageIcons[locale],
  }))

  /**
   * 设置用户语言cookie
   * @param locale 语言代码
   */
  const setUserLocaleCookie = (locale: Locale) => {
    const maxAge = 30 * 24 * 60 * 60 // 30天
    document.cookie = `user-locale=${locale}; path=/; max-age=${maxAge}; samesite=lax`
    console.log(`🌍 设置用户语言cookie: ${locale}`)
  }

  /**
   * 切换语言
   * @param locale 目标语言代码
   */
  const changeLanguage = (locale: Locale) => {
    if (locale === currentLocale || isChanging || isPending) {
      return
    }

    setIsChanging(true)

    try {
      console.log(`🔄 切换前端语言: ${currentLocale} → ${locale}`)

      // 设置cookie
      setUserLocaleCookie(locale)

      // 使用 startTransition 进行无刷新的路由更新
      startTransition(() => {
        // 刷新当前路由以重新读取cookie
        router.refresh()
        setIsChanging(false)
      })
    } catch (error) {
      console.error('❌ 前端语言切换失败:', error)
      setIsChanging(false)
    }
  }

  return {
    currentLocale,
    availableLanguages,
    isChanging: isChanging || isPending,
    changeLanguage,
  }
}
