/**
 * WebSocket连接管理器
 * 负责Socket.IO连接、认证、重连机制
 * 使用标准Socket.IO客户端实例
 */

import type { Socket } from 'socket.io-client'
import { createSocketClient } from '@/lib/socket'
import type { BaseWebSocketMessage, MessagePayload } from '@/types/websocket-event-type'

export enum ConnectionStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error',
}

export interface SocketManagerConfig {
  url: string
  auth?: {
    token?: string
    userId?: string
    organizationId?: string
  }
  options?: {
    reconnection?: boolean
    reconnectionAttempts?: number
    reconnectionDelay?: number
    timeout?: number
  }
}

export interface MessageHandler {
  (message: BaseWebSocketMessage): void
}

export class SocketManager {
  private socket: Socket
  private config: SocketManagerConfig
  private messageHandlers: Set<MessageHandler> = new Set()
  private connectionStatus: ConnectionStatus = ConnectionStatus.DISCONNECTED
  private statusListeners: Set<(status: ConnectionStatus) => void> = new Set()

  constructor(config: SocketManagerConfig) {
    // 使用标准Socket.IO客户端实例
    this.socket = createSocketClient(config)
    this.config = {
      ...config,
      options: {
        reconnection: true,
        reconnectionAttempts: 5,
        reconnectionDelay: 1000,
        timeout: 20000,
        ...config.options,
      },
    }
  }

  // 连接到WebSocket服务器
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.socket.connected) {
        console.log('🔗 SocketManager: Already connected')
        resolve()
        return
      }

      console.log('🔗 SocketManager: Starting connection to custom server')
      console.log('🔗 Current URL:', typeof window !== 'undefined' ? window.location.origin : 'SSR')
      this.setConnectionStatus(ConnectionStatus.CONNECTING)

      // 配置认证信息（如果有）
      if (this.config.auth) {
        this.socket.auth = this.config.auth
      }

      console.log('🔗 SocketManager: Using standard Socket.IO client')

      // 设置事件监听器
      this.setupEventListeners(resolve, reject)

      // 启动连接
      this.socket.connect()

      // 设置连接超时
      setTimeout(() => {
        if (this.connectionStatus === ConnectionStatus.CONNECTING) {
          this.setConnectionStatus(ConnectionStatus.ERROR)
          reject(new Error('Connection timeout'))
        }
      }, this.config.options?.timeout || 20000)
    })
  }

  // 设置事件监听器
  private setupEventListeners(resolve: () => void, reject: (error: Error) => void): void {
    // 连接成功
    this.socket.on('connect', () => {
      this.setConnectionStatus(ConnectionStatus.CONNECTED)
      resolve()
    })

    // 连接错误 - 增强错误处理
    this.socket.on('connect_error', error => {
      // 如果是第一次连接失败，直接拒绝
      if (this.connectionStatus === ConnectionStatus.CONNECTING) {
        this.setConnectionStatus(ConnectionStatus.ERROR)
        reject(new Error(`Socket.IO连接失败: ${error.message}`))
      } else {
        // 如果是重连失败，只更新状态
        this.setConnectionStatus(ConnectionStatus.ERROR)
      }
    })

    // 断开连接
    this.socket.on('disconnect', reason => {
      console.warn('🔌 SocketManager: Disconnected:', {
        reason,
        socketId: this.socket.id,
      })
      if (reason === 'io server disconnect') {
        // 服务器主动断开，需要重新连接
        this.socket.connect()
      }
      this.setConnectionStatus(ConnectionStatus.DISCONNECTED)
    })

    // 重连尝试
    this.socket.on('reconnect_attempt', attemptNumber => {
      console.log('🔄 SocketManager: Reconnect attempt:', attemptNumber)
      this.setConnectionStatus(ConnectionStatus.RECONNECTING)
    })

    // 重连成功
    this.socket.on('reconnect', attemptNumber => {
      console.log('✅ SocketManager: Reconnected after', attemptNumber, 'attempts')
      this.setConnectionStatus(ConnectionStatus.CONNECTED)
    })

    // 监听各种类型的消息（匹配自定义服务器的事件）
    this.socket.on('connection-confirmed', (data: BaseWebSocketMessage) => {
      console.log('Connection confirmed:', data)
      this.handleMessage(data)
    })

    this.socket.on('streaming-message', (data: BaseWebSocketMessage) => {
      this.handleMessage(data)
    })

    this.socket.on('checkpoint-message', (data: BaseWebSocketMessage) => {
      this.handleMessage(data)
    })

    this.socket.on('report-message', (data: BaseWebSocketMessage) => {
      this.handleMessage(data)
    })

    this.socket.on('error-message', (data: BaseWebSocketMessage) => {
      this.handleMessage(data)
    })

    // 通用消息监听（兼容性）
    this.socket.on('message', (data: BaseWebSocketMessage) => {
      this.handleMessage(data)
    })
  }

  // 断开连接
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect()
      // 移除所有事件监听器
      this.socket.removeAllListeners()
    }
    this.setConnectionStatus(ConnectionStatus.DISCONNECTED)
  }

  // 发送消息
  sendMessage(data: Partial<BaseWebSocketMessage>): void {
    if (!this.socket.connected) {
      throw new Error('Socket not connected')
    }

    const message: BaseWebSocketMessage = {
      groupChatId: data.groupChatId || '',
      sessionId: data.sessionId || '',
      userId: this.config.auth?.userId || '',
      organizationId: this.config.auth?.organizationId || '',
      timestamp: new Date().toISOString(),
      ...data,
    } as BaseWebSocketMessage

    this.socket.emit('user-message', message)
  }

  // 添加消息处理器
  addMessageHandler(handler: MessageHandler): void {
    this.messageHandlers.add(handler)
  }

  // 移除消息处理器
  removeMessageHandler(handler: MessageHandler): void {
    this.messageHandlers.delete(handler)
  }

  // 添加连接状态监听器
  addStatusListener(listener: (status: ConnectionStatus) => void): void {
    this.statusListeners.add(listener)
  }

  // 移除连接状态监听器
  removeStatusListener(listener: (status: ConnectionStatus) => void): void {
    this.statusListeners.delete(listener)
  }

  // 获取当前连接状态
  getConnectionStatus(): ConnectionStatus {
    return this.connectionStatus
  }

  // 检查是否已连接
  isConnected(): boolean {
    return this.connectionStatus === ConnectionStatus.CONNECTED && this.socket.connected === true
  }

  // 更新认证信息
  updateAuth(auth: SocketManagerConfig['auth']): void {
    this.config.auth = { ...this.config.auth, ...auth }

    // 如果已连接，需要重新连接以更新认证
    if (this.socket.connected) {
      this.disconnect()
      setTimeout(() => this.connect(), 100)
    }
  }

  // 私有方法：处理收到的消息
  private handleMessage(message: BaseWebSocketMessage): void {
    // 验证消息格式
    if (!this.isValidMessage(message)) {
      console.warn('Invalid message received:', message)
      return
    }

    // 分发消息给所有处理器
    this.messageHandlers.forEach(handler => {
      try {
        handler(message)
      } catch (error) {
        console.error('Error in message handler:', error)
      }
    })
  }

  // 私有方法：验证消息格式
  private isValidMessage(message: any): message is BaseWebSocketMessage {
    return (
      message &&
      typeof message === 'object' &&
      typeof message.groupChatId === 'string' &&
      typeof message.sessionId === 'string' &&
      typeof message.userId === 'string' &&
      typeof message.organizationId === 'string' &&
      typeof message.timestamp === 'string' &&
      message.payload &&
      typeof message.payload === 'object' &&
      typeof message.payload.type === 'string'
    )
  }

  // 私有方法：设置连接状态
  private setConnectionStatus(status: ConnectionStatus): void {
    if (this.connectionStatus !== status) {
      this.connectionStatus = status
      this.statusListeners.forEach(listener => {
        try {
          listener(status)
        } catch (error) {
          console.error('Error in status listener:', error)
        }
      })
    }
  }
}
