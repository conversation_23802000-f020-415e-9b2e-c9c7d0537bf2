/**
 * 会话管理 Store
 * 
 * 功能：群聊会话创建、切换、生命周期管理
 * 依赖：BaseWebSocketMessage类型定义
 * 性能：轻量级会话状态管理，优化了群聊切换逻辑
 * 
 * 数据流：User Action -> Session Management -> Store -> Message Store
 */

import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'
import { subscribeWithSelector } from 'zustand/middleware'

import type { BaseWebSocketMessage } from '@/types/websocket-event-type'

// ============================================================================
// Session Interface
// ============================================================================

export interface ChatSession {
  sessionId: string
  groupChatId: string
  userId: string
  organizationId: string
  messages: BaseWebSocketMessage[]
  isActive: boolean
  createdAt: Date
  updatedAt: Date
  
  // 会话元数据
  metadata?: {
    title?: string
    lastMessagePreview?: string
    unreadCount?: number
    tags?: string[]
  }
}

// ============================================================================
// Session State Interface
// ============================================================================

export interface SessionState {
  // 当前活跃状态
  currentGroupId: string | undefined
  currentSession: ChatSession | undefined

  // 群聊到会话的映射 - 一个群聊对应一个活跃会话
  groupToSessionMap: Record<string, string>

  // 所有会话数据
  sessions: Record<string, ChatSession>
}

// ============================================================================
// Session Actions Interface
// ============================================================================

export interface SessionActions {
  // 群聊管理 - 核心方法
  setCurrentGroup: (groupId: string) => void
  getActiveSessionByGroupId: (groupId: string) => ChatSession | undefined
  ensureSessionForGroup: (groupId: string, userId: string, organizationId: string) => ChatSession

  // Mock模式便捷方法
  ensureMockSessionForGroup: (groupId: string) => ChatSession

  // 传统会话管理 - 兼容性方法
  createSession: (groupChatId: string, userId: string, organizationId: string) => string
  setCurrentSession: (sessionId: string) => void
  getCurrentSession: (sessionId: string) => ChatSession | undefined
  clearSession: (sessionId: string) => void

  // 会话信息更新
  updateSessionMetadata: (sessionId: string, metadata: Partial<ChatSession['metadata']>) => void
  updateSessionTimestamp: (sessionId: string) => void

  // 批量操作
  clearAllSessions: () => void
  getSessionList: () => ChatSession[]
  getActiveSessionCount: () => number

  // 会话查询
  findSessionsByUserId: (userId: string) => ChatSession[]
  findSessionsByGroupId: (groupId: string) => ChatSession[]
}

export type SessionStore = SessionState & SessionActions

// ============================================================================
// Utility Functions
// ============================================================================

/**
 * 生成会话ID
 */
const generateSessionId = (): string => {
  const timestamp = Date.now()
  const randomStr = Math.random().toString(36).substring(2, 9)
  return `session-${timestamp}-${randomStr}`
}

/**
 * 创建新会话
 */
const createNewSession = (
  sessionId: string,
  groupChatId: string,
  userId: string,
  organizationId: string
): ChatSession => {
  const now = new Date()
  
  return {
    sessionId,
    groupChatId,
    userId,
    organizationId,
    messages: [],
    isActive: true,
    createdAt: now,
    updatedAt: now,
    metadata: {
      title: `群聊 ${groupChatId}`,
      lastMessagePreview: '',
      unreadCount: 0,
      tags: [],
    },
  }
}

// ============================================================================
// Session Store Implementation
// ============================================================================

export const useSessionStore = create<SessionStore>()(
  subscribeWithSelector(
    immer((set, get) => ({
      // ============================================================================
      // 初始状态
      // ============================================================================
      currentGroupId: undefined,
      currentSession: undefined,
      groupToSessionMap: {},
      sessions: {},

      // ============================================================================
      // 群聊管理方法 - 核心功能
      // ============================================================================

      setCurrentGroup: (groupId: string) => {
        set(state => {
          console.log('🏠 SET_CURRENT_GROUP:', { groupId })
          state.currentGroupId = groupId

          // 查找该群聊的活跃会话
          const sessionId = state.groupToSessionMap[groupId]
          if (sessionId && state.sessions[sessionId]) {
            state.currentSession = state.sessions[sessionId]
            console.log('✅ FOUND_ACTIVE_SESSION:', { sessionId })
          } else {
            state.currentSession = undefined
            console.log('❌ NO_ACTIVE_SESSION_FOR_GROUP:', { groupId })
          }
        })
      },

      getActiveSessionByGroupId: (groupId: string) => {
        const state = get()
        const sessionId = state.groupToSessionMap[groupId]
        return sessionId ? state.sessions[sessionId] : undefined
      },

      ensureSessionForGroup: (groupId: string, userId: string, organizationId: string) => {
        // 🔍 详细调试信息
        console.log('🔍 ensureSessionForGroup 调用参数:', {
          groupId,
          userId,
          organizationId,
          userIdType: typeof userId,
          userIdEquals_MOCK_AI_ASSISTANT_ID: userId === 'mock-assistant-bot',
        })

        let session = get().getActiveSessionByGroupId(groupId)

        if (!session) {
          // 创建新会话
          const sessionId = generateSessionId()
          session = createNewSession(sessionId, groupId, userId, organizationId)

          set(state => {
            state.sessions[sessionId] = session!
            state.groupToSessionMap[groupId] = sessionId
            state.currentGroupId = groupId
            state.currentSession = session!
          })

          console.log('🆕 CREATED_SESSION_FOR_GROUP:', { 
            groupId, 
            sessionId,
            sessionUserId: session.userId,
            userIdEquals_MOCK_AI_ASSISTANT_ID: session.userId === 'mock-assistant-bot',
          })
        } else {
          // 激活现有会话
          set(state => {
            state.currentGroupId = groupId
            state.currentSession = session!
            // 更新会话为活跃状态
            if (state.sessions[session!.sessionId]) {
              state.sessions[session!.sessionId].isActive = true
              state.sessions[session!.sessionId].updatedAt = new Date()
            }
          })

          console.log('✅ ACTIVATED_EXISTING_SESSION:', { 
            groupId, 
            sessionId: session.sessionId,
            sessionUserId: session.userId,
            userIdEquals_MOCK_AI_ASSISTANT_ID: session.userId === 'mock-assistant-bot',
          })
        }

        return session
      },

      // ============================================================================
      // Mock模式便捷方法
      // ============================================================================

      ensureMockSessionForGroup: (groupId: string) => {
        // 使用默认的mock用户信息
        const mockUserId = 'mock-user-001'
        const mockOrganizationId = 'mock-org-001'
        
        console.log('🎭 ENSURE_MOCK_SESSION:', { groupId, mockUserId, mockOrganizationId })
        
        // 委托给标准的ensureSessionForGroup方法
        return get().ensureSessionForGroup(groupId, mockUserId, mockOrganizationId)
      },

      // ============================================================================
      // 传统会话管理 - 兼容性方法
      // ============================================================================

      createSession: (groupChatId: string, userId: string, organizationId: string) => {
        const sessionId = generateSessionId()
        const session = createNewSession(sessionId, groupChatId, userId, organizationId)

        set(state => {
          state.sessions[sessionId] = session
          state.currentSession = session
          // 如果是新群聊会话，也更新映射
          if (!state.groupToSessionMap[groupChatId]) {
            state.groupToSessionMap[groupChatId] = sessionId
            state.currentGroupId = groupChatId
          }
        })

        console.log('🆕 CREATED_SESSION:', { sessionId, groupChatId })
        return sessionId
      },

      setCurrentSession: (sessionId: string) => {
        set(state => {
          const session = state.sessions[sessionId]
          if (session) {
            state.currentSession = session
            state.currentGroupId = session.groupChatId
            // 激活会话
            session.isActive = true
            session.updatedAt = new Date()
            console.log('✅ SET_CURRENT_SESSION:', { sessionId, groupId: session.groupChatId })
          } else {
            console.warn('❌ SESSION_NOT_FOUND:', { sessionId })
          }
        })
      },

      getCurrentSession: (sessionId: string) => {
        return get().sessions[sessionId]
      },

      clearSession: (sessionId: string) => {
        set(state => {
          const session = state.sessions[sessionId]
          if (session) {
            // 从群聊映射中移除
            if (state.groupToSessionMap[session.groupChatId] === sessionId) {
              delete state.groupToSessionMap[session.groupChatId]
            }
            
            // 删除会话
            delete state.sessions[sessionId]
            
            // 如果是当前会话，清空当前状态
            if (state.currentSession?.sessionId === sessionId) {
              state.currentSession = undefined
              state.currentGroupId = undefined
            }
            
            console.log('🗑️ CLEARED_SESSION:', { sessionId, groupId: session.groupChatId })
          }
        })
      },

      // ============================================================================
      // 会话信息更新
      // ============================================================================

      updateSessionMetadata: (sessionId: string, metadata: Partial<ChatSession['metadata']>) => {
        set(state => {
          const session = state.sessions[sessionId]
          if (session) {
            session.metadata = {
              ...session.metadata,
              ...metadata,
            }
            session.updatedAt = new Date()
            
            // 如果是当前会话，同步更新
            if (state.currentSession?.sessionId === sessionId) {
              state.currentSession = session
            }
            
            console.log('📝 UPDATED_SESSION_METADATA:', { sessionId, metadata })
          }
        })
      },

      updateSessionTimestamp: (sessionId: string) => {
        set(state => {
          const session = state.sessions[sessionId]
          if (session) {
            session.updatedAt = new Date()
            
            // 如果是当前会话，同步更新
            if (state.currentSession?.sessionId === sessionId) {
              state.currentSession = session
            }
          }
        })
      },

      // ============================================================================
      // 批量操作
      // ============================================================================

      clearAllSessions: () => {
        set(state => {
          console.log('🗑️ CLEARING_ALL_SESSIONS...')
          state.sessions = {}
          state.groupToSessionMap = {}
          state.currentSession = undefined
          state.currentGroupId = undefined
          console.log('✅ ALL_SESSIONS_CLEARED')
        })
      },

      getSessionList: () => {
        const state = get()
        return Object.values(state.sessions).sort(
          (a, b) => b.updatedAt.getTime() - a.updatedAt.getTime()
        )
      },

      getActiveSessionCount: () => {
        const state = get()
        return Object.values(state.sessions).filter(session => session.isActive).length
      },

      // ============================================================================
      // 会话查询
      // ============================================================================

      findSessionsByUserId: (userId: string) => {
        const state = get()
        return Object.values(state.sessions).filter(session => session.userId === userId)
      },

      findSessionsByGroupId: (groupId: string) => {
        const state = get()
        return Object.values(state.sessions).filter(session => session.groupChatId === groupId)
      },
    }))
  )
)

// ============================================================================
// Session Hook - 专门的会话管理Hook
// ============================================================================

export const useSession = () => {
  const currentSession = useSessionStore(state => state.currentSession)
  const currentGroupId = useSessionStore(state => state.currentGroupId)
  const sessions = useSessionStore(state => state.sessions)
  
  const setCurrentGroup = useSessionStore(state => state.setCurrentGroup)
  const ensureSessionForGroup = useSessionStore(state => state.ensureSessionForGroup)
  const createSession = useSessionStore(state => state.createSession)
  const clearSession = useSessionStore(state => state.clearSession)
  const updateSessionMetadata = useSessionStore(state => state.updateSessionMetadata)
  
  const getSessionList = useSessionStore(state => state.getSessionList)
  const getActiveSessionCount = useSessionStore(state => state.getActiveSessionCount)

  return {
    // 当前状态
    currentSession,
    currentGroupId,
    sessions,
    
    // 群聊操作
    setCurrentGroup,
    ensureSessionForGroup,
    
    // 会话操作
    createSession,
    clearSession,
    updateSessionMetadata,
    
    // 查询操作
    sessionList: getSessionList(),
    activeSessionCount: getActiveSessionCount(),
  }
}

// 清理函数
export const cleanupSessionStore = () => {
  const { clearAllSessions } = useSessionStore.getState()
  clearAllSessions()
  console.log('🗑️ Session Store 清理完成')
}