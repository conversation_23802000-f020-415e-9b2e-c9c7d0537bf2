/**
 * Socket.IO客户端实例
 * 连接到Python FastAPI后端的WebSocket服务
 */

'use client'

import { io, type Socket } from 'socket.io-client'

// ============================================================================
// Socket.IO客户端配置
// ============================================================================

export interface SocketConfig {
  url: string
  timeout?: number
  reconnection?: boolean
  reconnectionAttempts?: number
  reconnectionDelay?: number
  auth?: Record<string, any>
}

// ============================================================================
// 默认配置
// ============================================================================

const DEFAULT_CONFIG: Partial<SocketConfig> = {
  timeout: 20000,
  reconnection: true,
  reconnectionAttempts: 5,
  reconnectionDelay: 1000,
}

// ============================================================================
// Socket.IO客户端创建工厂
// ============================================================================

export function createSocketClient(config: SocketConfig): Socket {
  const finalConfig = { ...DEFAULT_CONFIG, ...config }

  console.log('🔌 Creating Socket.IO client with config:', {
    url: finalConfig.url,
    timeout: finalConfig.timeout,
    reconnection: finalConfig.reconnection,
    reconnectionAttempts: finalConfig.reconnectionAttempts,
  })

  const socket = io(finalConfig.url, {
    // 传输配置
    transports: ['websocket', 'polling'],
    upgrade: true,
    autoConnect: false, // 手动控制连接

    // 重连配置 - 修复类型错误
    reconnection: finalConfig.reconnection ?? true,
    reconnectionAttempts: finalConfig.reconnectionAttempts ?? 5,
    reconnectionDelay: finalConfig.reconnectionDelay ?? 1000,
    reconnectionDelayMax: 5000,

    // 超时配置
    timeout: finalConfig.timeout ?? 20000,

    // 认证配置
    auth: finalConfig.auth || {},

    // 其他配置
    forceNew: true,
    rememberUpgrade: false,
  })

  // 添加调试日志
  if (process.env.NODE_ENV === 'development') {
    setupDebugLogging(socket)
  }

  return socket
}

// ============================================================================
// 调试日志设置
// ============================================================================

function setupDebugLogging(socket: Socket): void {
  socket.on('connect', () => {
    console.log('✅ Socket.IO connected:', {
      id: socket.id,
      transport: socket.io.engine?.transport?.name,
      connected: socket.connected,
    })
  })

  socket.on('connect_error', error => {
    console.error('❌ Socket.IO connection error:', {
      message: error.message,
      type: (error as any).type,
      description: (error as any).description,
      context: (error as any).context,
      data: (error as any).data,
    })
  })

  socket.on('disconnect', (reason, details) => {
    console.warn('🔌 Socket.IO disconnected:', {
      reason,
      details,
      id: socket.id,
    })
  })

  socket.on('reconnect', attemptNumber => {
    console.log('🔄 Socket.IO reconnected after', attemptNumber, 'attempts')
  })

  socket.on('reconnect_attempt', attemptNumber => {
    console.log('🔄 Socket.IO reconnection attempt:', attemptNumber)
  })

  socket.on('reconnect_error', error => {
    console.error('❌ Socket.IO reconnection error:', error.message)
  })

  socket.on('reconnect_failed', () => {
    console.error('❌ Socket.IO reconnection failed - max attempts reached')
  })
}

// ============================================================================
// 环境配置获取
// ============================================================================

export function getSocketUrl(): string | null {
  // 优先使用环境变量
  if (typeof window !== 'undefined') {
    return process.env.NEXT_PUBLIC_SOCKET_URL || null
  }
  return null
}

export function getDefaultSocketConfig(): SocketConfig | null {
  const url = getSocketUrl()
  if (!url) return null

  return {
    url,
    ...DEFAULT_CONFIG,
  }
}
