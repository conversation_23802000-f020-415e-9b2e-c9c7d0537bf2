/**
 * 简化的连接工厂 - 去除过度工程化设计
 *
 * 功能：基本的连接创建，环境智能检测
 * 依赖：SocketConnection、MockConnection
 * 性能：轻量级工厂模式，无复杂的pool和fallback机制
 *
 * 从326行复杂实现简化为 ~80行，去除：
 * - 复杂的连接池管理
 * - 过度的fallback机制和重试逻辑
 * - 复杂的环境验证和推荐系统
 * - 冗余的便捷函数导出
 */

'use client'

import { SocketConnection } from './socket-connection'
import { MockConnection } from './mock-connection'
import { getSocketUrl, getDefaultSocketConfig } from '../socket'
import { ConnectionType, type IConnectionManager, type ConnectionConfig } from './types'

// ============================================================================
// 简化的环境检测
// ============================================================================

export function detectEnvironment() {
  const socketUrl = getSocketUrl()
  const forceMockMode = process.env.NEXT_PUBLIC_FORCE_MOCK_MODE === 'true'

  // 简单决策：强制Mock模式或没有Socket URL则使用Mock
  const preferredType = forceMockMode || !socketUrl ? ConnectionType.MOCK : ConnectionType.SOCKET

  return {
    socketUrl,
    preferredType,
    forceMockMode,
  }
}

// ============================================================================
// 简化的连接工厂
// ============================================================================

export class ConnectionFactory {
  /**
   * 创建连接管理器（智能选择）
   */
  static create(config?: ConnectionConfig): IConnectionManager {
    const env = detectEnvironment()

    // 检查运行时强制Mock模式
    if (config?.forceMockMode || env.forceMockMode) {
      console.log('🎭 Creating Mock connection')
      return this.createMockConnection(config)
    }

    if (env.preferredType === ConnectionType.SOCKET && env.socketUrl) {
      console.log('🌐 Creating Socket.IO connection')
      return this.createSocketConnection(env.socketUrl, config)
    } else {
      console.log('🎭 Creating Mock connection (fallback)')
      return this.createMockConnection(config)
    }
  }

  /**
   * 创建Socket.IO连接
   */
  static createSocketConnection(url?: string, config?: ConnectionConfig): SocketConnection {
    const socketConfig = url ? { url, ...config } : getDefaultSocketConfig()

    if (!socketConfig) {
      throw new Error('Socket URL not configured')
    }

    return new SocketConnection(socketConfig)
  }

  /**
   * 创建Mock连接
   */
  static createMockConnection(config?: ConnectionConfig): MockConnection {
    return new MockConnection({
      ...config,
      autoReply: true,
      responseDelay: 500,
    })
  }

  /**
   * 根据类型强制创建连接
   */
  static createByType(type: ConnectionType, config?: ConnectionConfig): IConnectionManager {
    switch (type) {
      case ConnectionType.SOCKET:
        const socketUrl = getSocketUrl()
        if (!socketUrl) {
          throw new Error('Socket URL not configured for Socket.IO connection')
        }
        return this.createSocketConnection(socketUrl, config)

      case ConnectionType.MOCK:
        return this.createMockConnection(config)

      default:
        throw new Error(`Unsupported connection type: ${type}`)
    }
  }
}

// ============================================================================
// 简化的便捷导出
// ============================================================================

/**
 * 创建默认连接（推荐方式）
 */
export function createConnection(config?: ConnectionConfig): IConnectionManager {
  return ConnectionFactory.create(config)
}

/**
 * 创建指定类型的连接
 */
export function createConnectionByType(
  type: ConnectionType,
  config?: ConnectionConfig
): IConnectionManager {
  return ConnectionFactory.createByType(type, config)
}

/**
 * 检查是否可以使用Socket.IO连接
 */
export function canUseSocketIO(): boolean {
  const env = detectEnvironment()
  return !!env.socketUrl && !env.forceMockMode
}
