'use client'

/**
 * MessageContainer - 基于BaseWebSocketMessage的标准消息渲染组件
 * 设计原则：严格按照websocket-event-type.ts的payload类型进行条件渲染
 * 性能优化：使用React.memo防止不必要的重渲染
 */

import { useMemo, memo } from 'react'
import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'
import type { BaseWebSocketMessage } from '@/types/websocket-event-type'
import {
  isStreamingPayload,
  isCheckpointPayload,
  isReportPayload,
  isErrorPayload,
} from '@/types/websocket-event-type'

// 导入各个渲染组件
import { TextGenerateEffect } from './text-generate-effect'
import { CheckpointForm } from './checkpoint-form'
import { ReportRenderer } from './report-renderer'
import { ErrorDisplay } from './error-display'

// Shadcn UI 组件
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { User, Bot, Clock, Check, AlertCircle, Send, Loader2 } from 'lucide-react'
import { useSession } from '@/components/providers/session-provider'

export interface MessageContainerProps {
  /** BaseWebSocketMessage标准消息数据 */
  message: BaseWebSocketMessage
  /** 是否显示头像 */
  showAvatar?: boolean
  /** 是否显示时间戳 */
  showTimestamp?: boolean
  /** 是否显示消息状态 */
  showStatus?: boolean
  /** 自定义样式 */
  className?: string
  /** 用户头像URL */
  userAvatarUrl?: string | undefined
  /** 助手头像URL */
  assistantAvatarUrl?: string | undefined
  /** Checkpoint表单提交回调 */
  onCheckpointSubmit?: (formId: string, values: Record<string, any>) => void | Promise<void>
  /** 错误重试回调 */
  onErrorRetry?: (errorId: string) => void | Promise<void>
  /** 错误忽略回调 */
  onErrorDismiss?: (errorId: string) => void
  /** 报告链接点击回调 */
  onReportLinkClick?: (url: string) => void
}

const MessageContainerComponent = ({
  message,
  showAvatar = true,
  showTimestamp = true,
  showStatus = true,
  className,
  userAvatarUrl,
  assistantAvatarUrl,
  onCheckpointSubmit,
  onErrorRetry,
  onErrorDismiss,
  onReportLinkClick,
}: MessageContainerProps) => {
  // 获取当前用户信息用于角色判断
  const { user } = useSession()
  // 🎯 核心：基于BaseWebSocketMessage.payload的标准渲染逻辑
  const messageContentRenderer = useMemo(() => {
    const { payload } = message

    // 基于payload.type进行条件渲染
    if (isErrorPayload(payload)) {
      return (
        <ErrorDisplay
          error={{
            code: payload.code,
            message: payload.message,
            timestamp: new Date(message.timestamp),
            severity: 'error' as any,
          }}
          {...(onErrorRetry && { onRetry: () => onErrorRetry(message.id) })}
          {...(onErrorDismiss && { onDismiss: () => onErrorDismiss(message.id) })}
          showTechnicalDetails={true}
        />
      )
    }

    if (isCheckpointPayload(payload)) {
      return (
        <CheckpointForm
          fields={payload.fields}
          onSubmit={
            onCheckpointSubmit ? values => onCheckpointSubmit(message.id, values) : () => {}
          }
          isSubmitting={false}
          initialValues={{}}
        />
      )
    }

    if (isReportPayload(payload)) {
      return (
        <ReportRenderer
          content={payload.content}
          {...(onReportLinkClick && { onLinkClick: onReportLinkClick })}
        />
      )
    }

    if (isStreamingPayload(payload)) {
      // 🎯 优先从metadata获取累积文本，fallback到delta
      const accumulatedText =
        message.metadata?.streamingState?.accumulatedText || payload.delta || ''
      const isComplete = payload.isComplete

      // 🎯 修复：如果没有文本内容且未完成，不渲染组件
      if (!accumulatedText && !isComplete) {
        return null
      }

      // 🔍 仅在开发环境输出渲染日志
      if (process.env.NODE_ENV === 'development' && accumulatedText) {
        console.log('🎨 MessageContainer rendering streaming:', {
          messageId: message.id,
          timestamp: message.timestamp,
          delta: payload.delta,
          accumulatedText:
            accumulatedText?.slice(0, 50) + (accumulatedText?.length > 50 ? '...' : ''),
          isComplete,
          hasMetadata: !!message.metadata?.streamingState,
        })
      }

      return (
        <TextGenerateEffect
          words={accumulatedText}
          isStreaming={!isComplete}
          showCursor={!isComplete}
          characterDelay={30}
          filter={false}
          className="text-foreground"
        />
      )
    }

    // 🎯 修复：检查是否有系统消息内容
    if (message.error) {
      return <div className="whitespace-pre-wrap break-words text-foreground">{message.error}</div>
    }

    // fallback：显示payload信息
    return (
      <div className="p-3 text-sm text-muted-foreground bg-muted rounded-md">
        <div className="mb-2 text-xs font-mono">未识别的消息类型</div>
        <pre className="text-xs overflow-auto">{JSON.stringify(payload, null, 2)}</pre>
      </div>
    )
  }, [message, onCheckpointSubmit, onErrorRetry, onErrorDismiss, onReportLinkClick])

  // 消息状态图标
  const getStatusIcon = () => {
    const status = message.status || 'delivered'

    switch (status) {
      case 'sending':
        return <Loader2 className="w-3 h-3 animate-spin" />
      case 'sent':
        return <Send className="w-3 h-3" />
      case 'delivered':
        return <Check className="w-3 h-3" />
      case 'failed':
        return <AlertCircle className="w-3 h-3 text-destructive" />
      default:
        return <Check className="w-3 h-3" />
    }
  }

  // 消息发送者信息
  const senderInfo = useMemo(() => {
    // 🎯 修复角色推断逻辑 - 支持用户、助手、系统三种角色
    let messageType: 'user' | 'assistant' | 'system' = 'assistant' // 默认为assistant

    // 优先根据payload类型判断系统消息
    if (isErrorPayload(message.payload)) {
      messageType = 'system'
    }
    // 根据userId判断用户消息
    else if (message.userId && user && message.userId === user.id) {
      messageType = 'user'
    }
    // 其他情况保持为assistant

    switch (messageType) {
      case 'user':
        return {
          name: '我',
          avatar: userAvatarUrl,
          fallback: 'U',
          icon: User,
          bgColor: 'bg-primary',
          textColor: 'text-primary-foreground',
        }
      case 'assistant':
        return {
          name: 'AI助手',
          avatar: assistantAvatarUrl,
          fallback: 'AI',
          icon: Bot,
          bgColor: 'bg-secondary',
          textColor: 'text-secondary-foreground',
        }
      case 'system':
      default:
        return {
          name: '系统',
          avatar: undefined,
          fallback: 'S',
          icon: AlertCircle,
          bgColor: 'bg-muted',
          textColor: 'text-muted-foreground',
        }
    }
  }, [message.payload, message.userId, user, userAvatarUrl, assistantAvatarUrl])

  // 格式化时间戳
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diff = now.getTime() - date.getTime()

    if (diff < 60000) {
      // 1分钟内
      return '刚刚'
    } else if (diff < 3600000) {
      // 1小时内
      return `${Math.floor(diff / 60000)}分钟前`
    } else if (diff < 86400000) {
      // 24小时内
      return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
    } else {
      return date.toLocaleDateString('zh-CN', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      })
    }
  }

  // 🎯 修复消息类型判断 - 支持用户消息
  const messageType: 'user' | 'assistant' | 'system' = (() => {
    if (isErrorPayload(message.payload)) {
      return 'system'
    }
    if (message.userId && user && message.userId === user.id) {
      return 'user'
    }
    return 'assistant'
  })()

  const isUserMessage = messageType === 'user'
  const isSystemMessage = messageType === 'system'

  return (
    <motion.div
      className={cn(
        'flex w-full gap-3 px-4 py-3',
        isUserMessage && 'flex-row-reverse',
        isSystemMessage && 'justify-center',
        className
      )}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* 头像 */}
      {showAvatar && !isSystemMessage && senderInfo && (
        <Avatar className="w-8 h-8 flex-shrink-0">
          {senderInfo.avatar && <AvatarImage src={senderInfo.avatar} />}
          <AvatarFallback className={cn(senderInfo.bgColor, senderInfo.textColor)}>
            {senderInfo.fallback}
          </AvatarFallback>
        </Avatar>
      )}

      {/* 消息内容区域 */}
      <div
        className={cn(
          'flex flex-col flex-1 max-w-[80%]',
          isUserMessage && 'items-end',
          isSystemMessage && 'items-center max-w-md'
        )}
      >
        {/* 消息头部信息 */}
        {(showTimestamp || showStatus) && !isSystemMessage && senderInfo && (
          <div
            className={cn(
              'flex items-center gap-2 mb-1 text-xs text-muted-foreground',
              isUserMessage && 'flex-row-reverse'
            )}
          >
            <span className="font-medium">{senderInfo.name}</span>
            {showTimestamp && (
              <>
                <span>•</span>
                <div className="flex items-center gap-1">
                  <Clock className="w-3 h-3" />
                  <span>{formatTimestamp(message.timestamp)}</span>
                </div>
              </>
            )}
            {showStatus && (
              <>
                <span>•</span>
                <div className="flex items-center gap-1">
                  {getStatusIcon()}
                  <Badge
                    variant={message.status === 'failed' ? 'destructive' : 'secondary'}
                    className="text-xs"
                  >
                    {message.status === 'sending' && '发送中'}
                    {message.status === 'sent' && '已发送'}
                    {message.status === 'delivered' && '已送达'}
                    {message.status === 'failed' && '发送失败'}
                    {!message.status && '已送达'}
                  </Badge>
                </div>
              </>
            )}
          </div>
        )}

        {/* 消息内容 */}
        <div
          className={cn(
            'w-full',
            isSystemMessage ? 'text-center' : isUserMessage ? 'text-right' : 'text-left'
          )}
        >
          {isSystemMessage ? (
            <Badge variant="outline" className="text-xs">
              {message.error || '系统消息'}
            </Badge>
          ) : (
            <Card
              className={cn(
                'border shadow-sm',
                isUserMessage ? 'bg-primary text-primary-foreground border-primary' : 'bg-card'
              )}
            >
              <CardContent className="p-3">{messageContentRenderer}</CardContent>
            </Card>
          )}
        </div>
      </div>
    </motion.div>
  )
}

// 🎯 React.memo优化 - 自定义比较函数确保精准重渲染控制
export const MessageContainer = memo(MessageContainerComponent, (prevProps, nextProps) => {
  // 检查关键属性是否变化
  const messageChanged =
    prevProps.message.id !== nextProps.message.id ||
    prevProps.message.payload !== nextProps.message.payload ||
    prevProps.message.status !== nextProps.message.status ||
    prevProps.message.timestamp !== nextProps.message.timestamp

  // 检查流式消息的累积文本是否变化
  const streamingTextChanged =
    prevProps.message.metadata?.streamingState?.accumulatedText !==
    nextProps.message.metadata?.streamingState?.accumulatedText

  // 检查其他UI相关属性
  const uiPropsChanged =
    prevProps.showAvatar !== nextProps.showAvatar ||
    prevProps.showTimestamp !== nextProps.showTimestamp ||
    prevProps.showStatus !== nextProps.showStatus ||
    prevProps.className !== nextProps.className ||
    prevProps.userAvatarUrl !== nextProps.userAvatarUrl ||
    prevProps.assistantAvatarUrl !== nextProps.assistantAvatarUrl

  // 只有在关键内容变化时才重新渲染
  const shouldRerender = messageChanged || streamingTextChanged || uiPropsChanged

  return !shouldRerender // memo返回true表示不重渲染
})
