'use client'
import { useEffect, useMemo, useRef, useState } from 'react'
import { motion, stagger, useAnimate } from 'framer-motion'
import { cn } from '@/lib/utils'

export interface TextGenerateEffectProps {
  words: string
  className?: string
  filter?: boolean
  duration?: number
  /** 流式模式：是否正在接收新文本 */
  isStreaming?: boolean
  /** 流式模式：显示光标 */
  showCursor?: boolean
  /** 流式模式：每个字符的延迟（毫秒） */
  characterDelay?: number
}

export const TextGenerateEffect = ({
  words,
  className,
  filter = true,
  duration = 0.5,
  isStreaming = false,
  showCursor = false,
  characterDelay = 30,
}: TextGenerateEffectProps) => {
  const [scope, animate] = useAnimate()
  const [displayedLength, setDisplayedLength] = useState(0)
  const previousWordsRef = useRef('')
  const animationTimerRef = useRef<NodeJS.Timeout | null>(null)

  // 🎯 性能优化：使用useMemo缓存分割结果
  const textArray = useMemo(() => {
    return isStreaming ? words.split('') : words.split(' ')
  }, [words, isStreaming])

  // 🎯 流式模式的增量显示逻辑
  useEffect(() => {
    if (!isStreaming) {
      // 标准模式：一次性播放所有动画
      animate(
        'span',
        {
          opacity: 1,
          filter: filter ? 'blur(0px)' : 'none',
        },
        {
          duration: duration,
          delay: stagger(0.2),
        }
      )
      setDisplayedLength(textArray.length)
      return
    }

    // 流式模式：逐步增加显示长度
    const newLength = textArray.length

    if (newLength > displayedLength) {
      // 清除之前的定时器
      if (animationTimerRef.current) {
        clearTimeout(animationTimerRef.current)
      }

      // 🎯 简化策略：延迟更新显示长度，让CSS transition处理动画
      animationTimerRef.current = setTimeout(() => {
        setDisplayedLength(newLength)
      }, characterDelay)
    }
  }, [textArray.length, isStreaming, displayedLength, characterDelay, animate, duration, filter])

  // 🎯 初始化和重置逻辑
  useEffect(() => {
    if (isStreaming) {
      // 如果是新的文本内容（长度变短），重置显示长度
      if (previousWordsRef.current && words.length < previousWordsRef.current.length) {
        setDisplayedLength(0)
      }
      // 初始化第一个字符显示
      if (displayedLength === 0 && textArray.length > 0) {
        setDisplayedLength(1)
      }
    } else {
      // 非流式模式重置
      setDisplayedLength(0)
    }

    previousWordsRef.current = words
  }, [isStreaming, textArray.length, displayedLength, words])

  // 🎯 性能监控（开发环境）
  useEffect(() => {
    if (process.env.NODE_ENV === 'development' && isStreaming) {
      const currentTime = Date.now()
      // eslint-disable-next-line no-console
      console.log('📊 TextGenerateEffect性能:', {
        文本长度: words.length,
        显示长度: displayedLength,
        是否流式: isStreaming,
        时间戳: currentTime,
        渲染频率: '~' + Math.round(1000 / characterDelay) + 'fps',
      })
    }
  }, [displayedLength, words.length, isStreaming, characterDelay])

  // 清理定时器
  useEffect(() => {
    return () => {
      if (animationTimerRef.current) {
        clearTimeout(animationTimerRef.current)
      }
    }
  }, [])

  const renderContent = () => {
    // 🎯 优化：静态文本使用简单渲染，避免复杂DOM结构
    if (!isStreaming) {
      return (
        <motion.div ref={scope} className="whitespace-pre-wrap break-words">
          {words}
        </motion.div>
      )
    }

    // 🎯 流式模式：使用字符级动画
    return (
      <motion.div ref={scope} className="inline-flex flex-wrap">
        {textArray.map((item, idx) => {
          // 🎯 流式模式：已显示的字符立即可见，新字符等待动画
          const isVisible = idx < displayedLength

          return (
            <motion.span
              key={`char-${idx}`}
              data-index={idx}
              className={cn(
                'dark:text-white text-black inline-block transition-opacity',
                isVisible ? 'opacity-100' : 'opacity-0'
              )}
              style={{
                filter: filter && !isVisible ? 'blur(10px)' : 'none',
                opacity: isVisible ? 1 : 0,
              }}
            >
              {item}
            </motion.span>
          )
        })}

        {/* 流式光标 */}
        {showCursor && (
          <motion.span
            className="inline-block w-0.5 h-5 bg-current ml-1"
            animate={{ opacity: [1, 0] }}
            transition={{
              duration: 0.8,
              repeat: Infinity,
              repeatType: 'reverse',
            }}
          />
        )}
      </motion.div>
    )
  }

  return (
    <div className={cn(isStreaming ? 'font-normal' : 'font-bold', className)}>
      <div className={isStreaming ? '' : 'mt-4'}>
        <div
          className={cn(
            'dark:text-white text-black leading-snug tracking-wide',
            isStreaming ? 'text-base whitespace-pre-wrap break-words' : 'text-sm'
          )}
        >
          {renderContent()}
        </div>
      </div>
    </div>
  )
}
