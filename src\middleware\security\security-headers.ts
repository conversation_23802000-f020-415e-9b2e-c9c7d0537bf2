/**
 * 安全头中间件
 * 为响应添加标准的安全头，提升应用安全性
 */

import { NextRequest, NextResponse } from 'next/server'
import {
  MiddlewareFunction,
  EnhancedMiddlewareFunction,
  MiddlewareContext,
  MiddlewareResult,
} from '../utils/types'
import { RouteClassifier } from '../utils/route-matcher'

/**
 * 安全头配置
 */
interface SecurityHeaderConfig {
  /** CSP 内容安全策略 */
  contentSecurityPolicy?: string
  /** X-Frame-Options */
  frameOptions?: 'DENY' | 'SAMEORIGIN' | string
  /** X-Content-Type-Options */
  contentTypeOptions?: boolean
  /** X-XSS-Protection */
  xssProtection?: string
  /** Referrer-Policy */
  referrerPolicy?: string
  /** Permissions-Policy */
  permissionsPolicy?: string
  /** Strict-Transport-Security */
  strictTransportSecurity?: string
  /** X-DNS-Prefetch-Control */
  dnsPrefetchControl?: boolean
}

/**
 * 默认安全头配置
 */
const DEFAULT_SECURITY_CONFIG: SecurityHeaderConfig = {
  frameOptions: 'DENY',
  contentTypeOptions: true,
  xssProtection: '1; mode=block',
  referrerPolicy: 'strict-origin-when-cross-origin',
  permissionsPolicy: 'camera=(), microphone=(), geolocation=(), interest-cohort=()',
  dnsPrefetchControl: false,
}

/**
 * 嵌入页面安全头配置
 * 嵌入页面需要特殊的安全头设置
 */
const EMBED_SECURITY_CONFIG: SecurityHeaderConfig = {
  frameOptions: 'SAMEORIGIN', // 允许在同源框架中显示
  contentTypeOptions: true,
  xssProtection: '1; mode=block',
  referrerPolicy: 'strict-origin-when-cross-origin',
  permissionsPolicy: 'camera=(), microphone=(), geolocation=()',
}

/**
 * 应用安全头到响应
 */
function applySecurityHeaders(response: NextResponse, config: SecurityHeaderConfig): void {
  // X-Frame-Options
  if (config.frameOptions) {
    response.headers.set('X-Frame-Options', config.frameOptions)
  }

  // X-Content-Type-Options
  if (config.contentTypeOptions) {
    response.headers.set('X-Content-Type-Options', 'nosniff')
  }

  // X-XSS-Protection
  if (config.xssProtection) {
    response.headers.set('X-XSS-Protection', config.xssProtection)
  }

  // Referrer-Policy
  if (config.referrerPolicy) {
    response.headers.set('Referrer-Policy', config.referrerPolicy)
  }

  // Permissions-Policy
  if (config.permissionsPolicy) {
    response.headers.set('Permissions-Policy', config.permissionsPolicy)
  }

  // Content-Security-Policy
  if (config.contentSecurityPolicy) {
    response.headers.set('Content-Security-Policy', config.contentSecurityPolicy)
  }

  // Strict-Transport-Security (仅在生产环境的 HTTPS 下)
  if (config.strictTransportSecurity && process.env.NODE_ENV === 'production') {
    response.headers.set('Strict-Transport-Security', config.strictTransportSecurity)
  }

  // X-DNS-Prefetch-Control
  if (config.dnsPrefetchControl !== undefined) {
    response.headers.set('X-DNS-Prefetch-Control', config.dnsPrefetchControl ? 'on' : 'off')
  }
}

/**
 * 标准安全头中间件
 */
export const securityHeadersMiddleware: MiddlewareFunction = async (
  request: NextRequest
): Promise<MiddlewareResult> => {
  const { pathname } = request.nextUrl

  // 跳过静态资源
  if (RouteClassifier.shouldSkipMiddleware(pathname)) {
    return null
  }

  // 创建响应
  const response = NextResponse.next()

  // 根据路由类型选择安全头配置
  let config = DEFAULT_SECURITY_CONFIG

  if (RouteClassifier.isEmbedRoute(pathname)) {
    config = EMBED_SECURITY_CONFIG
  }

  // 应用安全头
  applySecurityHeaders(response, config)

  // 添加自定义安全头
  response.headers.set('X-Powered-By', 'Next.js')
  response.headers.set('X-Middleware-Version', '2.0')

  if (process.env.NODE_ENV === 'development') {
    response.headers.set('X-Debug-Mode', 'true')
  }

  return response
}

/**
 * 增强安全头中间件
 * 支持更灵活的配置和上下文感知
 */
export const enhancedSecurityHeadersMiddleware: EnhancedMiddlewareFunction = async (
  request: NextRequest,
  context: MiddlewareContext
): Promise<MiddlewareResult> => {
  const { pathname } = context

  // 跳过静态资源
  if (RouteClassifier.shouldSkipMiddleware(pathname)) {
    return null
  }

  // 创建响应
  const response = NextResponse.next()

  // 基于上下文选择安全头配置
  let config = { ...DEFAULT_SECURITY_CONFIG }

  // 嵌入页面特殊处理
  if (RouteClassifier.isEmbedRoute(pathname)) {
    config = { ...EMBED_SECURITY_CONFIG }
  }

  // API 路由特殊处理
  if (RouteClassifier.isApiRoute(pathname)) {
    config = {
      ...config,
      frameOptions: 'DENY', // API 不应该在框架中显示
      contentTypeOptions: true,
    }
  }

  // 管理员页面加强安全
  if (RouteClassifier.isAdminOnlyRoute(pathname)) {
    config = {
      ...config,
      frameOptions: 'DENY',
      contentSecurityPolicy:
        "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';",
    }
  }

  // 应用安全头
  applySecurityHeaders(response, config)

  // 添加调试信息（开发环境）
  if (process.env.NODE_ENV === 'development') {
    response.headers.set('X-Route-Type', RouteClassifier.getRouteType(pathname))
    response.headers.set('X-Auth-Status', context.isAuthenticated ? 'authenticated' : 'anonymous')
  }

  return response
}

/**
 * CSP 构建器
 * 帮助构建复杂的内容安全策略
 */
export class CSPBuilder {
  private directives: Record<string, string[]> = {}

  /**
   * 添加指令
   */
  directive(name: string, values: string | string[]): this {
    if (!this.directives[name]) {
      this.directives[name] = []
    }

    const valueArray = Array.isArray(values) ? values : [values]
    this.directives[name].push(...valueArray)

    return this
  }

  /**
   * 添加 default-src
   */
  defaultSrc(values: string | string[]): this {
    return this.directive('default-src', values)
  }

  /**
   * 添加 script-src
   */
  scriptSrc(values: string | string[]): this {
    return this.directive('script-src', values)
  }

  /**
   * 添加 style-src
   */
  styleSrc(values: string | string[]): this {
    return this.directive('style-src', values)
  }

  /**
   * 添加 img-src
   */
  imgSrc(values: string | string[]): this {
    return this.directive('img-src', values)
  }

  /**
   * 构建 CSP 字符串
   */
  build(): string {
    return Object.entries(this.directives)
      .map(([directive, values]) => `${directive} ${values.join(' ')}`)
      .join('; ')
  }
}

/**
 * 预定义的 CSP 配置
 */
export const CSP_PRESETS = {
  /**
   * 严格的 CSP 配置
   */
  strict: new CSPBuilder()
    .defaultSrc(["'self'"])
    .scriptSrc(["'self'"])
    .styleSrc(["'self'", "'unsafe-inline'"])
    .imgSrc(["'self'", 'data:', 'https:'])
    .build(),

  /**
   * 开发环境 CSP 配置
   */
  development: new CSPBuilder()
    .defaultSrc(["'self'"])
    .scriptSrc(["'self'", "'unsafe-eval'", "'unsafe-inline'"])
    .styleSrc(["'self'", "'unsafe-inline'"])
    .imgSrc(["'self'", 'data:', 'https:', 'http:'])
    .build(),

  /**
   * 嵌入页面 CSP 配置
   */
  embed: new CSPBuilder()
    .defaultSrc(["'self'"])
    .scriptSrc(["'self'", "'unsafe-inline'"])
    .styleSrc(["'self'", "'unsafe-inline'"])
    .imgSrc(["'self'", 'data:', 'https:'])
    .directive('frame-ancestors', ['*'])
    .build(),
}

/**
 * 创建自定义安全头中间件
 */
export function createSecurityMiddleware(
  customConfig?: Partial<SecurityHeaderConfig>
): MiddlewareFunction {
  const config = { ...DEFAULT_SECURITY_CONFIG, ...customConfig }

  return async (request: NextRequest): Promise<MiddlewareResult> => {
    const { pathname } = request.nextUrl

    if (RouteClassifier.shouldSkipMiddleware(pathname)) {
      return null
    }

    const response = NextResponse.next()
    applySecurityHeaders(response, config)

    return response
  }
}

/**
 * CORS 安全头中间件
 * 处理跨域请求的安全头
 */
export const corsSecurityMiddleware: MiddlewareFunction = async (
  request: NextRequest
): Promise<MiddlewareResult> => {
  const { pathname } = request.nextUrl

  // 只处理 API 路由
  if (!RouteClassifier.isApiRoute(pathname)) {
    return null
  }

  const response = NextResponse.next()

  // 设置 CORS 头
  const origin = request.headers.get('origin')
  const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || ['*']

  if (allowedOrigins.includes('*') || (origin && allowedOrigins.includes(origin))) {
    response.headers.set('Access-Control-Allow-Origin', origin || '*')
    response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
    response.headers.set(
      'Access-Control-Allow-Headers',
      'Authorization, Content-Type, X-Requested-With'
    )
    response.headers.set('Access-Control-Max-Age', '86400')
  }

  return response
}

/**
 * 性能安全头中间件
 * 添加性能相关的安全头
 */
export const performanceSecurityMiddleware: MiddlewareFunction = async (
  request: NextRequest
): Promise<MiddlewareResult> => {
  const response = NextResponse.next()

  // 添加性能相关的安全头
  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('X-Download-Options', 'noopen')
  response.headers.set('X-Permitted-Cross-Domain-Policies', 'none')

  return response
}

/**
 * 导出默认的安全头中间件配置
 */
export const defaultSecurityMiddleware = enhancedSecurityHeadersMiddleware
