{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Next.js: debug server-side",
      "type": "node",
      "request": "launch",
      "runtimeExecutable": "pnpm",
      "runtimeArgs": ["run", "dev:inspect"],
      "console": "integratedTerminal",
      "serverReadyAction": {
        "pattern": "ready - started server on .+, url: (https?://.+)",
        "uriFormat": "%s",
        "action": "debugWithChrome"
      },
      "env": {
        "NEXT_TELEMETRY_DISABLED": "1",
        "NEXT_PUBLIC_APP_ENV": "development"
      },
      "skipFiles": [
        "<node_internals>/**",
        "${workspaceFolder}/node_modules/**"
      ],
      "resolveSourceMapLocations": [
        "${workspaceFolder}/**",
        "!**/node_modules/**"
      ]
    },
    {
      "name": "Next.js: debug client-side",
      "type": "chrome",
      "request": "launch",
      "url": "http://localhost:3000",
      "webRoot": "${workspaceFolder}",
      "sourceMapPathOverrides": {
        "webpack://_N_E/*": "${workspaceFolder}/*",
        "webpack://./*": "${workspaceFolder}/src/*",
        "webpack://./pages/*": "${workspaceFolder}/src/app/*",
        "webpack://./components/*": "${workspaceFolder}/src/components/*",
        "webpack://./lib/*": "${workspaceFolder}/src/lib/*",
        "webpack://./hooks/*": "${workspaceFolder}/src/hooks/*"
      },
      "skipFiles": [
        "<node_internals>/**",
        "${workspaceFolder}/node_modules/**",
        "${workspaceFolder}/.next/**"
      ]
    },
    {
      "name": "Next.js: debug with turbo",
      "type": "node",
      "request": "launch",
      "runtimeExecutable": "pnpm",
      "runtimeArgs": ["run", "dev:inspect:turbo"],
      "console": "integratedTerminal",
      "serverReadyAction": {
        "pattern": "ready - started server on .+, url: (https?://.+)",
        "uriFormat": "%s",
        "action": "debugWithChrome"
      },
      "env": {
        "NEXT_TELEMETRY_DISABLED": "1",
        "NEXT_PUBLIC_APP_ENV": "development"
      },
      "skipFiles": [
        "<node_internals>/**",
        "${workspaceFolder}/node_modules/**"
      ],
      "resolveSourceMapLocations": [
        "${workspaceFolder}/**",
        "!**/node_modules/**"
      ]
    },
    {
      "name": "Next.js: debug API routes",
      "type": "node",
      "request": "launch",
      "runtimeExecutable": "pnpm",
      "runtimeArgs": ["run", "dev:api-debug"],
      "console": "integratedTerminal",
      "env": {
        "NEXT_TELEMETRY_DISABLED": "1",
        "NEXT_PUBLIC_APP_ENV": "development"
      },
      "skipFiles": [
        "<node_internals>/**",
        "${workspaceFolder}/node_modules/**"
      ],
      "resolveSourceMapLocations": [
        "${workspaceFolder}/**",
        "!**/node_modules/**"
      ]
    },
    {
      "name": "Next.js: debug middleware",
      "type": "node",
      "request": "launch",
      "runtimeExecutable": "pnpm",
      "runtimeArgs": ["run", "dev:middleware-debug"],
      "console": "integratedTerminal",
      "env": {
        "NEXT_TELEMETRY_DISABLED": "1",
        "NEXT_PUBLIC_APP_ENV": "development"
      },
      "skipFiles": [
        "<node_internals>/**",
        "${workspaceFolder}/node_modules/**"
      ],
      "resolveSourceMapLocations": [
        "${workspaceFolder}/**",
        "!**/node_modules/**"
      ]
    },
    {
      "name": "Next.js: debug production build",
      "type": "node", 
      "request": "launch",
      "runtimeExecutable": "pnpm",
      "runtimeArgs": ["run", "start"],
      "console": "integratedTerminal",
      "env": {
        "NODE_ENV": "production",
        "NEXT_TELEMETRY_DISABLED": "1"
      },
      "skipFiles": [
        "<node_internals>/**",
        "${workspaceFolder}/node_modules/**"
      ]
    },
    {
      "name": "Next.js: attach to running process",
      "type": "node",
      "request": "attach",
      "port": 9229,
      "skipFiles": [
        "<node_internals>/**",
        "${workspaceFolder}/node_modules/**"
      ],
      "resolveSourceMapLocations": [
        "${workspaceFolder}/**",
        "!**/node_modules/**"
      ]
    }
  ],
  "compounds": [
    {
      "name": "Next.js: debug full stack (client + server)",
      "configurations": [
        "Next.js: debug server-side",
        "Next.js: debug client-side"
      ]
    }
  ]
}
