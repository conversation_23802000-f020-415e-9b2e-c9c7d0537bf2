/**
 * 中间件系统统一导出
 * 提供完整的类型定义和模块导出
 */

// 核心类型导出
export type {
  MiddlewareResult,
  MiddlewareFunction,
  EnhancedMiddlewareFunction,
  MiddlewareContext,
  MiddlewareOptions,
  RouteMatcher,
  RedirectRule,
} from './utils/types'

// 中间件组合器导出
export {
  composeMiddleware,
  composeEnhancedMiddleware,
  conditionalMiddleware,
  pathMatchMiddleware,
  parallelMiddleware,
} from './utils/middleware-composer'

// 路由匹配工具导出
export {
  RouteClassifier,
  RouteRedirectDecider,
  ROUTE_CONFIG,
  REDIRECT_TARGETS,
  routeUtils,
} from './utils/route-matcher'

// 认证中间件导出
export {
  authMiddleware,
  fastAuthMiddleware,
  adminAuthMiddleware,
  createAuthMiddleware,
  authChecker,
} from './auth/auth-middleware'

export {
  getUserSession,
  buildAuthContext,
  isAdminUser,
  hasCompanyProfile,
  getUserRole,
  getOrganizationRole,
} from './auth/auth-utils'

export type { UserSession, AuthCheckResult } from './auth/auth-utils'

// 业务路由中间件导出
export {
  businessRouteMiddleware,
  chatRouteOptimizer,
  conditionalBusinessMiddleware,
  lightweightBusinessMiddleware,
  createBusinessMiddleware,
  createBusinessMiddlewareChain,
  defaultBusinessMiddleware,
  businessPerformanceMonitor,
} from './business/route-handler'

export {
  getRedirectDecision,
  validateRedirectDecision,
  logRedirectDecision,
  getRootPathRedirect,
  getProtectedRouteRedirect,
  getGuestOnlyRouteRedirect,
  getAdminRouteRedirect,
} from './business/redirect-rules'

export type { RedirectDecision } from './business/redirect-rules'

// 安全中间件导出
export {
  securityHeadersMiddleware,
  enhancedSecurityHeadersMiddleware,
  createSecurityMiddleware,
  corsSecurityMiddleware,
  performanceSecurityMiddleware,
  defaultSecurityMiddleware,
  CSPBuilder,
  CSP_PRESETS,
} from './security/security-headers'

// 工具函数导出
export { middlewareUtils } from './utils/legacy-utils'

/**
 * 中间件版本信息
 */
export const MIDDLEWARE_VERSION = {
  version: '2.0.0',
  architecture: 'modular',
  features: [
    'authentication-middleware',
    'business-route-middleware',
    'security-headers-middleware',
    'middleware-composition',
    'route-classification',
    'redirect-optimization',
    'performance-monitoring',
    'error-handling',
  ],
} as const

/**
 * 中间件配置验证
 */
export function validateMiddlewareConfig(config: any): boolean {
  try {
    // 验证必要的配置项
    if (!config.matcher || !Array.isArray(config.matcher)) {
      console.error('[Middleware] Invalid matcher configuration')
      return false
    }

    return true
  } catch (error) {
    console.error('[Middleware] Configuration validation failed:', error)
    return false
  }
}

/**
 * 中间件健康检查
 */
export function middlewareHealthCheck(): {
  status: 'healthy' | 'degraded' | 'unhealthy'
  components: Record<string, boolean>
  timestamp: number
} {
  const components = {
    'middleware-composer': true,
    'route-classifier': true,
    'auth-middleware': true,
    'business-middleware': true,
    'security-middleware': true,
  }

  const allHealthy = Object.values(components).every(Boolean)

  return {
    status: allHealthy ? 'healthy' : 'degraded',
    components,
    timestamp: Date.now(),
  }
}

/**
 * 预设中间件配置
 */
export const MIDDLEWARE_PRESETS = {
  /**
   * 开发环境配置
   */
  development: {
    enableDebugLogs: true,
    enableTiming: true,
    errorHandling: 'continue' as const,
  },

  /**
   * 生产环境配置
   */
  production: {
    enableDebugLogs: false,
    enableTiming: false,
    errorHandling: 'continue' as const,
  },

  /**
   * 测试环境配置
   */
  testing: {
    enableDebugLogs: false,
    enableTiming: true,
    errorHandling: 'stop' as const,
  },
} as const
