'use client'

import * as React from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import { useSidebar } from '@/components/ui/sidebar'
import { useChatGroupStore, ChatGroupPriority, useSortedGroups } from '@/stores/chat-group-store'
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuSeparator,
  ContextMenuTrigger,
} from '@/components/ui/context-menu'
import { 
  Pin, 
  Plus,
  ArrowUp, 
  ArrowUpToLine,
  Trash2
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import SearchDialog from '@/components/common/search-dialog'

interface ChatItemProps {
  id: string
  title: string
  time: string
  description: string
  avatarBorderColor: string // e.g., "border-red-500", "border-blue-500", "border-orange-500"
  hasNotification: boolean
  isSelected: boolean
  onClick: (id: string) => void
  isCollapsed?: boolean
  priority: ChatGroupPriority
  onSetPriority?: (id: string, priority: ChatGroupPriority) => void
  onDisband?: (id: string) => void
}

function ChatItem({
  id,
  title,
  time,
  description,
  avatarBorderColor,
  hasNotification,
  isSelected,
  onClick,
  isCollapsed = false,
  priority,
  onSetPriority,
  onDisband,
}: ChatItemProps) {
  // 右键菜单事件处理
  const handleSetPriority = (newPriority: ChatGroupPriority) => {
    if (onSetPriority) {
      onSetPriority(id, newPriority)
    }
  }

  const handleDisband = () => {
    if (window.confirm('确定要解散此群聊吗？此操作不可恢复。')) {
      onDisband?.(id)
    }
  }

  if (isCollapsed) {
    // 收起状态：只显示头像
    return (
      <ContextMenu>
        <ContextMenuTrigger>
          <div
            className={cn(
              'relative flex items-center justify-center p-2 cursor-pointer transition-colors',
              'rounded-xl', // 圆角
              'hover:bg-gray-50', // Hover effect
              isSelected ? 'bg-white' : ''
            )}
            onClick={() => onClick(id)}
            title={title} // 添加悬停提示
          >
            {/* Avatar with border and notification dot */}
            <div
              className={cn(
                'relative flex-shrink-0 size-8 rounded-full bg-gray-100 flex items-center justify-center',
                avatarBorderColor // Dynamic border color
              )}
            >
              {/* 显示首字母 */}
              <span className="text-xs font-medium text-gray-600">{title.charAt(0).toUpperCase()}</span>
              {hasNotification && (
                <span className="absolute -top-1 -right-1 size-3 bg-red-500 rounded-full border border-white" />
              )}
            </div>
          </div>
        </ContextMenuTrigger>
        <ContextMenuContent className="w-48">
          <ContextMenuItem onClick={() => handleSetPriority('pinned')} disabled={priority === 'pinned'}>
            <Pin className="w-4 h-4 mr-2" />
            置顶
          </ContextMenuItem>
          <ContextMenuItem onClick={() => handleSetPriority('highest')} disabled={priority === 'highest'}>
            <ArrowUpToLine className="w-4 h-4 mr-2" />
            最高优先级
          </ContextMenuItem>
          <ContextMenuItem onClick={() => handleSetPriority('high')} disabled={priority === 'high'}>
            <ArrowUp className="w-4 h-4 mr-2" />
            较高优先级
          </ContextMenuItem>
          <ContextMenuSeparator />
          <ContextMenuItem onClick={handleDisband} className="text-red-600">
            <Trash2 className="w-4 h-4 mr-2" />
            解散群聊
          </ContextMenuItem>
        </ContextMenuContent>
      </ContextMenu>
    )
  }

  return (
    <ContextMenu>
      <ContextMenuTrigger>
        <div
          className={cn(
            'relative flex items-center gap-3 p-4 cursor-pointer transition-colors',
            'rounded-2xl', // 更大的圆角
            'hover:bg-gray-50', // Hover effect
            isSelected ? 'bg-white' : ''
          )}
          onClick={() => onClick(id)}
        >
          {/* Avatar with border and notification dot */}
          <div
            className={cn(
              'relative flex-shrink-0 size-10 rounded-full bg-gray-100 flex items-center justify-center',
              avatarBorderColor // Dynamic border color
            )}
          >
            {/* 显示首字母 */}
            <span className="text-sm font-medium text-gray-600">{title.charAt(0).toUpperCase()}</span>
            {hasNotification && (
              <span className="absolute top-0 right-0 size-2 bg-red-500 rounded-full border border-white" />
            )}
          </div>

          {/* Text content */}
          <div className="flex-1 overflow-hidden">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-semibold text-gray-800 truncate">{title}</h3>
              <span className="text-xs text-gray-500 ml-2 flex-shrink-0">{time}</span>
            </div>
            <p className="text-sm text-gray-600 truncate">{description}</p>
          </div>
        </div>
      </ContextMenuTrigger>
      <ContextMenuContent className="w-48">
        <ContextMenuItem onClick={() => handleSetPriority('pinned')} disabled={priority === 'pinned'}>
          <Pin className="w-4 h-4 mr-2" />
          置顶
        </ContextMenuItem>
        <ContextMenuItem onClick={() => handleSetPriority('highest')} disabled={priority === 'highest'}>
          <ArrowUpToLine className="w-4 h-4 mr-2" />
          最高优先级
        </ContextMenuItem>
        <ContextMenuItem onClick={() => handleSetPriority('high')} disabled={priority === 'high'}>
          <ArrowUp className="w-4 h-4 mr-2" />
          较高优先级
        </ContextMenuItem>
        <ContextMenuSeparator />
        <ContextMenuItem onClick={handleDisband} className="text-red-600">
          <Trash2 className="w-4 h-4 mr-2" />
          解散群聊
        </ContextMenuItem>
      </ContextMenuContent>
    </ContextMenu>
  )
}

interface ChatGroupProps {
  chats: Array<{
    id: string
    title: string
    time: string
    description: string
    avatarBorderColor: string
    hasNotification: boolean
    priority?: ChatGroupPriority
  }>
  initialSelectedId?: string
}

export default function ChatGroup({ chats, initialSelectedId }: ChatGroupProps) {
  const router = useRouter()
  const pathname = usePathname()
  const { state } = useSidebar()
  const [isDialogOpen, setIsDialogOpen] = React.useState(false)

  // 使用状态管理
  const { groups, selectedGroupId, setGroups, selectGroup, markAsRead, setPriority, removeGroup } = useChatGroupStore()
  const sortedGroups = useSortedGroups()

  // 初始化群聊数据
  React.useEffect(() => {
    if (chats.length > 0) {
      // 转换数据格式并设置到 store
      const groupsData = chats.map(chat => ({
        ...chat,
        unreadCount: chat.hasNotification ? 1 : 0,
        priority: chat.priority || 'normal' as ChatGroupPriority,
        memberCount: Math.floor(Math.random() * 10) + 2, // 模拟成员数量
        createdAt: new Date(Date.now() - Math.random() * 86400000 * 7), // 最近7天内创建
        updatedAt: new Date(Date.now() - Math.random() * 3600000 * 24), // 最近24小时内更新
      }))
      setGroups(groupsData)
    }
  }, [chats, setGroups])

  // 从路径同步当前选中的群聊
  React.useEffect(() => {
    const currentGroupId = pathname.split('/chat/')[1]
    if (currentGroupId && currentGroupId !== selectedGroupId) {
      selectGroup(currentGroupId)
    } else if (initialSelectedId && !selectedGroupId) {
      selectGroup(initialSelectedId)
    }
  }, [pathname, selectedGroupId, initialSelectedId, selectGroup])

  // 检查侧边栏是否收起
  const isCollapsed = state === 'collapsed'

  // 使用排序后的数据
  const displayGroups = sortedGroups.length > 0 ? sortedGroups : chats
  const currentGroupId =
    selectedGroupId || pathname.split('/chat/')[1] || initialSelectedId || chats[0]?.id

  const handleItemClick = (id: string) => {
    // 更新状态管理中的选中状态
    selectGroup(id)

    // 标记为已读
    markAsRead(id)

    // 跳转到对应的群聊页面
    router.push(`/chat/${id}`)
  }

  // 右键菜单事件处理函数
  const handleSetPriority = (id: string, priority: ChatGroupPriority) => {
    setPriority(id, priority)
  }

  const handleDisband = (id: string) => {
    removeGroup(id)
    // 如果删除的是当前选中的群组，跳转到首页
    if (id === selectedGroupId) {
      router.push('/chat')
    }
  }

  return (
    <div
      className={cn(
        'flex flex-col gap-2 w-full px-4',
        isCollapsed ? 'items-center p-2' : ''
      )}
      style={{background: '#F1F5F9'}}
    >
      {displayGroups.map(chat => (
        <ChatItem
          key={chat.id}
          {...chat}
          isSelected={chat.id === currentGroupId}
          onClick={handleItemClick}
          isCollapsed={isCollapsed}
          priority={chat.priority || 'normal'}
          onSetPriority={handleSetPriority}
          onDisband={handleDisband}
        />
      ))}
      
      {/* New Chat Button */}
      {isCollapsed ? (
        // 收起状态：只显示加号图标
        <div 
          className="w-8 h-8 rounded-lg bg-white flex items-center justify-center cursor-pointer hover:bg-gray-100 text-[#8e8e93] border border-gray-200"
          onClick={() => setIsDialogOpen(true)}
          title="新建聊天"
        >
          <Plus className="w-4 h-4" />
        </div>
      ) : (
        // 展开状态：显示完整按钮
        <Button 
          variant="ghost" 
          className="w-full justify-start text-gray-600 hover:bg-gray-100 h-12 px-3"
          onClick={() => setIsDialogOpen(true)}
        >
          <div className="w-8 h-8 rounded-lg bg-gray-200 flex items-center justify-center mr-3">
            <Plus className="h-4 w-4" />
          </div>
          <span>新建聊天</span>
        </Button>
      )}

      {/* Dialog Box */}
      <SearchDialog 
        isOpen={isDialogOpen} 
        onClose={() => setIsDialogOpen(false)} 
      />
    </div>
  )
}

export const defaultProps = {
  chats: [
    {
      id: '1',
      title: '法国2025轮椅投标项目',
      time: '1h ago',
      description: "I've completed the contract review...",
      avatarBorderColor: 'border-red-500',
      hasNotification: true,
      priority: 'pinned' as ChatGroupPriority,
    },
    {
      id: '2',
      title: '水果出口日本项目',
      time: '1h ago',
      description: "I've completed the contract review...",
      avatarBorderColor: 'border-blue-500',
      hasNotification: true,
      priority: 'highest' as ChatGroupPriority,
    },
    {
      id: '3',
      title: '果汁产品拓客项目',
      time: '1h ago',
      description: "I've completed the contract review...",
      avatarBorderColor: 'border-red-500',
      hasNotification: true,
      priority: 'high' as ChatGroupPriority,
    },
    {
      id: '4',
      title: '法国2025轮椅投标项目',
      time: '1h ago',
      description: "I've completed the contract review...",
      avatarBorderColor: 'border-orange-500',
      hasNotification: true,
      priority: 'normal' as ChatGroupPriority,
    },
    {
      id: '5',
      title: '水果出口日本项目',
      time: '1h ago',
      description: "I've completed the contract review...",
      avatarBorderColor: 'border-blue-500',
      hasNotification: true,
      priority: 'normal' as ChatGroupPriority,
    },
    {
      id: '6',
      title: '果汁产品拓客项目',
      time: '1h ago',
      description: "I've completed the contract review...",
      avatarBorderColor: 'border-red-500',
      hasNotification: true,
      priority: 'normal' as ChatGroupPriority,
    },
  ],
  initialSelectedId: '2',
}
