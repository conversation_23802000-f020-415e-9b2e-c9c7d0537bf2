/**
 * 路由匹配工具
 * 提供高效的路由匹配和分类功能
 */

import { NextRequest, NextResponse } from 'next/server'
import { RouteMatcher } from './types'

/**
 * 路由配置定义
 * 基于原有的 ROUTE_CONFIG 优化设计
 */
export const ROUTE_CONFIG = {
  // 公开页面 - 所有人都可以访问
  public: ['/health', '/api/public', '/api/status'],

  // 仅访客页面 - 只有未登录用户可以访问
  guestOnly: ['/login', '/register'],

  // 受保护页面 - 需要登录才能访问
  protected: ['/chat', '/poll'],

  // 管理员专用页面
  adminOnly: ['/admin'],

  // 嵌入页面 - 特殊处理
  embed: ['/embed'],

  // API 路由
  api: ['/api'],
} as const

/**
 * 静态资源模式
 * 这些请求应该跳过中间件处理
 */
export const STATIC_PATTERNS = [
  // Next.js 内部路由
  '/_next/static',
  '/_next/image',
  '/__nextjs',
  '/_vercel',

  // 静态文件
  '/favicon.ico',
  '/robots.txt',
  '/sitemap.xml',
  '/manifest.json',
  '/sw.js',

  // 监控路由
  '/monitoring',
] as const

/**
 * 静态资源文件扩展名正则
 */
export const STATIC_FILE_REGEX =
  /\.(jpg|jpeg|png|gif|svg|ico|webp|avif|mp4|webm|ogg|mp3|wav|flac|aac|woff|woff2|eot|ttf|otf|css|js|json)$/i

/**
 * 创建路径匹配器
 */
export function createPathMatcher(patterns: readonly string[]): RouteMatcher {
  return (pathname: string) => {
    return patterns.some(pattern => pathname.startsWith(pattern))
  }
}

/**
 * 创建精确路径匹配器
 */
export function createExactPathMatcher(paths: readonly string[]): RouteMatcher {
  const pathSet = new Set(paths)
  return (pathname: string) => pathSet.has(pathname)
}

/**
 * 创建正则表达式匹配器
 */
export function createRegexMatcher(regex: RegExp): RouteMatcher {
  return (pathname: string) => regex.test(pathname)
}

/**
 * 路由分类器
 * 将路径分类到不同的路由类型
 */
export class RouteClassifier {
  private static readonly publicMatcher = createPathMatcher(ROUTE_CONFIG.public)
  private static readonly guestOnlyMatcher = createExactPathMatcher(ROUTE_CONFIG.guestOnly)
  private static readonly protectedMatcher = createPathMatcher(ROUTE_CONFIG.protected)
  private static readonly adminOnlyMatcher = createPathMatcher(ROUTE_CONFIG.adminOnly)
  private static readonly embedMatcher = createPathMatcher(ROUTE_CONFIG.embed)
  private static readonly apiMatcher = createPathMatcher(ROUTE_CONFIG.api)

  /**
   * 检查是否应该跳过中间件处理
   */
  static shouldSkipMiddleware(pathname: string): boolean {
    // 静态资源文件
    if (STATIC_FILE_REGEX.test(pathname)) {
      return true
    }

    // 静态资源路径
    return STATIC_PATTERNS.some(pattern => pathname.startsWith(pattern))
  }

  /**
   * 检查是否为公开路由
   */
  static isPublicRoute(pathname: string): boolean {
    return this.publicMatcher(pathname)
  }

  /**
   * 检查是否为仅访客路由
   */
  static isGuestOnlyRoute(pathname: string): boolean {
    return this.guestOnlyMatcher(pathname)
  }

  /**
   * 检查是否为受保护路由
   */
  static isProtectedRoute(pathname: string): boolean {
    return this.protectedMatcher(pathname)
  }

  /**
   * 检查是否为管理员专用路由
   */
  static isAdminOnlyRoute(pathname: string): boolean {
    return this.adminOnlyMatcher(pathname)
  }

  /**
   * 检查是否为嵌入页面路由
   */
  static isEmbedRoute(pathname: string): boolean {
    return this.embedMatcher(pathname)
  }

  /**
   * 检查是否为 API 路由
   */
  static isApiRoute(pathname: string): boolean {
    return this.apiMatcher(pathname)
  }

  /**
   * 检查是否为根路径
   */
  static isRootPath(pathname: string): boolean {
    return pathname === '/'
  }

  /**
   * 获取路由类型
   */
  static getRouteType(pathname: string): string {
    if (this.isRootPath(pathname)) return 'root'
    if (this.isPublicRoute(pathname)) return 'public'
    if (this.isGuestOnlyRoute(pathname)) return 'guest-only'
    if (this.isProtectedRoute(pathname)) return 'protected'
    if (this.isAdminOnlyRoute(pathname)) return 'admin-only'
    if (this.isEmbedRoute(pathname)) return 'embed'
    if (this.isApiRoute(pathname)) return 'api'
    return 'unknown'
  }
}

/**
 * 重定向目标配置
 * 统一管理所有重定向逻辑
 */
export const REDIRECT_TARGETS = {
  // 认证相关重定向
  LOGIN: '/login',

  // 业务流程重定向
  CHAT: '/chat',
  POLL: '/poll',

  // 根路径重定向逻辑
  ROOT_REDIRECT: {
    // 未认证用户 -> 登录页
    UNAUTHENTICATED: '/login',
    // 已认证但需要完善资料 -> 问卷页
    NEEDS_PROFILE: '/poll',
    // 已认证且资料完整 -> 聊天页
    AUTHENTICATED: '/chat',
  },
} as const

/**
 * 智能路由重定向决策器
 */
export class RouteRedirectDecider {
  /**
   * 决定根路径的重定向目标
   */
  static decideRootRedirect(context: {
    isAuthenticated: boolean
    hasCompanyProfile?: boolean
    isAdmin?: boolean
  }): string {
    const { isAuthenticated, hasCompanyProfile, isAdmin } = context

    // 未认证用户 -> 登录页
    if (!isAuthenticated) {
      return REDIRECT_TARGETS.ROOT_REDIRECT.UNAUTHENTICATED
    }

    // 管理员直接进入聊天页
    if (isAdmin) {
      return REDIRECT_TARGETS.ROOT_REDIRECT.AUTHENTICATED
    }

    // 已认证用户根据资料完整性决定
    if (hasCompanyProfile === false) {
      return REDIRECT_TARGETS.ROOT_REDIRECT.NEEDS_PROFILE
    }

    // 默认进入聊天页
    return REDIRECT_TARGETS.ROOT_REDIRECT.AUTHENTICATED
  }

  /**
   * 检查是否需要重定向到问卷页
   */
  static needsPollRedirect(context: {
    isAuthenticated: boolean
    hasCompanyProfile?: boolean
    isAdmin?: boolean
    pathname: string
  }): boolean {
    const { isAuthenticated, hasCompanyProfile, isAdmin, pathname } = context

    // 只有已认证的非管理员用户需要检查
    if (!isAuthenticated || isAdmin) {
      return false
    }

    // 如果已经在问卷页或登录相关页面，不需要重定向
    if (pathname.startsWith('/poll') || RouteClassifier.isGuestOnlyRoute(pathname)) {
      return false
    }

    // 如果访问受保护页面但没有完善资料，需要重定向到问卷页
    return RouteClassifier.isProtectedRoute(pathname) && hasCompanyProfile === false
  }
}

/**
 * 路由工具函数
 */
export const routeUtils = {
  /**
   * 从请求中提取路径信息
   */
  extractPathInfo(request: NextRequest) {
    const { pathname, searchParams } = request.nextUrl
    return {
      pathname,
      search: searchParams.toString(),
      fullPath: pathname + (searchParams.toString() ? `?${searchParams}` : ''),
    }
  },

  /**
   * 创建重定向响应
   */
  createRedirect(request: NextRequest, target: string) {
    const url = new URL(target, request.url)
    return NextResponse.redirect(url, 302)
  },

  /**
   * 记录路由决策日志
   */
  logRouteDecision(decision: {
    pathname: string
    routeType: string
    action: 'allow' | 'redirect' | 'block'
    target?: string
    reason?: string
  }) {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[Route Decision]`, {
        path: decision.pathname,
        type: decision.routeType,
        action: decision.action,
        target: decision.target,
        reason: decision.reason,
      })
    }
  },
}
