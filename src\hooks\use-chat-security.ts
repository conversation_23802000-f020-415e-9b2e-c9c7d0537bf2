'use client'

import { useCallback, useMemo } from 'react'
import { useSession } from '@/components/providers/session-provider'

// 🔒 安全验证结果类型
export interface SecurityValidationResult {
  isValid: boolean
  reason: 'valid' | 'no-user' | 'no-organization' | 'invalid-group-id'
  errorCode?: string
}

// 🔒 群聊 ID 验证结果
export interface GroupIdValidationResult {
  isValid: boolean
  errorType?: 'invalid-format' | 'invalid-length' | 'path-traversal' | 'reserved-word' | 'invalid-pattern'
  details?: string
}

/**
 * 聊天系统安全验证 Hook
 * 提供用户权限验证、群聊ID验证等安全功能
 */
export function useChatSecurity() {
  const { user } = useSession()

  // 🔒 严格的群聊 ID 验证函数
  const validateGroupId = useCallback((id: string): GroupIdValidationResult => {
    // 基本格式检查：只允许字母数字、连字符、下划线
    if (!/^[a-zA-Z0-9-_]+$/.test(id)) {
      return {
        isValid: false,
        errorType: 'invalid-format',
        details: 'Group ID contains invalid characters'
      }
    }

    // 长度限制：防止过长的ID导致DoS
    if (id.length < 1 || id.length > 64) {
      return {
        isValid: false,
        errorType: 'invalid-length',
        details: `Group ID length (${id.length}) is out of allowed range (1-64)`
      }
    }

    // 防止路径遍历攻击
    if (id.includes('..') || id.includes('./') || id.includes('../')) {
      return {
        isValid: false,
        errorType: 'path-traversal',
        details: 'Group ID contains path traversal characters'
      }
    }

    // 防止保留字符串和系统路径
    const reservedWords = ['admin', 'api', 'system', 'root', 'null', 'undefined', 'console', 'eval']
    if (reservedWords.includes(id.toLowerCase())) {
      return {
        isValid: false,
        errorType: 'reserved-word',
        details: 'Group ID uses reserved keyword'
      }
    }

    // 防止纯数字或纯特殊字符的ID
    if (/^[-_]+$/.test(id) || /^\d+$/.test(id)) {
      return {
        isValid: false,
        errorType: 'invalid-pattern',
        details: 'Group ID format is not allowed'
      }
    }

    return { isValid: true }
  }, [])

  // 🔒 用户权限验证状态（缓存）
  const userValidation = useMemo((): SecurityValidationResult => {
    if (!user) {
      return { isValid: false, reason: 'no-user', errorCode: 'USER_NOT_FOUND' }
    }
    
    if (!user.organizationId) {
      return { 
        isValid: false, 
        reason: 'no-organization', 
        errorCode: 'MISSING_ORGANIZATION' 
      }
    }
    
    return { 
      isValid: true, 
      reason: 'valid' 
    }
  }, [user])

  // 🔒 综合安全验证函数
  const validateChatAccess = useCallback((groupId: string): SecurityValidationResult => {
    // 首先验证用户权限
    if (!userValidation.isValid) {
      return userValidation
    }

    // 然后验证群聊ID
    const groupIdResult = validateGroupId(groupId)
    if (!groupIdResult.isValid) {
      return {
        isValid: false,
        reason: 'invalid-group-id',
        errorCode: `GROUP_ID_${groupIdResult.errorType?.toUpperCase()}`
      }
    }

    return { isValid: true, reason: 'valid' }
  }, [userValidation, validateGroupId])

  // 🔒 获取安全错误消息的键值（用于国际化）
  const getSecurityErrorKey = useCallback((result: SecurityValidationResult): string => {
    switch (result.reason) {
      case 'no-user':
        return 'chat.security.error.noUser'
      case 'no-organization':
        return 'chat.security.error.noOrganization'
      case 'invalid-group-id':
        return 'chat.security.error.invalidGroupId'
      default:
        return 'chat.security.error.unknown'
    }
  }, [])

  return {
    // 验证函数
    validateGroupId,
    validateChatAccess,
    
    // 状态
    userValidation,
    currentUser: user,
    
    // 工具函数
    getSecurityErrorKey,
    
    // 快速检查
    isUserValid: userValidation.isValid,
    hasOrganization: !!user?.organizationId,
  }
}

// 🔒 安全验证错误类型
export type SecurityErrorType = 
  | 'no-user'
  | 'no-organization' 
  | 'invalid-group-id'
  | 'access-denied'

// 导出的类型已在上方定义