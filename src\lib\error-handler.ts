/**
 * 统一错误处理工具类
 * 集成 sonner toast 系统和 UI Store 通知管理
 *
 * 功能特性：
 * - 错误分类和友好化消息转换
 * - sonner toast 集成显示
 * - 国际化错误消息支持
 * - 错误重试和用户操作引导
 * - React 错误边界传播支持
 */

import { toast } from 'sonner'
import { AxiosError } from 'axios'

// 错误类型枚举
export enum ErrorType {
  NETWORK = 'network',
  SERVER = 'server',
  CLIENT = 'client',
  API = 'api',
  VALIDATION = 'validation',
  AUTH = 'auth',
  UNKNOWN = 'unknown',
}

// 错误严重级别
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

// 错误处理配置
export interface ErrorHandlerConfig {
  /** 是否显示 toast */
  showToast?: boolean
  /** 是否向上传播到错误边界 */
  propagateToErrorBoundary?: boolean
  /** 是否记录错误日志 */
  logError?: boolean
  /** 自定义错误消息 */
  customMessage?: string
  /** 错误操作回调 */
  onError?: (error: ProcessedError) => void
  /** 重试回调 */
  onRetry?: () => void | Promise<void>
}

// 处理后的错误信息
export interface ProcessedError {
  /** 错误类型 */
  type: ErrorType
  /** 严重级别 */
  severity: ErrorSeverity
  /** 用户友好的错误消息 */
  userMessage: string
  /** 技术错误消息 */
  technicalMessage: string
  /** HTTP状态码 */
  statusCode?: number
  /** 错误码 */
  errorCode?: string | number
  /** 原始错误对象 */
  originalError: Error
  /** 是否可重试 */
  canRetry: boolean
  /** 建议的用户操作 */
  suggestedActions: string[]
}

/**
 * 错误分类器
 * 根据错误对象判断错误类型和严重程度
 */
class ErrorClassifier {
  /**
   * 分类错误
   */
  static classify(error: Error | AxiosError | any): { type: ErrorType; severity: ErrorSeverity } {
    // Axios 网络错误
    if (error.isAxiosError) {
      const axiosError = error as AxiosError

      if (!axiosError.response) {
        // 网络连接错误
        return { type: ErrorType.NETWORK, severity: ErrorSeverity.HIGH }
      }

      const status = axiosError.response.status

      if (status >= 500) {
        // 服务器错误
        return { type: ErrorType.SERVER, severity: ErrorSeverity.HIGH }
      } else if (status === 401 || status === 403) {
        // 认证错误
        return { type: ErrorType.AUTH, severity: ErrorSeverity.MEDIUM }
      } else if (status >= 400) {
        // 客户端错误
        return { type: ErrorType.CLIENT, severity: ErrorSeverity.MEDIUM }
      }
    }

    // API 业务逻辑错误
    if ((error as any).isApiError) {
      return { type: ErrorType.API, severity: ErrorSeverity.MEDIUM }
    }

    // 验证错误
    if (error.name === 'ValidationError' || error.message.includes('validation')) {
      return { type: ErrorType.VALIDATION, severity: ErrorSeverity.LOW }
    }

    // 未知错误
    return { type: ErrorType.UNKNOWN, severity: ErrorSeverity.MEDIUM }
  }
}

/**
 * 错误消息生成器
 * 将技术错误转换为用户友好的消息
 */
class ErrorMessageGenerator {
  /**
   * 生成用户友好的错误消息
   */
  static generateUserMessage(error: Error | AxiosError | any, type: ErrorType): string {
    switch (type) {
      case ErrorType.NETWORK:
        return '网络连接失败，请检查您的网络连接后重试'

      case ErrorType.SERVER:
        if ((error as any).isAxiosError) {
          const status = (error as AxiosError).response?.status
          if (status === 500) {
            return '服务器暂时无法处理您的请求，请稍后重试'
          } else if (status === 502 || status === 503) {
            return '服务暂时不可用，请稍后重试'
          } else if (status === 504) {
            return '请求超时，请稍后重试'
          }
        }
        return '服务器错误，请稍后重试'

      case ErrorType.CLIENT:
        if ((error as any).isAxiosError) {
          const status = (error as AxiosError).response?.status
          if (status === 400) {
            return '请求参数有误，请检查后重试'
          } else if (status === 404) {
            return '请求的资源不存在'
          } else if (status === 429) {
            return '请求过于频繁，请稍后重试'
          }
        }
        return '请求失败，请检查输入信息'

      case ErrorType.AUTH:
        return '身份验证失败，请重新登录'

      case ErrorType.API:
        // 尝试使用 API 返回的错误消息
        const apiError = error as any
        if (apiError.response?.message) {
          return apiError.response.message
        }
        return '操作失败，请重试'

      case ErrorType.VALIDATION:
        return '输入信息格式不正确，请检查后重试'

      default:
        return '操作失败，请重试'
    }
  }

  /**
   * 生成建议操作
   */
  static generateSuggestedActions(type: ErrorType, canRetry: boolean): string[] {
    const actions: string[] = []

    if (canRetry) {
      actions.push('重试')
    }

    switch (type) {
      case ErrorType.NETWORK:
        actions.push('检查网络连接', '刷新页面')
        break

      case ErrorType.SERVER:
        actions.push('稍后重试', '联系客服')
        break

      case ErrorType.AUTH:
        actions.push('重新登录')
        break

      case ErrorType.CLIENT:
      case ErrorType.VALIDATION:
        actions.push('检查输入信息')
        break

      default:
        actions.push('刷新页面')
    }

    return actions
  }
}

/**
 * Toast 错误显示器
 * 使用 sonner 显示错误提示
 */
class ToastErrorDisplay {
  /**
   * 显示错误 Toast
   */
  static show(processedError: ProcessedError, config?: ErrorHandlerConfig): void {
    const { type, severity, userMessage, canRetry } = processedError
    const { onRetry } = config || {}

    // 根据严重程度选择 toast 类型
    const toastOptions = {
      description: userMessage,
      duration: this.getToastDuration(severity),
      action:
        canRetry && onRetry
          ? {
              label: '重试',
              onClick: onRetry,
            }
          : undefined,
    }

    // 根据错误类型显示不同样式的 toast
    switch (severity) {
      case ErrorSeverity.CRITICAL:
      case ErrorSeverity.HIGH:
        toast.error('操作失败', toastOptions)
        break

      case ErrorSeverity.MEDIUM:
        if (type === ErrorType.AUTH) {
          toast.warning('需要重新登录', toastOptions)
        } else {
          toast.error('请求失败', toastOptions)
        }
        break

      case ErrorSeverity.LOW:
        toast.info('提示', toastOptions)
        break

      default:
        toast.error('操作失败', toastOptions)
    }
  }

  /**
   * 获取 Toast 显示时长
   */
  private static getToastDuration(severity: ErrorSeverity): number {
    switch (severity) {
      case ErrorSeverity.CRITICAL:
        return 8000 // 8秒
      case ErrorSeverity.HIGH:
        return 6000 // 6秒
      case ErrorSeverity.MEDIUM:
        return 4000 // 4秒
      case ErrorSeverity.LOW:
        return 3000 // 3秒
      default:
        return 4000
    }
  }
}

/**
 * 主要的错误处理器类
 */
export class ErrorHandler {
  /**
   * 处理错误的主入口方法
   */
  static handle(error: Error | AxiosError | any, config: ErrorHandlerConfig = {}): ProcessedError {
    // 默认配置
    const defaultConfig: ErrorHandlerConfig = {
      showToast: true,
      propagateToErrorBoundary: false,
      logError: true,
      ...config,
    }

    // 错误分类
    const { type, severity } = ErrorClassifier.classify(error)

    // 生成用户友好消息
    const userMessage =
      defaultConfig.customMessage || ErrorMessageGenerator.generateUserMessage(error, type)

    // 判断是否可重试
    const canRetry = this.canRetry(type, severity)

    // 生成建议操作
    const suggestedActions = ErrorMessageGenerator.generateSuggestedActions(type, canRetry)

    // 构建处理后的错误对象
    const processedError: ProcessedError = {
      type,
      severity,
      userMessage,
      technicalMessage: error.message || '未知错误',
      statusCode: error.isAxiosError ? error.response?.status : undefined,
      errorCode: (error as any).code || (error as any).response?.code,
      originalError: error,
      canRetry,
      suggestedActions,
    }

    // 记录错误日志
    if (defaultConfig.logError) {
      this.logError(processedError)
    }

    // 显示 Toast
    if (defaultConfig.showToast) {
      ToastErrorDisplay.show(processedError, defaultConfig)
    }

    // 执行自定义错误回调
    if (defaultConfig.onError) {
      try {
        defaultConfig.onError(processedError)
      } catch (callbackError) {
        console.error('错误处理回调执行失败:', callbackError)
      }
    }

    // 决定是否向上传播错误
    if (defaultConfig.propagateToErrorBoundary && severity >= ErrorSeverity.HIGH) {
      // 重新抛出错误以触发 React 错误边界
      throw new Error(`[${type.toUpperCase()}] ${processedError.userMessage}`)
    }

    return processedError
  }

  /**
   * 判断错误是否可重试
   */
  private static canRetry(type: ErrorType, severity: ErrorSeverity): boolean {
    // 网络错误和服务器错误通常可以重试
    if (type === ErrorType.NETWORK || type === ErrorType.SERVER) {
      return true
    }

    // 低严重性错误可以重试
    if (severity === ErrorSeverity.LOW) {
      return true
    }

    // 认证错误不建议自动重试（需要用户操作）
    if (type === ErrorType.AUTH) {
      return false
    }

    // 客户端错误通常不应该重试
    if (type === ErrorType.CLIENT) {
      return false
    }

    return false
  }

  /**
   * 记录错误日志
   */
  private static logError(processedError: ProcessedError): void {
    const logData = {
      type: processedError.type,
      severity: processedError.severity,
      message: processedError.technicalMessage,
      statusCode: processedError.statusCode,
      errorCode: processedError.errorCode,
      timestamp: new Date().toISOString(),
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : undefined,
      url: typeof window !== 'undefined' ? window.location.href : undefined,
    }

    // 根据严重程度选择日志级别
    switch (processedError.severity) {
      case ErrorSeverity.CRITICAL:
      case ErrorSeverity.HIGH:
        console.error('🚨 [Error Handler]', logData, processedError.originalError)
        break
      case ErrorSeverity.MEDIUM:
        console.warn('⚠️ [Error Handler]', logData)
        break
      case ErrorSeverity.LOW:
        console.info('ℹ️ [Error Handler]', logData)
        break
      default:
        console.log('📝 [Error Handler]', logData)
    }
  }
}

/**
 * 便捷的错误处理函数
 */
export const handleError = ErrorHandler.handle

/**
 * 预设配置的错误处理函数
 */
export const handleErrorSilently = (error: Error | AxiosError | any) =>
  ErrorHandler.handle(error, { showToast: false, logError: true })

export const handleCriticalError = (error: Error | AxiosError | any) =>
  ErrorHandler.handle(error, {
    showToast: true,
    propagateToErrorBoundary: true,
    logError: true,
  })

export const handleNetworkError = (
  error: Error | AxiosError | any,
  onRetry?: () => void | Promise<void>
) =>
  ErrorHandler.handle(error, {
    showToast: true,
    ...(onRetry && { onRetry }),
    customMessage: '网络连接失败，请检查网络后重试',
  })
