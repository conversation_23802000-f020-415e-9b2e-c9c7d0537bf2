/**
 * 中间件组合器
 * 实现函数式编程的中间件组合模式，符合 Next.js Edge Runtime 要求
 */

import { NextRequest, NextResponse } from 'next/server'
import {
  MiddlewareFunction,
  EnhancedMiddlewareFunction,
  MiddlewareContext,
  MiddlewareOptions,
  MiddlewareResult,
} from './types'

/**
 * 创建中间件执行上下文
 */
function createContext(request: NextRequest): MiddlewareContext {
  const pathname = request.nextUrl.pathname

  return {
    pathname,
    timing: {
      start: Date.now(),
    },
  }
}

/**
 * 记录性能指标
 */
function recordTiming(
  context: MiddlewareContext,
  phase: keyof Omit<MiddlewareContext['timing'], 'start'>,
  startTime: number
): void {
  context.timing[phase] = Date.now() - startTime
}

/**
 * debug 日志工具
 */
function debugLog(message: string, data?: any): void {
  if (process.env.NODE_ENV === 'development') {
    console.log(`[Middleware] ${message}`, data ? JSON.stringify(data, null, 2) : '')
  }
}

/**
 * 错误日志工具
 */
function errorLog(message: string, error: any): void {
  console.error(`[Middleware Error] ${message}`, error)
}

/**
 * 基础中间件组合器
 * 适用于简单的中间件函数
 */
export function composeMiddleware(
  middlewares: MiddlewareFunction[],
  options: MiddlewareOptions = {}
) {
  const { enableDebugLogs = process.env.NODE_ENV === 'development', errorHandling = 'continue' } =
    options

  return async (request: NextRequest): Promise<NextResponse> => {
    const startTime = Date.now()

    if (enableDebugLogs) {
      debugLog(`开始执行中间件链`, {
        pathname: request.nextUrl.pathname,
        middlewareCount: middlewares.length,
      })
    }

    try {
      // 按顺序执行中间件
      for (let i = 0; i < middlewares.length; i++) {
        const middleware = middlewares[i]
        const middlewareStartTime = Date.now()

        try {
          const result = await middleware(request)

          if (enableDebugLogs) {
            debugLog(`中间件 ${i + 1} 执行完成`, {
              duration: Date.now() - middlewareStartTime,
              hasResponse: !!result,
            })
          }

          // 如果中间件返回了响应，立即返回（早期退出）
          if (result) {
            if (enableDebugLogs) {
              debugLog(`中间件链提前结束`, {
                exitedAt: i + 1,
                totalDuration: Date.now() - startTime,
              })
            }
            return result
          }
        } catch (error) {
          errorLog(`中间件 ${i + 1} 执行失败`, error)

          // 根据错误处理策略决定是否继续
          if (errorHandling === 'stop') {
            // 发生错误时返回默认响应
            return NextResponse.next()
          }
          // 继续执行下一个中间件
        }
      }

      // 所有中间件都执行完成，没有返回响应，继续请求
      if (enableDebugLogs) {
        debugLog(`中间件链执行完成`, {
          totalDuration: Date.now() - startTime,
        })
      }

      return NextResponse.next()
    } catch (error) {
      errorLog('中间件链执行失败', error)
      return NextResponse.next()
    }
  }
}

/**
 * 增强的中间件组合器
 * 支持上下文传递和更丰富的功能
 */
export function composeEnhancedMiddleware(
  middlewares: EnhancedMiddlewareFunction[],
  options: MiddlewareOptions = {}
) {
  const {
    enableDebugLogs = process.env.NODE_ENV === 'development',
    enableTiming = process.env.NODE_ENV === 'development',
    errorHandling = 'continue',
  } = options

  return async (request: NextRequest): Promise<NextResponse> => {
    const context = createContext(request)

    if (enableDebugLogs) {
      debugLog(`开始执行增强中间件链`, {
        pathname: context.pathname,
        middlewareCount: middlewares.length,
      })
    }

    try {
      // 按顺序执行中间件
      for (let i = 0; i < middlewares.length; i++) {
        const middleware = middlewares[i]
        const middlewareStartTime = Date.now()

        try {
          const result = await middleware(request, context)

          if (enableTiming) {
            // 记录每个中间件的执行时间（简化版）
            const duration = Date.now() - middlewareStartTime
            if (enableDebugLogs) {
              debugLog(`增强中间件 ${i + 1} 执行完成`, {
                duration,
                hasResponse: !!result,
              })
            }
          }

          // 如果中间件返回了响应，立即返回
          if (result) {
            if (enableDebugLogs) {
              debugLog(`增强中间件链提前结束`, {
                exitedAt: i + 1,
                context: {
                  isAuthenticated: context.isAuthenticated,
                  userRole: context.userRole,
                },
              })
            }
            return result
          }
        } catch (error) {
          errorLog(`增强中间件 ${i + 1} 执行失败`, error)

          if (errorHandling === 'stop') {
            return NextResponse.next()
          }
        }
      }

      // 所有中间件都执行完成
      if (enableDebugLogs) {
        const totalDuration = Date.now() - context.timing.start
        debugLog(`增强中间件链执行完成`, {
          totalDuration,
          finalContext: {
            isAuthenticated: context.isAuthenticated,
            userRole: context.userRole,
            isAdmin: context.isAdmin,
          },
        })
      }

      return NextResponse.next()
    } catch (error) {
      errorLog('增强中间件链执行失败', error)
      return NextResponse.next()
    }
  }
}

/**
 * 条件执行中间件组合器
 * 只在满足特定条件时执行中间件
 */
export function conditionalMiddleware(
  condition: (request: NextRequest) => boolean,
  middleware: MiddlewareFunction
): MiddlewareFunction {
  return async (request: NextRequest): Promise<MiddlewareResult> => {
    if (condition(request)) {
      return await middleware(request)
    }
    return null
  }
}

/**
 * 路径匹配中间件组合器
 * 只对特定路径执行中间件
 */
export function pathMatchMiddleware(
  pathPattern: string | RegExp,
  middleware: MiddlewareFunction
): MiddlewareFunction {
  const matcher =
    typeof pathPattern === 'string'
      ? (path: string) => path.startsWith(pathPattern)
      : (path: string) => pathPattern.test(path)

  return conditionalMiddleware(request => matcher(request.nextUrl.pathname), middleware)
}

/**
 * 并行执行中间件组合器
 * 适用于独立的中间件任务（如安全头设置）
 */
export function parallelMiddleware(middlewares: MiddlewareFunction[]): MiddlewareFunction {
  return async (request: NextRequest): Promise<MiddlewareResult> => {
    try {
      // 并行执行所有中间件
      const results = await Promise.allSettled(middlewares.map(middleware => middleware(request)))

      // 返回第一个非null的结果
      for (const result of results) {
        if (result.status === 'fulfilled' && result.value) {
          return result.value
        }
      }

      return null
    } catch (error) {
      errorLog('并行中间件执行失败', error)
      return null
    }
  }
}
