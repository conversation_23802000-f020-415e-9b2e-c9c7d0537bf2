/**
 * 聊天状态Store - 完全基于BaseWebSocketMessage标准
 * 设计原则：Python后端只提供数据，前端处理所有业务逻辑
 */

import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'
import { subscribeWithSelector } from 'zustand/middleware'
import type {
  BaseWebSocketMessage,
  MessagePayload,
  StreamingPayload,
  CheckpointPayload,
  ReportPayload,
  ErrorPayload,
  FormField,
} from '@/types/websocket-event-type'
import {
  isStreamingPayload,
  isCheckpointPayload,
  isReportPayload,
  isErrorPayload,
} from '@/types/websocket-event-type'

import {
  createConnection,
  createConnectionByType,
  canUseSocketIO,
} from '@/lib/connection/connection-factory'
import { ConnectionType } from '@/lib/connection/types'
import {
  ConnectionStatus,
  type IConnectionManager,
  type ConnectionConfig,
} from '@/lib/connection/types'

// ============================================================================
// 标准数据接口 - 完全基于BaseWebSocketMessage
// ============================================================================

/**
 * 聊天会话 - 存储BaseWebSocketMessage数组
 */
export interface ChatSession {
  sessionId: string
  groupChatId: string
  userId: string
  organizationId: string
  messages: BaseWebSocketMessage[] // 🎯 核心：直接存储标准消息
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

/**
 * 用户输入状态
 */
export interface UserInput {
  content: string
  isSubmitting: boolean
  isTyping: boolean
}

/**
 * UI状态信息 - 从BaseWebSocketMessage派生
 */
export interface MessageUIState {
  id: string // 生成的UI id
  status: 'sending' | 'sent' | 'delivered' | 'failed'
  accumulatedText?: string // 前端累积的streaming文本
}

/**
 * 聊天状态 - 群聊应用架构，专注核心功能
 */
export interface ChatState {
  // 连接状态
  connectionStatus: ConnectionStatus
  isConnecting: boolean
  isConnected: boolean
  lastError: string | undefined

  // 群聊和会话管理 - 重新设计的核心数据结构
  currentGroupId: string | undefined // 当前活跃的群聊ID
  currentSession: ChatSession | undefined // 当前活跃的会话

  // 群聊到活跃会话的映射 - 一个群聊对应一个活跃会话
  groupToSessionMap: Record<string, string> // { groupId: sessionId }

  // 所有会话数据
  sessions: Record<string, ChatSession>

  // UI状态映射 - 轻量级UI增强
  messageUIStates: Record<string, MessageUIState>

  // 用户输入
  userInput: UserInput

  // 连接实例
  connectionManager: IConnectionManager | undefined

  // 清理函数
  cleanupFunctions: (() => void)[] | undefined
}

/**
 * 聊天操作接口 - 基于BaseWebSocketMessage的标准操作
 */
export interface ChatActions {
  // ============================================================================
  // 连接管理
  // ============================================================================
  initializeConnection: (config?: ConnectionConfig) => Promise<void>
  connect: (config?: ConnectionConfig) => Promise<void>
  disconnect: () => void
  updateConnectionStatus: (status: ConnectionStatus) => void
  handleIncomingMessage: (message: BaseWebSocketMessage) => void
  handleStreamingMessage: (message: BaseWebSocketMessage) => void
  handleCheckpointMessage: (message: BaseWebSocketMessage) => void
  handleReportMessage: (message: BaseWebSocketMessage) => void
  handleErrorMessage: (message: BaseWebSocketMessage) => void

  // Mock模式管理
  switchToMockMode: (scenarioId?: string) => Promise<void>
  switchToSocketMode: () => Promise<void>
  switchMockScenario: (scenarioId: string) => boolean
  getCurrentMockScenario: () => any
  getAvailableMockScenarios: () => any[]

  // ============================================================================
  // 群聊和会话管理 - 群聊应用核心方法
  // ============================================================================
  setCurrentGroup: (groupId: string) => void
  getActiveSessionByGroupId: (groupId: string) => ChatSession | undefined
  ensureSessionForGroup: (groupId: string, userId: string, organizationId: string) => ChatSession

  // ============================================================================
  // 传统会话管理 - 保持兼容性
  // ============================================================================
  createSession: (groupChatId: string, userId: string, organizationId: string) => string
  setCurrentSession: (sessionId: string) => void
  getCurrentSession: (sessionId: string) => ChatSession | undefined
  clearSession: (sessionId: string) => void

  // ============================================================================
  // 消息处理 - 核心：BaseWebSocketMessage操作
  // ============================================================================

  /**
   * 发送用户消息 - 创建标准的BaseWebSocketMessage
   */
  sendUserMessage: (content: string) => Promise<void>

  /**
   * 添加WebSocket消息到会话
   */
  addWebSocketMessage: (message: BaseWebSocketMessage) => void

  /**
   * 更新消息的UI状态
   */
  updateMessageUIState: (messageId: string, updates: Partial<MessageUIState>) => void

  /**
   * 删除消息
   */
  deleteMessage: (messageId: string, sessionId?: string) => void

  /**
   * 根据timestamp查找消息
   */
  getMessageByTimestamp: (timestamp: string, sessionId?: string) => BaseWebSocketMessage | undefined

  // ============================================================================
  // 兼容性方法
  // ============================================================================
  sendMessage: (params: { content: string; sessionId: string; metadata?: any }) => Promise<void>
  addMessage: (messageData: any) => string
  updateMessage: (messageId: string, updates: Partial<BaseWebSocketMessage>) => void
  retryMessage: (messageId: string, sessionId?: string) => Promise<void>
  getMessageById: (messageId: string, sessionId?: string) => BaseWebSocketMessage | undefined

  // ============================================================================
  // 表单处理
  // ============================================================================
  submitForm: (formId: string, values: Record<string, any>) => Promise<void>
  updateFormField: (formId: string, fieldId: string, value: any) => void

  // ============================================================================
  // 用户输入
  // ============================================================================
  setUserInput: (input: Partial<UserInput>) => void
  updateUserInput: (content: string) => void
  setIsSubmitting: (isSubmitting: boolean) => void
  setIsTyping: (isTyping: boolean) => void
  clearUserInput: () => void

  // ============================================================================
  // 实用工具
  // ============================================================================
  clearAllData: () => void
  getSessionMessages: (sessionId: string) => BaseWebSocketMessage[]
  getSessionStats: () => {
    totalSessions: number
    totalMessages: number
    streamingMessages: number
    checkpointMessages: number
    reportMessages: number
    errorMessages: number
  }

  /**
   * 获取streaming消息的累积文本
   */
  getAccumulatedStreamingText: (sessionId: string, beforeTimestamp?: string) => string
}

export type ChatStore = ChatState & ChatActions

// ============================================================================
// 工具函数 - streaming文本累积逻辑
// ============================================================================

/**
 * 生成消息的唯一标识符
 * 使用timestamp + 随机字符串确保唯一性
 */
const generateMessageId = (message?: BaseWebSocketMessage): string => {
  const timestamp = message?.timestamp || new Date().toISOString()
  const random = Math.random().toString(36).substring(2, 9)
  const type = message?.payload.type || 'unknown'
  return `msg-${timestamp.replace(/[:.]/g, '')}-${type}-${random}`
}

/**
 * 创建标准的BaseWebSocketMessage
 * 统一消息创建逻辑，避免类型强制转换
 */
const createStandardMessage = (
  sessionInfo: {
    groupChatId: string
    sessionId: string
    userId: string
    organizationId: string
  },
  payload: MessagePayload,
  options?: {
    id?: string
    timestamp?: string
    status?: string
    metadata?: any
  }
): BaseWebSocketMessage => {
  const timestamp = options?.timestamp || new Date().toISOString()
  const id = options?.id || generateMessageId()

  // 🐛 调试日志：追踪用户消息创建时的 userId 值
  console.log('🔍 DEBUG_createStandardMessage:', {
    source: 'chat-store.ts',
    userId: sessionInfo.userId,
    groupChatId: sessionInfo.groupChatId,
    sessionId: sessionInfo.sessionId,
    organizationId: sessionInfo.organizationId,
    messageType: payload.type,
    messageId: id,
    timestamp
  })

  return {
    id,
    groupChatId: sessionInfo.groupChatId,
    sessionId: sessionInfo.sessionId,
    userId: sessionInfo.userId,
    organizationId: sessionInfo.organizationId,
    payload,
    timestamp,
    ...(options?.status && {
      status: options.status as 'sending' | 'sent' | 'delivered' | 'failed',
    }),
    metadata: options?.metadata,
  }
}

/**
 * 创建流式消息的便捷函数
 */
const createStreamingMessage = (
  sessionInfo: {
    groupChatId: string
    sessionId: string
    userId: string
    organizationId: string
  },
  content: string,
  isComplete: boolean = true,
  options?: {
    id?: string
    timestamp?: string
    status?: string
  }
): BaseWebSocketMessage => {
  const payload: StreamingPayload = {
    type: 'streaming',
    delta: content,
    isComplete,
  }

  return createStandardMessage(sessionInfo, payload, {
    ...options,
    status: options?.status || (isComplete ? 'delivered' : 'sending'),
    metadata: {
      streamingState: {
        isComplete,
        accumulatedText: content,
      },
    },
  })
}

/**
 * 创建表单消息的便捷函数
 */
const createCheckpointMessage = (
  sessionInfo: {
    groupChatId: string
    sessionId: string
    userId: string
    organizationId: string
  },
  fields: FormField[],
  options?: {
    id?: string
    timestamp?: string
  }
): BaseWebSocketMessage => {
  const payload: CheckpointPayload = {
    type: 'checkpoint',
    fields,
  }

  return createStandardMessage(sessionInfo, payload, options)
}

/**
 * 累积streaming消息的文本内容（遵循isComplete逻辑）
 */
const accumulateStreamingText = (
  messages: BaseWebSocketMessage[],
  beforeTimestamp?: string
): string => {
  let accumulated = ''

  for (const message of messages) {
    if (beforeTimestamp && message.timestamp >= beforeTimestamp) {
      break
    }

    if (isStreamingPayload(message.payload)) {
      accumulated += message.payload.delta

      // 🎯 关键：遇到isComplete=true的消息，表示一个完整回合结束
      if (message.payload.isComplete) {
        break
      }
    }
  }

  return accumulated
}

/**
 * 检查最后一个消息是否是未完成的streaming消息
 */
const getLastIncompleteStreamingMessage = (
  messages: BaseWebSocketMessage[]
): BaseWebSocketMessage | null => {
  if (messages.length === 0) return null

  const lastMessage = messages[messages.length - 1]
  if (isStreamingPayload(lastMessage.payload) && !lastMessage.payload.isComplete) {
    return lastMessage
  }

  return null
}

// ============================================================================
// ChatStore实现 - 完全基于BaseWebSocketMessage
// ============================================================================

export const useChatStore = create<ChatStore>()(
  subscribeWithSelector(
    immer((set, get) => ({
      // ============================================================================
      // 初始状态 - 群聊应用架构
      // ============================================================================
      connectionStatus: ConnectionStatus.DISCONNECTED,
      isConnecting: false,
      isConnected: false,
      lastError: undefined,

      // 群聊和会话状态
      currentGroupId: undefined,
      currentSession: undefined,
      groupToSessionMap: {},
      sessions: {},

      messageUIStates: {},
      userInput: {
        content: '',
        isSubmitting: false,
        isTyping: false,
      },
      connectionManager: undefined,
      cleanupFunctions: undefined,

      // ============================================================================
      // 连接管理
      // ============================================================================

      initializeConnection: async (config?: ConnectionConfig) => {
        set(state => {
          // 创建连接管理器
          state.connectionManager = createConnection(config)

          // 🎯 关键修复：如果是Mock连接，设置会话信息
          const currentSession = state.currentSession
          if (currentSession && state.connectionManager.getType() === ConnectionType.MOCK) {
            const mockConnection = state.connectionManager as any
            if ('setSessionInfo' in mockConnection) {
              console.log('🎭 INIT_SESSION_INFO_SET:', {
                groupId: state.currentGroupId,
                sessionId: currentSession.sessionId,
              })
              mockConnection.setSessionInfo({
                groupChatId: currentSession.groupChatId,
                sessionId: currentSession.sessionId,
                userId: currentSession.userId,
                organizationId: currentSession.organizationId,
              })
            }
          }

          // 设置连接状态监听
          const statusUnsubscribe = state.connectionManager.onStatus(status => {
            get().updateConnectionStatus(status)
          })

          // 设置消息处理 - 直接处理BaseWebSocketMessage
          const messageUnsubscribe = state.connectionManager.onMessage(message => {
            try {
              get().handleIncomingMessage(message)
            } catch (error) {
              console.error('❌ Error in message handler:', error)
            }
          })

          // 保存清理函数
          state.cleanupFunctions = [statusUnsubscribe, messageUnsubscribe]
        })

        console.log('✅ Connection Manager初始化完成')
      },

      /**
       * 核心消息处理器 - 遵循BaseWebSocketMessage标准和isComplete逻辑
       */
      handleIncomingMessage: (message: BaseWebSocketMessage) => {
        const state = get()

        if (!message.payload || !state.currentSession) {
          console.warn('❌ MSG_REJECTED:', {
            reason: !message.payload ? 'no-payload' : 'no-session',
            hasSession: !!state.currentSession,
            sessionId: state.currentSession?.sessionId,
          })
          return
        }

        console.log('📨 MSG_RECEIVED:', {
          type: message.payload.type,
          sessionId: state.currentSession.sessionId,
          messageCount: state.currentSession.messages.length,
        })

        // 🎯 关键：根据payload类型处理，遵循isComplete逻辑
        switch (message.payload.type) {
          case 'streaming':
            get().handleStreamingMessage(message)
            break

          case 'checkpoint':
            get().handleCheckpointMessage(message)
            break

          case 'report':
            get().handleReportMessage(message)
            break

          case 'error':
            get().handleErrorMessage(message)
            break

          default:
            console.warn('❌ Unknown message type:', message.payload)
        }
      },

      /**
       * 处理streaming消息 - 核心累积逻辑
       */
      handleStreamingMessage: (message: BaseWebSocketMessage) => {
        if (!isStreamingPayload(message.payload)) return

        const streamingPayload = message.payload
        const state = get()

        if (!state.currentSession) return

        console.log('🌊 STREAM_PROCESS:', {
          delta: streamingPayload.delta?.substring(0, 10) + '...',
          isComplete: streamingPayload.isComplete,
          sessionId: state.currentSession.sessionId,
        })

        // 🎯 关键逻辑：检查是否需要创建新气泡
        const lastIncompleteMessage = getLastIncompleteStreamingMessage(
          state.currentSession.messages
        )

        if (lastIncompleteMessage) {
          // 更新现有streaming消息
          console.log('🔄 UPDATE_EXISTING')
          set(state => {
            const session = state.currentSession!
            const messageIndex = session.messages.findIndex(m => m.id === lastIncompleteMessage.id)

            if (messageIndex !== -1) {
              // 获取现有的累积文本
              const existingPayload = session.messages[messageIndex].payload as StreamingPayload
              const existingAccumulated =
                session.messages[messageIndex].metadata?.streamingState?.accumulatedText ||
                existingPayload.delta
              const newAccumulated = existingAccumulated + streamingPayload.delta

              // 更新消息，将累积文本存储在metadata中
              session.messages[messageIndex] = {
                ...session.messages[messageIndex],
                payload: {
                  ...streamingPayload,
                  delta: streamingPayload.delta, // 保持原始delta
                },
                metadata: {
                  ...session.messages[messageIndex].metadata,
                  streamingState: {
                    isComplete: streamingPayload.isComplete,
                    accumulatedText: newAccumulated,
                  },
                },
                status: streamingPayload.isComplete ? 'delivered' : 'sending',
                timestamp: message.timestamp,
              }

              console.log('📝 MSG_UPDATED:', {
                accumulated: newAccumulated?.substring(0, 20) + '...',
                isComplete: streamingPayload.isComplete,
              })

              session.updatedAt = new Date()
            }
          })
        } else {
          // 创建新的streaming消息（新对话回合）
          console.log('✨ CREATE_NEW')
          get().addWebSocketMessage(message)
        }
      },

      /**
       * 处理其他类型消息
       */
      handleCheckpointMessage: (message: BaseWebSocketMessage) => {
        console.log('📋 Processing checkpoint message')
        get().addWebSocketMessage(message)
      },

      handleReportMessage: (message: BaseWebSocketMessage) => {
        console.log('📊 Processing report message')
        get().addWebSocketMessage(message)
      },

      handleErrorMessage: (message: BaseWebSocketMessage) => {
        console.log('❌ Processing error message')
        get().addWebSocketMessage(message)
      },

      // 连接
      connect: async (config?: ConnectionConfig) => {
        const state = get()

        // 如果没有初始化，先初始化
        if (!state.connectionManager) {
          await get().initializeConnection(config)
        }

        const { connectionManager } = get()
        if (!connectionManager) {
          throw new Error('Connection Manager not initialized')
        }

        set(state => {
          state.isConnecting = true
          state.lastError = undefined
        })

        try {
          const result = await connectionManager.connect(config)
          if (!result.success) {
            throw new Error(result.error || 'Connection failed')
          }
        } catch (error) {
          set(state => {
            state.lastError = error instanceof Error ? error.message : 'Connection failed'
            state.isConnecting = false
          })
          throw error
        }
      },

      // 断开连接
      disconnect: () => {
        const { connectionManager, cleanupFunctions } = get()
        if (connectionManager) {
          connectionManager.disconnect()
        }
        // 清理事件监听器
        if (cleanupFunctions) {
          cleanupFunctions.forEach(cleanup => cleanup())
        }
        set(state => {
          state.isConnecting = false
          state.cleanupFunctions = []
        })
      },

      // 更新连接状态
      updateConnectionStatus: (status: ConnectionStatus) => {
        set(state => {
          state.connectionStatus = status
          state.isConnecting =
            status === ConnectionStatus.CONNECTING || status === ConnectionStatus.RECONNECTING
          state.isConnected = status === ConnectionStatus.CONNECTED
        })
      },

      // ============================================================================
      // 群聊管理方法 - 核心重构
      // ============================================================================

      // 设置当前群聊
      setCurrentGroup: (groupId: string) => {
        set(state => {
          console.log('🏠 SET_CURRENT_GROUP:', { groupId })
          state.currentGroupId = groupId

          // 查找该群聊的活跃会话
          const sessionId = state.groupToSessionMap[groupId]
          if (sessionId && state.sessions[sessionId]) {
            state.currentSession = state.sessions[sessionId]
            console.log('✅ FOUND_ACTIVE_SESSION:', { sessionId })
          } else {
            state.currentSession = undefined
            console.log('❌ NO_ACTIVE_SESSION_FOR_GROUP:', { groupId })
          }
        })
      },

      // 根据群聊ID获取活跃会话
      getActiveSessionByGroupId: (groupId: string) => {
        const state = get()
        const sessionId = state.groupToSessionMap[groupId]
        return sessionId ? state.sessions[sessionId] : undefined
      },

      // 为群聊确保有活跃会话（群聊应用核心方法）
      ensureSessionForGroup: (groupId: string, userId: string, organizationId: string) => {
        let session = get().getActiveSessionByGroupId(groupId)

        if (!session) {
          // 创建新会话
          const sessionId = `session-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
          const now = new Date()

          session = {
            sessionId,
            groupChatId: groupId,
            userId,
            organizationId,
            messages: [],
            isActive: true,
            createdAt: now,
            updatedAt: now,
          }

          set(state => {
            state.sessions[sessionId] = session!
            state.groupToSessionMap[groupId] = sessionId
            state.currentGroupId = groupId
            state.currentSession = session!
          })

          console.log('🆕 CREATED_SESSION_FOR_GROUP:', { groupId, sessionId })
        } else {
          // 激活现有会话
          set(state => {
            state.currentGroupId = groupId
            state.currentSession = session!
          })

          console.log('✅ ACTIVATED_EXISTING_SESSION:', { groupId, sessionId: session.sessionId })
        }

        return session
      },

      // ============================================================================
      // 传统会话方法 - 保持兼容性
      // ============================================================================

      // 创建会话
      createSession: (groupChatId: string, userId: string, organizationId: string) => {
        const sessionId = `session-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
        const now = new Date()

        set(state => {
          const session: ChatSession = {
            sessionId,
            groupChatId,
            userId,
            organizationId,
            messages: [],
            isActive: true,
            createdAt: now,
            updatedAt: now,
          }
          state.sessions[sessionId] = session
          state.currentSession = session
        })

        return sessionId
      },

      // 设置当前会话
      setCurrentSession: (sessionId: string) => {
        set(state => {
          const session = state.sessions[sessionId]
          if (session) {
            state.currentSession = session
          }
        })
      },

      // 获取当前会话
      getCurrentSession: (sessionId: string) => {
        return get().sessions[sessionId]
      },

      // 清理会话
      clearSession: (sessionId: string) => {
        set(state => {
          delete state.sessions[sessionId]
          if (state.currentSession?.sessionId === sessionId) {
            state.currentSession = undefined
          }
        })
      },

      // ============================================================================
      // 核心消息操作 - BaseWebSocketMessage
      // ============================================================================

      /**
       * 添加WebSocket消息到会话
       */
      addWebSocketMessage: (message: BaseWebSocketMessage) => {
        set(state => {
          if (!state.currentSession) return

          // 确保消息有唯一ID
          const processedMessage = { ...message }
          if (!processedMessage.id) {
            processedMessage.id = generateMessageId(message)
          }

          // 如果是streaming消息，确保metadata中有累积文本
          if (isStreamingPayload(message.payload)) {
            processedMessage.metadata = {
              ...message.metadata,
              streamingState: {
                isComplete: message.payload.isComplete,
                accumulatedText: message.payload.delta, // 新消息的累积文本就是其delta
              },
            }
            processedMessage.status = message.payload.isComplete ? 'delivered' : 'sending'
          }

          // 添加到消息数组
          state.currentSession.messages.push(processedMessage)
          state.currentSession.updatedAt = new Date()

          console.log('✅ MSG_ADDED:', {
            type: message.payload.type,
            sessionId: state.currentSession.sessionId,
            newCount: state.currentSession.messages.length,
          })
        })
      },

      /**
       * 发送用户消息 - 创建标准BaseWebSocketMessage
       */
      sendUserMessage: async (content: string) => {
        const state = get()
        const { connectionManager, currentSession } = state
        if (!connectionManager || !currentSession) {
          throw new Error('Connection or session not available')
        }
        try {
          get().setIsSubmitting(true)
          // 使用统一的消息创建函数，避免类型强制转换
          const userMessage = createStreamingMessage(
            {
              groupChatId: currentSession.groupChatId,
              sessionId: currentSession.sessionId,
              userId: currentSession.userId,
              organizationId: currentSession.organizationId,
            },
            content,
            true, // 用户消息总是完整的
            {
              status: 'sending', // 初始状态为发送中
            }
          )
          get().addWebSocketMessage(userMessage)
          await connectionManager.sendMessage(userMessage)
          get().clearUserInput()
        } catch (error) {
          console.error('❌ Failed to send user message:', error)
          throw error
        } finally {
          get().setIsSubmitting(false)
        }
      },

      /**
       * 更新消息UI状态
       */
      updateMessageUIState: (messageId: string, updates: Partial<MessageUIState>) => {
        set(state => {
          const existing = state.messageUIStates[messageId]
          if (existing) {
            state.messageUIStates[messageId] = {
              ...existing,
              ...updates,
            }
          }
        })
      },

      /**
       * 根据timestamp查找消息
       */
      getMessageByTimestamp: (timestamp: string, sessionId?: string) => {
        const state = get()
        const targetSession = sessionId ? state.sessions[sessionId] : state.currentSession

        if (!targetSession) return undefined

        return targetSession.messages.find(m => m.timestamp === timestamp)
      },

      /**
       * 获取streaming消息的累积文本
       */
      getAccumulatedStreamingText: (sessionId: string, beforeTimestamp?: string) => {
        const session = get().sessions[sessionId]
        if (!session) return ''

        return accumulateStreamingText(session.messages, beforeTimestamp)
      },

      // ============================================================================
      // 兼容性方法 - 为现有代码提供平滑迁移
      // ============================================================================

      /**
       * @deprecated 使用sendUserMessage代替
       */
      sendMessage: async (params: { content: string; sessionId: string; metadata?: any }) => {
        console.warn('⚠️ sendMessage is deprecated, use sendUserMessage instead')
        // 只委托sendUserMessage，移除无效的userMessage构造
        return get().sendUserMessage(params.content ?? '')
      },

      /**
       * @deprecated 使用addWebSocketMessage代替
       */
      addMessage: (messageData: any) => {
        console.warn('⚠️ addMessage is deprecated, use addWebSocketMessage instead')
        const currentSession = get().currentSession
        if (!currentSession) {
          throw new Error('No current session available')
        }

        // 使用统一的消息创建函数
        const dummyMessage = createStreamingMessage(
          {
            groupChatId: currentSession.groupChatId,
            sessionId: currentSession.sessionId,
            userId: currentSession.userId,
            organizationId: currentSession.organizationId,
          },
          messageData.content ?? '',
          true // 兼容消息总是完整的
        )

        get().addWebSocketMessage(dummyMessage)
        return dummyMessage.id // 返回生成的ID
      },

      // 更新消息 - 使用Immer安全更新
      updateMessage: (messageId: string, updates: Partial<BaseWebSocketMessage>) => {
        set(state => {
          if (state.currentSession) {
            // 更新 currentSession 中的消息
            const messageIndex = state.currentSession.messages.findIndex(m => m.id === messageId)
            if (messageIndex !== -1) {
              // 使用Immer安全的深度合并
              const currentMessage = state.currentSession.messages[messageIndex]

              // 更新基本属性
              Object.keys(updates).forEach(key => {
                if (key !== 'metadata') {
                  ;(currentMessage as any)[key] = (updates as any)[key]
                }
              })

              // 安全更新metadata
              if (updates.metadata) {
                if (!currentMessage.metadata) {
                  currentMessage.metadata = {}
                }
                Object.assign(currentMessage.metadata, updates.metadata)
              }

              state.currentSession.updatedAt = new Date()
            }

            // 同步更新 sessions 中的对应会话
            const sessionInStore = state.sessions[state.currentSession.sessionId]
            if (sessionInStore) {
              const messageIndexInStore = sessionInStore.messages.findIndex(m => m.id === messageId)
              if (messageIndexInStore !== -1) {
                const storeMessage = sessionInStore.messages[messageIndexInStore]

                // 更新基本属性
                Object.keys(updates).forEach(key => {
                  if (key !== 'metadata') {
                    ;(storeMessage as any)[key] = (updates as any)[key]
                  }
                })

                // 安全更新metadata
                if (updates.metadata) {
                  if (!storeMessage.metadata) {
                    storeMessage.metadata = {}
                  }
                  Object.assign(storeMessage.metadata, updates.metadata)
                }

                sessionInStore.updatedAt = new Date()
              }
            }
          }
        })
      },

      // 删除消息
      deleteMessage: (messageId: string, sessionId?: string) => {
        set(state => {
          const targetSessionId = sessionId || state.currentSession?.sessionId
          if (targetSessionId) {
            // 从 sessions 中删除
            const sessionInStore = state.sessions[targetSessionId]
            if (sessionInStore) {
              const messageIndex = sessionInStore.messages.findIndex(m => m.id === messageId)
              if (messageIndex !== -1) {
                sessionInStore.messages.splice(messageIndex, 1)
                sessionInStore.updatedAt = new Date()
              }
            }

            // 如果是当前会话，也从 currentSession 中删除
            if (state.currentSession && state.currentSession.sessionId === targetSessionId) {
              const messageIndex = state.currentSession.messages.findIndex(m => m.id === messageId)
              if (messageIndex !== -1) {
                state.currentSession.messages.splice(messageIndex, 1)
                state.currentSession.updatedAt = new Date()
              }
            }
          }
        })
      },

      // 重试消息
      retryMessage: async (messageId: string, sessionId?: string) => {
        const state = get()
        const targetSession = sessionId ? state.sessions[sessionId] : state.currentSession
        if (!targetSession) {
          throw new Error('Session not found')
        }
        const message = targetSession.messages.find(m => m.id === messageId)
        if (!message) {
          throw new Error('Message not found')
        }
        // 只能重试失败的消息
        if (message.status !== 'failed') {
          return
        }
        // 重新发送消息
        try {
          // 只允许重试streaming类型消息
          if (message.payload.type === 'streaming') {
            await get().sendUserMessage((message.payload as any).delta ?? '')
          }
          // 删除原失败消息
          get().deleteMessage(messageId, sessionId)
        } catch (error) {
          throw error
        }
      },

      // 根据ID获取消息
      getMessageById: (messageId: string, sessionId?: string) => {
        const state = get()
        const targetSession = sessionId ? state.sessions[sessionId] : state.currentSession

        if (!targetSession) {
          return undefined
        }

        return targetSession.messages.find(m => m.id === messageId)
      },

      // 提交表单
      submitForm: async (formId: string, values: Record<string, any>) => {
        const { connectionManager, currentSession } = get()
        if (!connectionManager || !currentSession) {
          throw new Error('Required services not available')
        }

        try {
          // 构建表单字段 - 智能识别字段类型
          const fields: FormField[] = Object.entries(values).map(([key, value]) => {
            // 根据值的类型和名称智能推断字段类型
            let fieldType: FormField['type'] = 'text'

            if (typeof value === 'number') {
              fieldType = 'number'
            } else if (typeof value === 'boolean') {
              fieldType = 'checkbox'
            } else if (Array.isArray(value)) {
              fieldType = 'multiselect'
            } else if (value instanceof Date) {
              fieldType = 'date'
            } else if (typeof value === 'string') {
              // 根据内容长度判断是否使用textarea
              if (value.length > 100) {
                fieldType = 'textarea'
              }
              // 根据字段名称判断特殊类型
              if (key.toLowerCase().includes('email')) {
                fieldType = 'text' // 可以扩展为email类型
              }
            }

            return {
              id: key,
              label: key.charAt(0).toUpperCase() + key.slice(1), // 首字母大写作为标签
              type: fieldType,
              required: false,
              defaultValue: value,
              ...(Array.isArray(value) && {
                options: value.map(item => ({
                  label: String(item),
                  value: item,
                })),
              }),
            }
          })

          // 使用统一的消息创建函数
          const checkpointMessage = createCheckpointMessage(
            {
              groupChatId: currentSession.groupChatId,
              sessionId: currentSession.sessionId,
              userId: currentSession.userId,
              organizationId: currentSession.organizationId,
            },
            fields
          )

          await connectionManager.sendMessage(checkpointMessage)
        } catch (error) {
          throw error
        }
      },

      // 更新表单字段
      updateFormField: (formId: string, fieldId: string, value: any) => {
        // checkpointProcessor is removed, so this method is no longer needed
        // If form processing is needed, it should be handled by the backend or a separate processor
        console.warn('updateFormField is deprecated as checkpointProcessor is removed.')
      },

      // ============================================================================
      // 用户输入管理 - 只保留一套实现
      // =========================================================================
      setUserInput: (input: Partial<UserInput>) => {
        set(state => {
          Object.assign(state.userInput, input)
        })
      },

      updateUserInput: (content: string) => {
        set(state => {
          state.userInput.content = content
        })
      },

      setIsSubmitting: (isSubmitting: boolean) => {
        set(state => {
          state.userInput.isSubmitting = isSubmitting
        })
      },

      setIsTyping: (isTyping: boolean) => {
        set(state => {
          state.userInput.isTyping = isTyping
        })
      },

      clearUserInput: () => {
        set(state => {
          state.userInput.content = ''
          state.userInput.isSubmitting = false
          state.userInput.isTyping = false
        })
      },

      // ============================================================================
      // 实用工具
      // ============================================================================

      clearAllData: () => {
        set(state => {
          state.sessions = {}
          state.currentSession = undefined
          state.messageUIStates = {}
          state.userInput = {
            content: '',
            isSubmitting: false,
            isTyping: false,
          }
        })
      },

      getSessionMessages: (sessionId: string) => {
        const session = get().sessions[sessionId]
        return session ? [...session.messages] : []
      },

      getSessionStats: () => {
        const state = get()
        let totalMessages = 0
        let streamingMessages = 0
        let checkpointMessages = 0
        let reportMessages = 0
        let errorMessages = 0

        Object.values(state.sessions).forEach(session => {
          totalMessages += session.messages.length
          session.messages.forEach(message => {
            switch (message.payload.type) {
              case 'streaming':
                streamingMessages++
                break
              case 'checkpoint':
                checkpointMessages++
                break
              case 'report':
                reportMessages++
                break
              case 'error':
                errorMessages++
                break
            }
          })
        })

        return {
          totalSessions: Object.keys(state.sessions).length,
          totalMessages,
          streamingMessages,
          checkpointMessages,
          reportMessages,
          errorMessages,
        }
      },

      // ============================================================================
      // Mock模式管理
      // ============================================================================

      // Mock模式管理
      switchToMockMode: async (scenarioId?: string) => {
        const { connectionManager, cleanupFunctions } = get()

        // 先断开当前连接
        if (connectionManager) {
          await connectionManager.disconnect()
        }

        // 清理事件监听器
        if (cleanupFunctions) {
          cleanupFunctions.forEach(cleanup => cleanup())
        }

        // 创建Mock连接
        const mockConnection = createConnectionByType(ConnectionType.MOCK, {
          mockScenario: scenarioId || 'streaming-fast',
          enableScenarioSwitching: true,
        })

        // 设置会话信息给Mock连接
        const currentSession = get().currentSession
        const currentGroupId = get().currentGroupId
        if (currentSession && 'setSessionInfo' in mockConnection) {
          console.log('🎭 SWITCH_SESSION_INFO_SET:', {
            groupId: currentGroupId,
            sessionId: currentSession.sessionId,
          })
          ;(mockConnection as any).setSessionInfo({
            groupChatId: currentSession.groupChatId,
            sessionId: currentSession.sessionId,
            userId: currentSession.userId,
            organizationId: currentSession.organizationId,
          })
        } else {
          console.warn('⚠️ SWITCH_NO_SESSION_INFO:', {
            hasSession: !!currentSession,
            hasSetMethod: 'setSessionInfo' in mockConnection,
            currentGroupId,
          })
        }

        set(state => {
          state.connectionManager = mockConnection
          state.cleanupFunctions = []
        })

        // 重新设置事件监听
        const statusUnsubscribe = mockConnection.onStatus(status => {
          get().updateConnectionStatus(status)
        })

        const messageUnsubscribe = mockConnection.onMessage(message => {
          try {
            get().handleIncomingMessage(message)
          } catch (error) {
            console.error('Error in message handler:', error)
          }
        })

        set(state => {
          state.cleanupFunctions = [statusUnsubscribe, messageUnsubscribe]
        })

        // 连接Mock服务
        await mockConnection.connect()

        console.log('🎭 Switched to Mock mode')
      },

      switchToSocketMode: async () => {
        if (!canUseSocketIO()) {
          throw new Error('Socket.IO mode is not available (no URL configured or forced mock mode)')
        }

        const { connectionManager, cleanupFunctions } = get()

        // 先断开当前连接
        if (connectionManager) {
          await connectionManager.disconnect()
        }

        // 清理事件监听器
        if (cleanupFunctions) {
          cleanupFunctions.forEach(cleanup => cleanup())
        }

        // 创建Socket.IO连接
        const socketConnection = createConnectionByType(ConnectionType.SOCKET)

        set(state => {
          state.connectionManager = socketConnection
          state.cleanupFunctions = []
        })

        // 重新设置事件监听
        const statusUnsubscribe = socketConnection.onStatus(status => {
          get().updateConnectionStatus(status)
        })

        const messageUnsubscribe = socketConnection.onMessage(message => {
          try {
            get().handleIncomingMessage(message)
          } catch (error) {
            console.error('Error in message handler:', error)
          }
        })

        set(state => {
          state.cleanupFunctions = [statusUnsubscribe, messageUnsubscribe]
        })

        // 连接Socket.IO服务
        await socketConnection.connect()

        console.log('🌐 Switched to Socket.IO mode')
      },

      switchMockScenario: (scenarioId: string) => {
        const { connectionManager } = get()
        if (
          connectionManager?.getType() === ConnectionType.MOCK &&
          connectionManager.switchScenario
        ) {
          return connectionManager.switchScenario(scenarioId)
        }
        return false
      },

      getCurrentMockScenario: () => {
        const { connectionManager } = get()
        if (
          connectionManager?.getType() === ConnectionType.MOCK &&
          connectionManager.getCurrentScenario
        ) {
          return connectionManager.getCurrentScenario()
        }
        return null
      },

      getAvailableMockScenarios: () => {
        const { connectionManager } = get()
        if (
          connectionManager?.getType() === ConnectionType.MOCK &&
          connectionManager.getAvailableScenarios
        ) {
          return connectionManager.getAvailableScenarios()
        }
        return []
      },
    }))
  )
)

// 清理函数 - 在组件卸载时调用
export const cleanupChatStore = () => {
  const { disconnect, clearAllData } = useChatStore.getState()

  // 断开WebSocket连接
  disconnect()

  // 清理实时管理器 - 简化版本无需清理
  console.log('🗑️ Chat Store清理完成')

  // 可选：清理所有数据
  // clearAllData()
}

// ============================================================================
// 标准Selector Hooks - 基于BaseWebSocketMessage
// ============================================================================

/**
 * 连接状态管理Hook
 */
export const useChatConnection = () => {
  const connectionStatus = useChatStore(state => state.connectionStatus)
  const isConnecting = useChatStore(state => state.isConnecting)
  const lastError = useChatStore(state => state.lastError)
  const connect = useChatStore(state => state.connect)
  const disconnect = useChatStore(state => state.disconnect)
  const switchToMockMode = useChatStore(state => state.switchToMockMode)
  const switchToSocketMode = useChatStore(state => state.switchToSocketMode)
  const switchMockScenario = useChatStore(state => state.switchMockScenario)
  const getCurrentMockScenario = useChatStore(state => state.getCurrentMockScenario)
  const getAvailableMockScenarios = useChatStore(state => state.getAvailableMockScenarios)
  const connectionManager = useChatStore(state => state.connectionManager)

  // 获取当前连接类型
  const currentConnectionType = connectionManager?.getType()
  const isMockMode = currentConnectionType === ConnectionType.MOCK
  const isSocketMode = currentConnectionType === ConnectionType.SOCKET

  return {
    connectionStatus,
    isConnecting,
    lastError,
    connect,
    disconnect,
    // Mock模式管理
    switchToMockMode,
    switchToSocketMode,
    switchMockScenario,
    getCurrentMockScenario,
    getAvailableMockScenarios,
    // 连接管理器实例 - 用于直接调用方法
    connectionManager,
    // 状态信息
    currentConnectionType,
    isMockMode,
    isSocketMode,
    canUseSocketIO: canUseSocketIO(),
  }
}

/**
 * 会话管理Hook
 */
export const useChatSession = () => {
  const currentSession = useChatStore(state => state.currentSession)
  const sessions = useChatStore(state => state.sessions)
  const createSession = useChatStore(state => state.createSession)
  const setCurrentSession = useChatStore(state => state.setCurrentSession)
  const clearSession = useChatStore(state => state.clearSession)

  return {
    currentSession,
    sessions,
    createSession,
    setCurrentSession,
    clearSession,
  }
}

/**
 * 消息管理Hook - 基于BaseWebSocketMessage
 */
export const useChatMessages = () => {
  const messages = useChatStore(state => state.currentSession?.messages || [])
  const messageUIStates = useChatStore(state => state.messageUIStates)
  const addWebSocketMessage = useChatStore(state => state.addWebSocketMessage)
  const updateMessageUIState = useChatStore(state => state.updateMessageUIState)
  const deleteMessage = useChatStore(state => state.deleteMessage)
  const sendUserMessage = useChatStore(state => state.sendUserMessage)
  const getAccumulatedStreamingText = useChatStore(state => state.getAccumulatedStreamingText)

  // 兼容性方法
  const sendMessage = useChatStore(state => state.sendMessage)
  const addMessage = useChatStore(state => state.addMessage)

  return {
    // 标准BaseWebSocketMessage API
    messages,
    messageUIStates,
    addWebSocketMessage,
    updateMessageUIState,
    deleteMessage,
    sendUserMessage,
    getAccumulatedStreamingText,
    // 兼容性API（标记为deprecated）
    sendMessage,
    addMessage,
  }
}

/**
 * 用户输入管理Hook
 */
export const useChatInput = () => {
  const userInput = useChatStore(state => state.userInput)
  const updateUserInput = useChatStore(state => state.updateUserInput)
  const setIsSubmitting = useChatStore(state => state.setIsSubmitting)
  const setIsTyping = useChatStore(state => state.setIsTyping)
  const clearUserInput = useChatStore(state => state.clearUserInput)

  return {
    userInput,
    updateUserInput,
    setIsSubmitting,
    setIsTyping,
    clearUserInput,
  }
}

/**
 * 统计信息Hook
 */
export const useChatStats = () => useChatStore(state => state.getSessionStats())

/**
 * 消息UI状态Hook - 根据messageId获取UI状态
 */
export const useMessageUIState = (messageId: string) => {
  return useChatStore(state => state.messageUIStates[messageId])
}

/**
 * Streaming文本累积Hook - 实时获取累积文本
 */
export const useStreamingText = (sessionId: string, beforeTimestamp?: string) => {
  return useChatStore(state => {
    const session = state.sessions[sessionId]
    if (!session) return ''

    return accumulateStreamingText(session.messages, beforeTimestamp)
  })
}
