'use client'

import { ReactNode, useEffect, useState } from 'react'
import { usePathname, useRouter } from 'next/navigation'
import { useSession } from '@/components/providers/session-provider'
import { Button } from '@/components/ui/button'
import { AppSidebar } from '@/components/common/app-sidebar'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb'
import { Separator } from '@/components/ui/separator'
import { SidebarInset, SidebarProvider, SidebarTrigger } from '@/components/ui/sidebar'
import { Folder } from 'lucide-react'
import Image from 'next/image'
import logo from '@/assets/logo.svg'
import Loading from '@/app/loading'
import GlobalDetailPanel from '@/components/common/GlobalDetailPanel'
interface DashboardLayoutProps {
  children: ReactNode
}

interface BreadcrumbConfig {
  title: string
  href?: string | undefined
}

// 面包屑路由配置
const breadcrumbConfig: Record<string, BreadcrumbConfig[]> = {
  '/chat': [{ title: '首页', href: '' }, { title: '仪表板' }],
  '/page1': [
    { title: '首页', href: '' },
    { title: '仪表板', href: '/chat' },
    { title: '基础会话测试' },
  ],
  '/page2': [
    { title: '首页', href: '' },
    { title: '仪表板', href: '/chat' },
    { title: '高级会话管理' },
  ],
  '/users': [{ title: '首页', href: '' }, { title: '用户管理' }],
}

function generateBreadcrumbs(pathname: string): BreadcrumbConfig[] {
  // 优先使用配置的面包屑
  if (breadcrumbConfig[pathname]) {
    return breadcrumbConfig[pathname]
  }

  // 如果没有配置，根据路径自动生成
  const segments = pathname.split('login').filter(Boolean)
  const breadcrumbs: BreadcrumbConfig[] = [{ title: '首页', href: '' }]

  let currentPath = ''
  for (let i = 1; i < segments.length; i++) {
    // 跳过语言段
    currentPath += `/${segments[i]}`
    const fullPath = `/${segments[0]}${currentPath}`

    const isLast = i === segments.length - 1
    const title = segments[i].charAt(0).toUpperCase() + segments[i].slice(1)

    breadcrumbs.push({
      title,
      href: isLast ? undefined : fullPath,
    })
  }

  return breadcrumbs
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  const pathname = usePathname()
  const router = useRouter()
  const { loading, user, error, isAuthenticated } = useSession()

  // 报告相关状态
  const [isReportsListOpen, setIsReportsListOpen] = useState(false)
  const [isReportDetailOpen, setIsReportDetailOpen] = useState(false)
  const [selectedReport, setSelectedReport] = useState<Report | null>(null)

  // 检查是否在聊天页面
  const isChatPage = pathname?.startsWith('/chat')

  // 处理文件夹图标点击
  const handleFolderClick = () => {
    setIsReportsListOpen(!isReportsListOpen)
    if (isReportDetailOpen) {
      setIsReportDetailOpen(false)
      setSelectedReport(null)
    }
  }

  // 处理报告项点击
  const handleReportClick = (report: Report) => {
    setSelectedReport(report)
    setIsReportDetailOpen(true)
    setIsReportsListOpen(false)
  }

  // 处理详情面板关闭
  const handleDetailClose = () => {
    setIsReportDetailOpen(false)
    setSelectedReport(null)
  }

  // 预加载关键路由
  useEffect(() => {
    router.prefetch('/chat')
  }, [router])

  // 如果session正在加载，显示加载状态
  // 🎯 修复：使用useEffect处理认证失败的跳转，避免渲染期间的副作用
  useEffect(() => {
    if (!loading && (!isAuthenticated || !user || error)) {
      if (process.env.NODE_ENV === 'development') {
        console.log('🚫 用户未认证，跳转到登录页面:', { isAuthenticated, hasUser: !!user, error })
      }
      router.push('/login')
    }
  }, [isAuthenticated, user, error, loading, router])

  // 如果还在加载，显示加载组件
  if (loading) {
    return <Loading />
  }

  // 如果未认证或即将跳转，显示加载状态（避免闪烁）
  if (!isAuthenticated || !user || error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">{error ? '请重新登录...' : '正在验证身份...'}</p>
        </div>
      </div>
    )
  }

  // 用户已认证，显示dashboard布局
  return (
    <SidebarProvider
      style={
        {
          '--sidebar-width': '350px',
          '--sidebar-width-mobile': '350px',
        } as React.CSSProperties
      }
    >
      <AppSidebar />
      <SidebarInset className="h-screen flex flex-col">
        {/* Logo header with proper spacing */}
        <header className="w-full flex justify-around h-16 shrink-0 items-center gap-2 px-4 py-2 border-b border-gray-200 bg-white">
          <Image src={logo} alt="logo" width={200} height={100} className="h-12 w-auto" />

          {/* 文件夹图标 - 仅在聊天页面显示 */}
          {isChatPage && (
            <Button
              variant="ghost"
              size="icon"
              onClick={handleFolderClick}
              className="ml-2 h-10 w-10 text-gray-600 hover:text-gray-900 hover:bg-gray-100"
              title="查看报告列表"
            >
              <Folder className="w-5 h-5" />
            </Button>
          )}
        </header>

        {/* Main content area with reports panels - 固定高度确保面板在视口内 */}
        <div className="flex flex-1 overflow-hidden h-[calc(100vh-4rem)]">
          {/* 主内容区域 */}
          <div className="flex flex-1 flex-col gap-4 p-4 min-w-0">{children}</div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  )
}
