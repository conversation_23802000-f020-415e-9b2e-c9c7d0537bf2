/**
 * Better Auth 服务端配置
 * 用于处理认证逻辑和API路由
 */

import { betterAuth } from 'better-auth'
import { nextCookies } from 'better-auth/next-js'

export const auth = betterAuth({
  // 数据库配置（示例使用内存数据库，生产环境需要配置真实数据库）
  database: {
    provider: 'sqlite',
    url: ':memory:',
  },

  // 用户字段扩展
  user: {
    additionalFields: {
      language: {
        type: 'string',
        defaultValue: 'english',
        input: true, // 允许用户设置此字段
        required: false,
      },
    },
  },

  // 邮箱密码认证
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: false, // 开发环境暂时禁用邮箱验证
  },

  // 社交登录配置
  socialProviders: {
    google: {
      clientId: process.env.GOOGLE_CLIENT_ID || '',
      clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',
      enabled: false, // 暂时禁用，需要配置Google OAuth
    },
  },

  // Cookie 配置
  cookies: {
    ...nextCookies(),
  },

  // 高级配置
  advanced: {
    cookiePrefix: 'better-auth',
    crossSubDomainCookies:
      process.env.NODE_ENV === 'production'
        ? {
            enabled: true,
            domain: '.yourdomain.com',
          }
        : {
            enabled: true,
          },
  },
})
