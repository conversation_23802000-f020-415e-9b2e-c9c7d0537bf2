import { useSession } from '@/components/providers/session-provider'

/**
 * 简单的认证状态hook
 * 用于组件级的权限控制
 */
export function useAuth() {
  const { user, loading, error } = useSession()

  return {
    // 基础状态
    user,
    loading,
    error,

    // 便捷的认证状态
    isAuthenticated: !!user,

    // 基础角色检查
    isAdmin: user?.role === 'admin',
    isSuperAdmin: user?.role === 'super_admin',

    // 权限检查函数
    hasRole: (role: string) => user?.role === role,

    // 便捷的权限检查
    canEdit: user?.role === 'admin' || user?.role === 'super_admin',
    canDelete: user?.role === 'super_admin',

    // 用户信息
    userEmail: user?.email,
    userName: user?.name,
    userAvatar: user?.avatar,
  }
}
