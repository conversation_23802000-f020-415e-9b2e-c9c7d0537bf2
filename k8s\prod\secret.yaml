apiVersion: v1
kind: Secret
metadata:
  name: specific-ai-ui-secret
  namespace: ovs
  labels:
    app: specific-ai-ui
    app.kubernetes.io/name: specific-ai-ui
    app.kubernetes.io/component: service
    app.kubernetes.io/version: '1.0.0'
    app.kubernetes.io/managed-by: 'kubectl'
    environment: prod
type: Opaque
data:
  # 生产环境敏感数据 - 需要根据实际生产环境配置进行base64编码
  # 这里使用占位符，实际部署时需要替换为真实的生产环境配置
  DATABASE_URL: PROD_BASE64_ENCODED_DATABASE_URL
  BETTER_AUTH_SECRET: PROD_BASE64_ENCODED_BETTER_AUTH_SECRET
  RESEND_API_KEY: PROD_BASE64_ENCODED_RESEND_API_KEY
  GOOGLE_CLIENT_SECRET: PROD_BASE64_ENCODED_GOOGLE_CLIENT_SECRET