/**
 * 设计系统 - 基于Figma原型图的设计token
 * 确保与设计稿像素级一致
 */

// 🎨 颜色系统
export const colors = {
  // 消息气泡背景色
  message: {
    assistant: '#E7EEFE', // AI助手消息背景
    user: '#F7F7F7', // 用户消息背景
  },

  // 文本颜色
  text: {
    primary: '#252525', // 主要文本颜色
    timestamp: '#9CA3AF', // 时间戳颜色
  },

  // 头像背景色
  avatar: {
    user: '#DBEAFE', // 用户头像背景
  },
} as const

// 📐 尺寸系统
export const dimensions = {
  // 消息气泡
  bubble: {
    borderRadius: 26, // 圆角
    padding: {
      horizontal: 22, // 水平内边距
      vertical: 14, // 垂直内边距
    },
    maxWidth: {
      assistant: 714, // AI消息最大宽度
      user: 606, // 用户消息最大宽度
    },
  },

  // 头像
  avatar: {
    size: 32, // 头像尺寸
    borderRadius: 9999, // 圆形头像
  },

  // 间距
  spacing: {
    messageGap: 12, // 消息间距
    avatarGap: 12, // 头像与消息的间距
  },
} as const

// 🔤 字体系统
export const typography = {
  message: {
    fontFamily: 'Inter',
    fontSize: 16,
    fontWeight: 400,
    lineHeight: 1.5,
  },

  username: {
    fontFamily: 'Inter',
    fontSize: 16,
    fontWeight: 500,
    lineHeight: 1.5,
  },

  timestamp: {
    fontFamily: 'Inter',
    fontSize: 11,
    fontWeight: 400,
    lineHeight: 1.5,
  },
} as const

// 🎭 CSS变量映射
export const cssVars = {
  // 消息背景色
  '--message-bg-assistant': colors.message.assistant,
  '--message-bg-user': colors.message.user,

  // 文本颜色
  '--text-primary': colors.text.primary,
  '--text-timestamp': colors.text.timestamp,

  // 头像背景
  '--avatar-bg-user': colors.avatar.user,
} as const

// 🎯 Tailwind类名映射 - 基于Figma设计规范
export const tailwindClasses = {
  // 消息气泡基础样式 - 完全匹配Figma设计
  bubbleBase: [
    'rounded-[26px]',
    'font-normal',
    'text-base',
    'leading-6',
    'whitespace-pre-wrap',
    'break-words',
    'px-[22px]',
    'py-[14px]',
    'font-inter', // 确保使用Inter字体
  ].join(' '),

  // AI助手消息样式 - 精确匹配Figma
  assistantBubble: [
    'bg-[#E7EEFE]', // Figma中的精确颜色
    'text-[#252525]', // Figma中的精确文本颜色
    'max-w-[714px]', // Figma中的精确最大宽度
    'w-fit',
  ].join(' '),

  // 用户消息样式 - 精确匹配Figma
  userBubble: [
    'bg-[#F7F7F7]', // Figma中的精确颜色
    'text-[#252525]', // Figma中的精确文本颜色
    'max-w-[606px]', // Figma中的精确最大宽度
    'w-fit',
  ].join(' '),

  // 头像样式 - 精确匹配Figma (32x32px)
  avatar: ['w-8', 'h-8', 'rounded-full', 'flex-shrink-0'].join(' '),

  // 用户头像背景 - 精确匹配Figma颜色
  userAvatarBg: 'bg-[#DBEAFE]',

  // 用户名样式 - 精确匹配Figma (Inter 500 16px)
  username: [
    'text-base', // 16px
    'font-medium', // 500
    'text-[#252525]', // 精确颜色
    'leading-6', // 1.5行高
    'font-inter',
  ].join(' '),

  // 时间戳样式 - 精确匹配Figma (Inter 400 11px)
  timestamp: [
    'text-[11px]', // 精确的11px字体大小
    'font-normal', // 400
    'text-[#9CA3AF]', // 精确颜色
    'leading-6', // 1.5行高
    'font-inter',
  ].join(' '),
} as const

// 🔧 工具函数
export const getMessageBubbleClasses = (role: 'user' | 'assistant') => {
  const baseClasses = tailwindClasses.bubbleBase
  const roleClasses = role === 'user' ? tailwindClasses.userBubble : tailwindClasses.assistantBubble

  return `${baseClasses} ${roleClasses}`
}

export const getContainerClasses = (role: 'user' | 'assistant') => {
  return [
    'flex',
    'gap-3', // 保持12px间距 (gap-3 = 12px)
    'max-w-[85%]',
    'group',
    role === 'user' ? 'ml-auto flex-row-reverse' : 'mr-auto',
  ].join(' ')
}

// 📱 响应式断点
export const breakpoints = {
  mobile: '640px',
  tablet: '768px',
  desktop: '1024px',
} as const
