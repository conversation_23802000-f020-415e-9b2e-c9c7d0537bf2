"use client"

import { useEffect, useState } from "react"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { GlobalDetailPanel } from "@/components/common/GlobalDetailPanel"
import { NewsItem } from "@/types/news"

interface RightPanelProps {
  articleId: string | null
  isOpen: boolean
  onClose: () => void
  articles: NewsItem[]
}

export default function RightPanel({ articleId, isOpen, onClose, articles }: RightPanelProps) {
  const [lastSelectedArticleId, setLastSelectedArticleId] = useState<string | null>(null)

  // 当articleId变化时更新状态
  useEffect(() => {
    if (articleId) {
      setLastSelectedArticleId(articleId)
    }
  }, [articleId])

  // 使用最后选中的文章ID来显示内容，如果没有则使用当前选中的
  const effectiveArticleId = articleId || lastSelectedArticleId
  
  // 从传入的articles数组中查找选中的新闻
  const selectedArticle = effectiveArticleId 
    ? articles.find(article => article._id === effectiveArticleId)
    : null

  return (
    <GlobalDetailPanel
      title="原文详情"
      isOpen={isOpen}
      onClose={onClose}
      width={500}
    >
      {!selectedArticle ? (
        <div className="text-center text-gray-500 py-8">
          <p>请选择一条新闻查看详情</p>
        </div>
      ) : (
        <>
          {/* 文章头部信息 */}
          <div className="flex items-start mb-6 px-6 pt-6 overflow-hidden">
            <Avatar className="h-10 w-10 mr-4 flex-shrink-0">
              <AvatarFallback className="bg-blue-100 text-blue-600 text-sm font-medium">
                新
              </AvatarFallback>
            </Avatar>
            <div className="min-w-0 flex-1">
              <h2
                className="text-lg font-semibold text-gray-900 mb-2 leading-tight cursor-pointer hover:text-blue-600 transition-colors break-words word-wrap"
                onClick={() => {
                  if (selectedArticle.url) {
                    window.open(selectedArticle.url, '_blank', 'noopener,noreferrer')
                  }
                }}
                style={{
                  wordBreak: 'break-word',
                  overflowWrap: 'break-word',
                  hyphens: 'auto'
                }}
              >
                {selectedArticle.title}
              </h2>

              {/* 新闻类型标签 */}
              <div className="flex items-center gap-2 mb-2 flex-wrap">
                {selectedArticle.news_type && (
                  <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full whitespace-nowrap">
                    {selectedArticle.news_type}
                  </span>
                )}
                {selectedArticle.country && (
                  <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full whitespace-nowrap">
                    {selectedArticle.country}
                  </span>
                )}
              </div>

              <div
                className="text-sm text-gray-500 break-words"
                style={{
                  wordBreak: 'break-word',
                  overflowWrap: 'break-word'
                }}
              >
                {selectedArticle.web_site || selectedArticle.url}
              </div>
            </div>
          </div>
        </>
      )}
    </GlobalDetailPanel>
  )
}