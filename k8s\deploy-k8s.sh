#!/bin/bash

# 配置部分 - 根据项目修改
NAMESPACE="ovs"
APP_NAME="specific-ai-ui"
IMAGE_REGISTRY="192.168.50.112/specific-ai"
IMAGE_NAME="ui"
YOUR_PORT="8230"
YOUR_NODEPORT="30989"

# 默认启用强制更新和最新镜像拉取
FORCE_UPDATE=true
FORCE_LATEST=true  
PULL_FIRST=true

# 默认环境为开发环境
ENVIRONMENT="dev"
VERBOSE=false
DRY_RUN=false

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --env dev|prod       指定部署环境 (默认: dev)"
    echo "  --verbose            详细输出模式"
    echo "  --dry-run           干运行模式，显示将要执行的操作"
    echo "  --help              显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 --env dev         部署到开发环境"
    echo "  $0 --env prod        部署到生产环境"
    echo "  $0 --dry-run         查看将要执行的操作"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --env)
            ENVIRONMENT="$2"
            shift 2
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 验证环境参数
if [[ "$ENVIRONMENT" != "dev" && "$ENVIRONMENT" != "prod" ]]; then
    log_error "无效的环境参数: $ENVIRONMENT"
    log_error "支持的环境: dev, prod"
    exit 1
fi

# 设置环境相关的配置文件路径
CONFIG_DIR="${ENVIRONMENT}"
CONFIGMAP_FILE="${CONFIG_DIR}/configmap.yaml"
SECRET_FILE="${CONFIG_DIR}/secret.yaml"

# 完整镜像名
FULL_IMAGE_NAME="${IMAGE_REGISTRY}/${IMAGE_NAME}:latest"

log_info "=================================================="
log_info "部署 ${APP_NAME} 到 Kubernetes 集群"
log_info "环境: ${ENVIRONMENT}"
log_info "命名空间: ${NAMESPACE}"
log_info "镜像: ${FULL_IMAGE_NAME}"
log_info "端口: ${YOUR_PORT} (NodePort: ${YOUR_NODEPORT})"
log_info "=================================================="

# 检查kubectl是否可用
if ! command -v kubectl &> /dev/null; then
    log_error "kubectl 命令未找到，请先安装 kubectl"
    exit 1
fi

# 检查配置文件是否存在
if [[ ! -f "$CONFIGMAP_FILE" ]]; then
    log_error "配置文件不存在: $CONFIGMAP_FILE"
    exit 1
fi

if [[ ! -f "$SECRET_FILE" ]]; then
    log_error "密钥文件不存在: $SECRET_FILE"
    exit 1
fi

# 干运行模式
if [[ "$DRY_RUN" == "true" ]]; then
    log_info "=== 干运行模式 - 将要执行的操作 ==="
    echo "1. 创建命名空间: $NAMESPACE"
    echo "2. 应用配置文件: $CONFIGMAP_FILE"
    echo "3. 应用密钥文件: $SECRET_FILE" 
    echo "4. 部署应用: deployment.yaml"
    echo "5. 创建服务: service.yaml"
    echo "6. 验证部署状态"
    echo "7. 执行连通性测试"
    exit 0
fi

# 执行函数
execute_command() {
    local cmd="$1"
    local description="$2"
    
    if [[ "$VERBOSE" == "true" ]]; then
        log_info "执行: $cmd"
    fi
    
    log_info "$description"
    
    if eval "$cmd"; then
        log_success "$description - 完成"
        return 0
    else
        log_error "$description - 失败"
        return 1
    fi
}

# 创建命名空间（如果不存在）
log_info "检查并创建命名空间..."
kubectl get namespace $NAMESPACE &>/dev/null || kubectl create namespace $NAMESPACE

# 清理旧资源（如果存在）
if [[ "$FORCE_UPDATE" == "true" ]]; then
    log_info "清理旧资源..."
    kubectl delete deployment $APP_NAME -n $NAMESPACE --ignore-not-found=true
    kubectl delete configmap ${APP_NAME}-config -n $NAMESPACE --ignore-not-found=true
    kubectl delete secret ${APP_NAME}-secret -n $NAMESPACE --ignore-not-found=true
    sleep 2
fi

# 应用配置文件
execute_command "kubectl apply -f $CONFIGMAP_FILE" "应用配置文件"
execute_command "kubectl apply -f $SECRET_FILE" "应用密钥文件"

# 部署应用
execute_command "kubectl apply -f deployment.yaml" "部署应用"
execute_command "kubectl apply -f service.yaml" "创建服务"

# 等待部署就绪
log_info "等待部署就绪..."
if kubectl wait --for=condition=available --timeout=300s deployment/$APP_NAME -n $NAMESPACE; then
    log_success "部署就绪"
else
    log_error "部署超时，请检查Pod状态"
    kubectl get pods -n $NAMESPACE -l app=$APP_NAME
    exit 1
fi

# 获取Pod信息
POD_NAME=$(kubectl get pod -n $NAMESPACE -l app=$APP_NAME -o jsonpath='{.items[0].metadata.name}')
if [[ -z "$POD_NAME" ]]; then
    log_error "未找到运行中的Pod"
    exit 1
fi

log_success "Pod创建成功: $POD_NAME"

# 验证部署状态
log_info "验证部署状态..."
kubectl get pods -n $NAMESPACE -l app=$APP_NAME
kubectl get services -n $NAMESPACE -l app=$APP_NAME

# 连通性测试
log_info "执行连通性测试..."

# 1. Pod内部健康检查
log_info "1. Pod内部健康检查..."
if kubectl exec -n $NAMESPACE $POD_NAME -- wget -q --spider http://localhost:$YOUR_PORT/api/health; then
    log_success "Pod内部健康检查通过"
else
    log_warning "Pod内部健康检查失败，但部署可能仍然正常"
fi

# 2. ClusterIP服务访问测试
log_info "2. ClusterIP服务访问测试..."
if kubectl run test-pod --rm -i --restart=Never --image=busybox --timeout=30s -- wget -q --spider http://${APP_NAME}-service.${NAMESPACE}.svc.cluster.local:$YOUR_PORT/api/health 2>/dev/null; then
    log_success "ClusterIP服务访问测试通过"
else
    log_warning "ClusterIP服务访问测试失败"
fi

# 3. 获取NodePort访问信息
NODE_IP=$(kubectl get nodes -o jsonpath='{.items[0].status.addresses[?(@.type=="InternalIP")].address}')
if [[ -n "$NODE_IP" ]]; then
    log_info "3. NodePort外部访问信息..."
    log_info "外部访问地址: http://$NODE_IP:$YOUR_NODEPORT"
    log_info "健康检查地址: http://$NODE_IP:$YOUR_NODEPORT/api/health"
    
    # 尝试NodePort访问测试（可能因为网络配置而失败）
    if curl -s --connect-timeout 5 http://$NODE_IP:$YOUR_NODEPORT/api/health >/dev/null 2>&1; then
        log_success "NodePort外部访问测试通过"
    else
        log_warning "NodePort外部访问测试失败（可能需要防火墙配置）"
    fi
else
    log_warning "无法获取节点IP地址"
fi

# 显示最终状态
log_info "=================================================="
log_success "部署完成！"
log_info "环境: ${ENVIRONMENT}"
log_info "应用名称: $APP_NAME"
log_info "命名空间: $NAMESPACE"
log_info "镜像: $FULL_IMAGE_NAME"
if [[ -n "$NODE_IP" ]]; then
    log_info "访问地址: http://$NODE_IP:$YOUR_NODEPORT"
fi
log_info "=================================================="

# 显示有用的命令
log_info "常用运维命令:"
echo "  查看Pod状态: kubectl get pods -n $NAMESPACE -l app=$APP_NAME"
echo "  查看日志: kubectl logs -f -n $NAMESPACE -l app=$APP_NAME"
echo "  进入Pod: kubectl exec -it -n $NAMESPACE $POD_NAME -- /bin/sh"
echo "  重启部署: kubectl rollout restart deployment/$APP_NAME -n $NAMESPACE"

log_success "部署脚本执行完成"