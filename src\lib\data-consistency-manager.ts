/**
 * 数据一致性管理器
 * 
 * 功能：确保各个Store间数据同步，检测和修复数据不一致问题
 * 依赖：各种Store接口、BaseWebSocketMessage类型
 * 性能：轻量级同步机制，支持批量验证和自动修复
 * 
 * 数据流：Store Changes -> Consistency Manager -> Validation -> Auto Repair
 */

import type { BaseWebSocketMessage } from '@/types/websocket-event-type'
import type { ChatSession } from '@/stores/session-store'

// ============================================================================
// 一致性检查接口
// ============================================================================

export interface ConsistencyRule {
  name: string
  description: string
  check(context: ConsistencyContext): ConsistencyResult
  autoFix?: (context: ConsistencyContext, issue: ConsistencyIssue) => Promise<boolean>
  priority: 'high' | 'medium' | 'low'
  enabled: boolean
}

export interface ConsistencyContext {
  // Store数据快照
  sessions: Record<string, ChatSession>
  currentSession: ChatSession | undefined
  currentGroupId: string | undefined
  groupToSessionMap: Record<string, string>
  
  // 时间戳
  timestamp: Date
  
  // 元数据
  checkId: string
  triggeredBy: 'manual' | 'auto' | 'store_change' | 'periodic'
}

export interface ConsistencyResult {
  ruleName: string
  passed: boolean
  issues: ConsistencyIssue[]
  metadata?: Record<string, any>
  executionTime: number
}

export interface ConsistencyIssue {
  id: string
  type: 'data_mismatch' | 'missing_data' | 'invalid_state' | 'orphaned_data'
  severity: 'critical' | 'high' | 'medium' | 'low'
  description: string
  affectedEntities: string[]
  suggestedFix?: string
  canAutoFix: boolean
  metadata?: Record<string, any>
}

export interface ConsistencyReport {
  checkId: string
  timestamp: Date
  triggeredBy: string
  totalRules: number
  rulesChecked: number
  rulesPassed: number
  rulesFailed: number
  totalIssues: number
  criticalIssues: number
  highIssues: number
  autoFixedIssues: number
  executionTime: number
  results: ConsistencyResult[]
  summary: string
}

// ============================================================================
// 一致性管理器配置
// ============================================================================

export interface ConsistencyManagerConfig {
  // 检查配置
  enableAutoChecks: boolean
  autoCheckInterval: number
  checkOnStoreChanges: boolean
  batchCheckDelay: number
  
  // 修复配置
  enableAutoFix: boolean
  maxAutoFixAttempts: number
  autoFixDelay: number
  
  // 性能配置
  maxCheckTime: number
  enableMetrics: boolean
  debugMode: boolean
  
  // 报告配置
  maxReportHistory: number
  enableReportLogging: boolean
}

export const DEFAULT_CONSISTENCY_CONFIG: ConsistencyManagerConfig = {
  enableAutoChecks: true,
  autoCheckInterval: 30000, // 30秒
  checkOnStoreChanges: true,
  batchCheckDelay: 1000,
  enableAutoFix: true,
  maxAutoFixAttempts: 3,
  autoFixDelay: 500,
  maxCheckTime: 5000,
  enableMetrics: true,
  debugMode: process.env.NODE_ENV === 'development',
  maxReportHistory: 50,
  enableReportLogging: true,
}

// ============================================================================
// 内置一致性规则
// ============================================================================

/**
 * 会话映射一致性规则
 */
export class SessionMappingConsistencyRule implements ConsistencyRule {
  name = 'session_mapping_consistency'
  description = '检查群聊到会话映射的一致性'
  priority: 'high' = 'high'
  enabled = true

  check(context: ConsistencyContext): ConsistencyResult {
    const startTime = Date.now()
    const issues: ConsistencyIssue[] = []

    // 检查映射中的会话是否存在
    for (const [groupId, sessionId] of Object.entries(context.groupToSessionMap)) {
      if (!context.sessions[sessionId]) {
        issues.push({
          id: `missing_session_${sessionId}`,
          type: 'missing_data',
          severity: 'high',
          description: `群聊 ${groupId} 映射的会话 ${sessionId} 不存在`,
          affectedEntities: [groupId, sessionId],
          suggestedFix: '从映射中移除无效的会话引用',
          canAutoFix: true,
          metadata: { groupId, sessionId },
        })
      }
    }

    // 检查会话的群聊映射是否正确
    for (const [sessionId, session] of Object.entries(context.sessions)) {
      const mappedSessionId = context.groupToSessionMap[session.groupChatId]
      if (mappedSessionId && mappedSessionId !== sessionId) {
        issues.push({
          id: `mapping_mismatch_${sessionId}`,
          type: 'data_mismatch',
          severity: 'medium',
          description: `会话 ${sessionId} 的群聊映射不一致`,
          affectedEntities: [sessionId, session.groupChatId],
          suggestedFix: '更新群聊到会话的映射关系',
          canAutoFix: true,
          metadata: { sessionId, groupChatId: session.groupChatId, mappedSessionId },
        })
      }
    }

    return {
      ruleName: this.name,
      passed: issues.length === 0,
      issues,
      metadata: {
        totalMappings: Object.keys(context.groupToSessionMap).length,
        totalSessions: Object.keys(context.sessions).length,
      },
      executionTime: Date.now() - startTime,
    }
  }

  async autoFix(context: ConsistencyContext, issue: ConsistencyIssue): Promise<boolean> {
    try {
      switch (issue.type) {
        case 'missing_data':
          // 移除无效的映射
          if (issue.metadata?.groupId && issue.metadata?.sessionId) {
            // 这里需要调用store的方法来修复
            console.log(`🔧 Auto-fixing: Removing invalid mapping ${issue.metadata.groupId} -> ${issue.metadata.sessionId}`)
            return true
          }
          break
          
        case 'data_mismatch':
          // 更新映射关系
          if (issue.metadata?.sessionId && issue.metadata?.groupChatId) {
            console.log(`🔧 Auto-fixing: Updating mapping ${issue.metadata.groupChatId} -> ${issue.metadata.sessionId}`)
            return true
          }
          break
      }
      return false
    } catch (error) {
      console.error('❌ Auto-fix failed:', error)
      return false
    }
  }
}

/**
 * 当前会话一致性规则
 */
export class CurrentSessionConsistencyRule implements ConsistencyRule {
  name = 'current_session_consistency'
  description = '检查当前会话状态的一致性'
  priority: 'high' = 'high'
  enabled = true

  check(context: ConsistencyContext): ConsistencyResult {
    const startTime = Date.now()
    const issues: ConsistencyIssue[] = []

    // 检查当前会话是否存在于会话列表中
    if (context.currentSession && !context.sessions[context.currentSession.sessionId]) {
      issues.push({
        id: `orphaned_current_session`,
        type: 'orphaned_data',
        severity: 'critical',
        description: '当前会话不存在于会话列表中',
        affectedEntities: [context.currentSession.sessionId],
        suggestedFix: '重新设置当前会话或将其添加到会话列表',
        canAutoFix: true,
        metadata: { sessionId: context.currentSession.sessionId },
      })
    }

    // 检查当前群聊与当前会话的一致性
    if (context.currentSession && context.currentGroupId) {
      if (context.currentSession.groupChatId !== context.currentGroupId) {
        issues.push({
          id: `current_session_group_mismatch`,
          type: 'data_mismatch',
          severity: 'high',
          description: '当前会话的群聊ID与当前群聊ID不匹配',
          affectedEntities: [context.currentSession.sessionId, context.currentGroupId],
          suggestedFix: '同步当前会话和当前群聊的状态',
          canAutoFix: true,
          metadata: {
            sessionGroupId: context.currentSession.groupChatId,
            currentGroupId: context.currentGroupId,
          },
        })
      }
    }

    // 检查当前群聊的映射是否指向当前会话
    if (context.currentGroupId && context.currentSession) {
      const mappedSessionId = context.groupToSessionMap[context.currentGroupId]
      if (mappedSessionId !== context.currentSession.sessionId) {
        issues.push({
          id: `current_group_mapping_mismatch`,
          type: 'data_mismatch',
          severity: 'medium',
          description: '当前群聊的会话映射与当前会话不匹配',
          affectedEntities: [context.currentGroupId, context.currentSession.sessionId],
          suggestedFix: '更新群聊映射或当前会话状态',
          canAutoFix: true,
          metadata: {
            currentGroupId: context.currentGroupId,
            currentSessionId: context.currentSession.sessionId,
            mappedSessionId,
          },
        })
      }
    }

    return {
      ruleName: this.name,
      passed: issues.length === 0,
      issues,
      metadata: {
        hasCurrentSession: !!context.currentSession,
        hasCurrentGroup: !!context.currentGroupId,
        currentSessionId: context.currentSession?.sessionId,
        currentGroupId: context.currentGroupId,
      },
      executionTime: Date.now() - startTime,
    }
  }

  async autoFix(context: ConsistencyContext, issue: ConsistencyIssue): Promise<boolean> {
    try {
      console.log(`🔧 Auto-fixing current session consistency issue: ${issue.description}`)
      // 这里需要实际的修复逻辑，调用相应的store方法
      return true
    } catch (error) {
      console.error('❌ Auto-fix failed:', error)
      return false
    }
  }
}

/**
 * 消息数据完整性规则
 */
export class MessageDataIntegrityRule implements ConsistencyRule {
  name = 'message_data_integrity'
  description = '检查消息数据的完整性'
  priority: 'medium' = 'medium'
  enabled = true

  check(context: ConsistencyContext): ConsistencyResult {
    const startTime = Date.now()
    const issues: ConsistencyIssue[] = []

    // 检查每个会话的消息数据
    for (const [sessionId, session] of Object.entries(context.sessions)) {
      // 检查消息ID的唯一性
      const messageIds = new Set<string>()
      const duplicateIds: string[] = []

      for (const message of session.messages) {
        if (messageIds.has(message.id)) {
          duplicateIds.push(message.id)
        } else {
          messageIds.add(message.id)
        }

        // 检查消息的必需字段
        if (!message.payload || !message.timestamp) {
          issues.push({
            id: `invalid_message_${message.id}`,
            type: 'invalid_state',
            severity: 'medium',
            description: `消息 ${message.id} 缺少必需字段`,
            affectedEntities: [sessionId, message.id],
            suggestedFix: '修复或移除无效的消息',
            canAutoFix: false,
            metadata: { sessionId, messageId: message.id },
          })
        }
      }

      // 报告重复的消息ID
      if (duplicateIds.length > 0) {
        issues.push({
          id: `duplicate_message_ids_${sessionId}`,
          type: 'data_mismatch',
          severity: 'high',
          description: `会话 ${sessionId} 存在重复的消息ID`,
          affectedEntities: [sessionId, ...duplicateIds],
          suggestedFix: '移除重复的消息或重新生成ID',
          canAutoFix: true,
          metadata: { sessionId, duplicateIds },
        })
      }
    }

    return {
      ruleName: this.name,
      passed: issues.length === 0,
      issues,
      metadata: {
        totalSessions: Object.keys(context.sessions).length,
        totalMessages: Object.values(context.sessions).reduce((sum, session) => sum + session.messages.length, 0),
      },
      executionTime: Date.now() - startTime,
    }
  }

  async autoFix(context: ConsistencyContext, issue: ConsistencyIssue): Promise<boolean> {
    try {
      if (issue.type === 'data_mismatch' && issue.metadata?.duplicateIds) {
        console.log(`🔧 Auto-fixing duplicate message IDs in session ${issue.metadata.sessionId}`)
        // 这里需要实际的修复逻辑
        return true
      }
      return false
    } catch (error) {
      console.error('❌ Auto-fix failed:', error)
      return false
    }
  }
}

// ============================================================================
// 数据一致性管理器主类
// ============================================================================

export class DataConsistencyManager {
  private config: ConsistencyManagerConfig
  private rules: ConsistencyRule[] = []
  private reports: ConsistencyReport[] = []
  private isChecking = false
  private autoCheckTimer: NodeJS.Timeout | null = null
  private pendingCheck: NodeJS.Timeout | null = null

  constructor(config: Partial<ConsistencyManagerConfig> = {}) {
    this.config = { ...DEFAULT_CONSISTENCY_CONFIG, ...config }
    this.initializeRules()
    
    if (this.config.enableAutoChecks) {
      this.startAutoChecks()
    }
    
    if (this.config.debugMode) {
      console.log('🔍 DataConsistencyManager initialized with config:', this.config)
    }
  }

  private initializeRules(): void {
    this.rules = [
      new SessionMappingConsistencyRule(),
      new CurrentSessionConsistencyRule(),
      new MessageDataIntegrityRule(),
    ]
    
    if (this.config.debugMode) {
      console.log('📋 Registered consistency rules:', this.rules.map(r => r.name))
    }
  }

  /**
   * 执行完整的一致性检查
   */
  async performConsistencyCheck(
    context: ConsistencyContext,
    rulesToCheck?: string[]
  ): Promise<ConsistencyReport> {
    if (this.isChecking) {
      throw new Error('Consistency check already in progress')
    }

    this.isChecking = true
    const startTime = Date.now()
    
    try {
      const checkId = `check-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
      const contextWithId = { ...context, checkId }
      
      if (this.config.debugMode) {
        console.log('🔍 Starting consistency check:', {
          checkId,
          triggeredBy: context.triggeredBy,
          totalRules: this.rules.length,
        })
      }

      // 执行规则检查
      const activeRules = this.rules.filter(rule => {
        return rule.enabled && (!rulesToCheck || rulesToCheck.includes(rule.name))
      })

      const results = await Promise.all(
        activeRules.map(async (rule) => {
          try {
            return await Promise.race([
              Promise.resolve(rule.check(contextWithId)),
              new Promise<ConsistencyResult>((_, reject) =>
                setTimeout(() => reject(new Error('Rule check timeout')), this.config.maxCheckTime)
              ),
            ])
          } catch (error) {
            const err = error instanceof Error ? error : new Error('Unknown rule error')
            return {
              ruleName: rule.name,
              passed: false,
              issues: [{
                id: `rule_error_${rule.name}`,
                type: 'invalid_state' as const,
                severity: 'high' as const,
                description: `规则检查失败: ${err.message}`,
                affectedEntities: [rule.name],
                canAutoFix: false,
              }],
              executionTime: this.config.maxCheckTime,
            }
          }
        })
      )

      // 执行自动修复
      let autoFixedIssues = 0
      if (this.config.enableAutoFix) {
        autoFixedIssues = await this.performAutoFix(contextWithId, results)
      }

      // 生成报告
      const report = this.generateReport(
        checkId,
        contextWithId.triggeredBy,
        results,
        autoFixedIssues,
        Date.now() - startTime
      )

      // 保存报告
      this.addReport(report)

      if (this.config.debugMode) {
        console.log('✅ Consistency check completed:', {
          checkId,
          passed: report.rulesPassed,
          failed: report.rulesFailed,
          issues: report.totalIssues,
          autoFixed: report.autoFixedIssues,
          executionTime: `${report.executionTime}ms`,
        })
      }

      return report
    } finally {
      this.isChecking = false
    }
  }

  /**
   * 执行自动修复
   */
  private async performAutoFix(
    context: ConsistencyContext,
    results: ConsistencyResult[]
  ): Promise<number> {
    let fixedCount = 0
    
    for (const result of results) {
      if (result.passed) continue

      const rule = this.rules.find(r => r.name === result.ruleName)
      if (!rule || !rule.autoFix) continue

      for (const issue of result.issues) {
        if (!issue.canAutoFix) continue

        try {
          if (this.config.debugMode) {
            console.log('🔧 Attempting auto-fix for issue:', issue.id)
          }

          const fixed = await rule.autoFix(context, issue)
          if (fixed) {
            fixedCount++
            if (this.config.debugMode) {
              console.log('✅ Auto-fix successful for issue:', issue.id)
            }
          }
        } catch (error) {
          if (this.config.debugMode) {
            console.error('❌ Auto-fix failed for issue:', issue.id, error)
          }
        }

        // 在修复之间添加延迟
        if (this.config.autoFixDelay > 0) {
          await new Promise(resolve => setTimeout(resolve, this.config.autoFixDelay))
        }
      }
    }

    return fixedCount
  }

  /**
   * 生成一致性检查报告
   */
  private generateReport(
    checkId: string,
    triggeredBy: string,
    results: ConsistencyResult[],
    autoFixedIssues: number,
    executionTime: number
  ): ConsistencyReport {
    const totalIssues = results.reduce((sum, result) => sum + result.issues.length, 0)
    const criticalIssues = results.reduce(
      (sum, result) => sum + result.issues.filter(issue => issue.severity === 'critical').length,
      0
    )
    const highIssues = results.reduce(
      (sum, result) => sum + result.issues.filter(issue => issue.severity === 'high').length,
      0
    )
    
    const rulesPassed = results.filter(result => result.passed).length
    const rulesFailed = results.length - rulesPassed

    let summary = `检查了 ${results.length} 个规则`
    if (totalIssues === 0) {
      summary += '，数据一致性良好'
    } else {
      summary += `，发现 ${totalIssues} 个问题`
      if (autoFixedIssues > 0) {
        summary += `，自动修复了 ${autoFixedIssues} 个`
      }
    }

    return {
      checkId,
      timestamp: new Date(),
      triggeredBy,
      totalRules: this.rules.length,
      rulesChecked: results.length,
      rulesPassed,
      rulesFailed,
      totalIssues,
      criticalIssues,
      highIssues,
      autoFixedIssues,
      executionTime,
      results,
      summary,
    }
  }

  /**
   * 添加报告到历史记录
   */
  private addReport(report: ConsistencyReport): void {
    this.reports.unshift(report)
    
    // 限制报告历史记录数量
    if (this.reports.length > this.config.maxReportHistory) {
      this.reports = this.reports.slice(0, this.config.maxReportHistory)
    }

    // 记录报告日志
    if (this.config.enableReportLogging) {
      if (report.criticalIssues > 0) {
        console.error('🚨 Critical consistency issues found:', report.summary)
      } else if (report.highIssues > 0) {
        console.warn('⚠️ High priority consistency issues found:', report.summary)
      } else if (report.totalIssues > 0) {
        console.log('ℹ️ Consistency issues found:', report.summary)
      } else if (this.config.debugMode) {
        console.log('✅ Consistency check passed:', report.summary)
      }
    }
  }

  /**
   * 启动自动检查
   */
  private startAutoChecks(): void {
    if (this.autoCheckTimer) {
      return
    }

    this.autoCheckTimer = setInterval(() => {
      // 这里需要获取当前的store状态来执行检查
      // 由于我们不直接依赖store，这个方法需要外部调用
      if (this.config.debugMode) {
        console.log('⏰ Auto consistency check scheduled')
      }
    }, this.config.autoCheckInterval)

    if (this.config.debugMode) {
      console.log('⏰ Auto consistency checks started')
    }
  }

  /**
   * 停止自动检查
   */
  private stopAutoChecks(): void {
    if (this.autoCheckTimer) {
      clearInterval(this.autoCheckTimer)
      this.autoCheckTimer = null
      
      if (this.config.debugMode) {
        console.log('⏰ Auto consistency checks stopped')
      }
    }
  }

  /**
   * 触发延迟检查（用于store变更时）
   */
  triggerDelayedCheck(context: ConsistencyContext): void {
    if (!this.config.checkOnStoreChanges) {
      return
    }

    // 清除之前的延迟检查
    if (this.pendingCheck) {
      clearTimeout(this.pendingCheck)
    }

    // 设置新的延迟检查
    this.pendingCheck = setTimeout(() => {
      this.performConsistencyCheck({
        ...context,
        triggeredBy: 'store_change',
      }).catch(error => {
        console.error('❌ Delayed consistency check failed:', error)
      })
      this.pendingCheck = null
    }, this.config.batchCheckDelay)
  }

  /**
   * 获取最新的报告
   */
  getLatestReport(): ConsistencyReport | null {
    return this.reports[0] || null
  }

  /**
   * 获取所有报告
   */
  getAllReports(): ConsistencyReport[] {
    return [...this.reports]
  }

  /**
   * 获取指标统计
   */
  getMetrics() {
    const recentReports = this.reports.slice(0, 10)
    const totalChecks = recentReports.length
    const checksWithIssues = recentReports.filter(r => r.totalIssues > 0).length
    const avgExecutionTime = totalChecks > 0 
      ? recentReports.reduce((sum, r) => sum + r.executionTime, 0) / totalChecks 
      : 0

    return {
      totalRules: this.rules.length,
      activeRules: this.rules.filter(r => r.enabled).length,
      totalChecks,
      checksWithIssues,
      successRate: totalChecks > 0 ? (totalChecks - checksWithIssues) / totalChecks : 1,
      avgExecutionTime,
      isAutoCheckEnabled: !!this.autoCheckTimer,
      reportHistorySize: this.reports.length,
    }
  }

  /**
   * 重置管理器
   */
  reset(): void {
    this.stopAutoChecks()
    this.reports = []
    this.isChecking = false
    
    if (this.pendingCheck) {
      clearTimeout(this.pendingCheck)
      this.pendingCheck = null
    }

    if (this.config.debugMode) {
      console.log('🔄 DataConsistencyManager reset')
    }
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    this.reset()
    this.rules = []
    
    if (this.config.debugMode) {
      console.log('🗑️ DataConsistencyManager destroyed')
    }
  }
}

// ============================================================================
// 全局实例
// ============================================================================

let globalConsistencyManager: DataConsistencyManager | null = null

export const getGlobalConsistencyManager = (config?: Partial<ConsistencyManagerConfig>): DataConsistencyManager => {
  if (!globalConsistencyManager) {
    globalConsistencyManager = new DataConsistencyManager(config)
  }
  return globalConsistencyManager
}

export const resetGlobalConsistencyManager = (): void => {
  if (globalConsistencyManager) {
    globalConsistencyManager.destroy()
    globalConsistencyManager = null
  }
}

// ============================================================================
// 便捷函数
// ============================================================================

/**
 * 快速执行一致性检查
 */
export const checkDataConsistency = async (
  context: ConsistencyContext,
  config?: Partial<ConsistencyManagerConfig>
): Promise<ConsistencyReport> => {
  const manager = getGlobalConsistencyManager(config)
  return manager.performConsistencyCheck(context)
}