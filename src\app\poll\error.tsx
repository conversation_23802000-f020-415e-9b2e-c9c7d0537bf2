'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { AlertTriangle, RefreshCw, ArrowLeft } from 'lucide-react'
import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { handleError } from '@/lib/error-handler'

/**
 * Poll 页面专用错误边界
 * 针对问卷页面的错误场景进行优化
 */
export default function PollError({
  error,
  reset,
}: {
  error: (Error & { digest?: string }) | null
  reset: () => void
}) {
  // const t = useTranslations('poll')
  // const tCommon = useTranslations('common')
  const router = useRouter()
  const [isRetrying, setIsRetrying] = useState(false)

  useEffect(() => {
    if (error) {
      // 使用统一错误处理系统记录错误
      handleError(error, {
        showToast: false, // 错误页面不显示 toast
        logError: true,
        customMessage: '问卷页面发生错误',
      })
    }
  }, [error])

  // 重试处理
  const handleRetry = async () => {
    setIsRetrying(true)
    try {
      reset()
    } catch (retryError) {
      handleError(retryError as Error, {
        showToast: true,
        customMessage: '重试失败，请稍后再试',
      })
    } finally {
      setIsRetrying(false)
    }
  }

  // 返回登录页面
  const handleGoToLogin = () => {
    router.push('/login')
  }

  // 返回上一页
  const handleGoBack = () => {
    router.back()
  }

  return (
    <div className="h-screen overflow-hidden bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto w-12 h-12 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mb-4">
            <AlertTriangle className="w-6 h-6 text-red-600 dark:text-red-400" />
          </div>
          <CardTitle className="text-xl font-bold text-gray-900 dark:text-white">
            问卷页面出现错误
          </CardTitle>
          <CardDescription className="text-gray-600 dark:text-gray-300">
            很抱歉，问卷页面遇到了一些问题
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 错误详情 */}
          {error && (
            <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-lg">
              <p className="text-sm font-medium text-gray-900 dark:text-white mb-1">错误详情</p>
              <p className="text-xs text-gray-600 dark:text-gray-400 font-mono break-all">
                {error.message || '未知错误'}
              </p>
            </div>
          )}

          {/* 建议解决方案 */}
          <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
            <p className="text-sm font-medium text-blue-900 dark:text-blue-300 mb-2">
              建议的解决方案：
            </p>
            <ul className="text-xs text-blue-800 dark:text-blue-400 space-y-1">
              <li>• 检查网络连接是否正常</li>
              <li>• 刷新页面重新加载问卷</li>
              <li>• 清除浏览器缓存后重试</li>
              <li>• 如果问题持续存在，请联系客服</li>
            </ul>
          </div>

          {/* 操作按钮 */}
          <div className="flex flex-col space-y-2">
            {/* 重试按钮 */}
            <Button
              onClick={handleRetry}
              className="w-full"
              variant="default"
              disabled={isRetrying}
            >
              <RefreshCw className={`mr-2 h-4 w-4 ${isRetrying ? 'animate-spin' : ''}`} />
              {isRetrying ? '重试中...' : '重新加载问卷'}
            </Button>

            {/* 返回按钮 */}
            <Button onClick={handleGoBack} className="w-full" variant="outline">
              <ArrowLeft className="mr-2 h-4 w-4" />
              返回上一页
            </Button>

            {/* 回到登录页 */}
            <Button onClick={handleGoToLogin} className="w-full" variant="ghost">
              回到登录页
            </Button>
          </div>

          {/* 帮助信息 */}
          <div className="text-center">
            <p className="text-xs text-gray-500 dark:text-gray-400">
              如果问题持续存在，您可以尝试：
            </p>
            <div className="mt-2 space-y-1">
              <p className="text-xs text-gray-500 dark:text-gray-400">1. 稍后重新访问问卷页面</p>
              <p className="text-xs text-gray-500 dark:text-gray-400">2. 联系客服获得帮助</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
