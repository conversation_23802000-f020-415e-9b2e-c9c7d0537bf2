{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "delete": "Delete", "edit": "Edit", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "close": "Close", "search": "Search", "filter": "Filter", "refresh": "Refresh", "retry": "Retry"}, "auth": {"login": {"title": "Welcome Back", "subtitle": "Sign in to your Vibe Coding account", "username": "Username or Email", "usernamePlaceholder": "Enter username or email", "password": "Password", "passwordPlaceholder": "Enter password", "forgotPassword": "Forgot password?", "loginButton": "<PERSON><PERSON>", "loggingIn": "Logging in...", "orLoginWith": "Or login with", "loginWithApple": "Login with Apple", "loginWithGoogle": "Login with Google", "loginWithFacebook": "Login with Facebook", "noAccount": "Don't have an account?", "registerNow": "Register now", "brandTitle": "Vibe Coding Refactory", "brandSubtitle": "Enterprise AI Application Frontend Architecture", "feature1": "✨ Complete Vue3 to Next.js Migration", "feature2": "🚀 Modern Component System", "feature3": "🔐 Complete Authentication & Permission Management", "feature4": "🎨 Enterprise UI Design System", "termsText": "By continuing, you agree to our", "termsOfService": "Terms of Service", "and": "and", "privacyPolicy": "Privacy Policy", "period": "."}, "register": {"title": "Create Account", "subtitle": "Sign up for your new account", "name": "Name", "namePlaceholder": "Enter your name", "email": "Email", "emailPlaceholder": "Enter your email address", "password": "Password", "passwordPlaceholder": "Enter your password (at least 6 characters)", "companyName": "Company Name", "companyNamePlaceholder": "Enter your company name", "inviteCode": "Invite Code", "inviteCodePlaceholder": "Enter invite code", "createAccountButton": "Create Account", "creatingAccount": "Creating Account...", "accountCreated": "Account Created!", "alreadyHaveAccount": "Already have an account?", "signIn": "Sign in now", "or": "Or", "registerSuccess": "Registration successful! Redirecting to homepage...", "registering": "Registering..."}, "validation": {"usernameRequired": "Please enter username or email", "passwordRequired": "Please enter password", "emailFormat": "Please enter a valid email address", "passwordLength": "Password must be at least 6 characters", "nameLength": "Name must be at least 2 characters", "companyNameLength": "Company name must be at least 2 characters", "inviteCodeLength": "Invite code format is incorrect", "loginError": "An error occurred during login, please try again later", "allFieldsRequired": "Please fill in all required fields", "passwordMismatch": "Passwords do not match", "registerError": "Registration failed, please check network connection or contact administrator"}, "messages": {"loginSuccess": "Login successful", "welcomeBack": "Welcome back, {username}!", "loginFailed": "<PERSON><PERSON> failed", "loginError": "An error occurred during login", "featureInDevelopment": "Feature in Development", "forgotPasswordInDevelopment": "Forgot password feature is under development", "registerInDevelopment": "Registration feature is under development"}, "legacy": {"login": "<PERSON><PERSON>", "logout": "Logout", "pleaseLogin": "Please login first", "invalidCredentials": "Invalid username or password", "accountLocked": "Account has been locked, please contact administrator", "userDeleted": "This user has been deleted, please contact administrator"}}, "navigation": {"chat": "Cha<PERSON>", "uiGallery": "UI Gallery", "home": "Home", "dashboard": "Dashboard", "settings": "Settings"}, "chat": {"title": "Chat Interface", "subtitle": "Coming soon...", "welcome": "Welcome to SpecificAI!", "description": "Your AI-powered assistant is ready to help.", "placeholder": "Type a message...", "send": "Send", "clear": "Clear chat", "typing": "Typing...", "security": {"error": {"noUser": "User information not found, please login again", "noOrganization": "Organization information not found, please contact administrator", "invalidGroupId": "Invalid chat group ID, please refresh and try again", "unknown": "Security validation failed, unknown error"}}, "status": {"loading": "Loading...", "validating": "Validating permissions...", "connecting": "Connecting..."}, "error": {"title": "An Error Occurred", "reload": "Reload", "initializationFailed": "Chat initialization failed: {error}", "unknown": "Unknown error", "reconnect": "Reconnect", "boundary": {"connection": {"title": "Network Connection Error", "message": "Chat service is temporarily unavailable. Please check your network connection and try again."}, "security": {"title": "Access Permission Error", "message": "You don't have permission to access this chat. Please log in again."}, "render": {"title": "Interface Display Error", "message": "Chat interface encountered a display issue. Attempting to recover."}, "unknown": {"title": "System Error", "message": "Chat system encountered an unknown error. Please try again later."}, "technicalDetails": "Technical Details", "retry": {"connection": "Reconnect", "general": "Retry"}, "reset": "Reset", "refresh": "Refresh Page", "maxRetriesReached": "Maximum retry attempts reached. Please refresh the page or contact support."}}, "interface": {"placeholder": {"group": "Type a message in group {groupId}...", "default": "Type a message to test the intelligent connection system..."}}}, "ui": {"gallery": {"title": "UI Component Gallery", "description": "Test and debug all UI components for style verification", "buttons": "Button Components", "inputs": "Input Components", "cards": "Card Components", "markdown": "Markdown Renderer Demo"}}, "uiGallery": {"title": "UI Component Gallery", "subtitle": "Showcase of available components in the project", "radixComponents": "Radix UI Components", "customComponents": "Custom Components", "componentAvailable": "Component Available"}, "errors": {"unauthorized": "Unauthorized, please login again", "noPermission": "Insufficient permissions to access", "userNotLogin": "User not logged in, please login first", "userDeleted": "This user has been deleted, please contact administrator", "networkError": "Network connection failed, please check network settings", "serverError": "Internal server error", "notFound": "Requested resource not found", "badRequest": "Bad request parameters", "forbidden": "Insufficient permissions", "timeout": "Request timeout", "unknown": "Unknown error"}, "language": {"switch": "Switch Language", "chinese": "中文", "english": "English", "japanese": "日本語", "current": "Current Language"}, "user": {"profile": "Profile", "account": "Account", "preferences": "Preferences", "avatar": "Avatar", "name": "Name", "email": "Email", "role": "Role", "lastLogin": "Last Login", "memberSince": "Member Since"}, "poll": {"title": "Tell Us About Your Company", "subtitle": "Please provide your company information so we can better serve you", "placeholder": "Please describe your company situation or select tags above...", "start": "Start", "step": "Step {step} of {total}", "loading": "Loading survey questions...", "steps": {"country": {"title": "Please select your country/region", "options": {"vietnam": "Vietnam", "japan": "Japan", "korea": "Korea", "eastAsia": "East Asian Countries", "southeastAsia": "Southeast Asia Region"}}, "industry": {"title": "Please select your industry type", "options": {"technology": "Technology/Internet", "manufacturing": "Manufacturing", "finance": "Financial Services", "retail": "Retail/E-commerce", "other": "Other Industries"}}, "focus": {"title": "Please select your areas of focus", "options": {"aiAutomation": "AI Automation", "digitalTransformation": "Digital Transformation", "dataAnalytics": "Data Analytics", "processOptimization": "Process Optimization", "other": "Other Areas"}}, "companyInfo": {"title": "Please describe your company", "placeholder": "Please provide details about your company size, business model, main challenges, etc..."}}, "progress": "Progress", "stepOf": "Step {current} of {total}", "buttons": {"next": "Next", "previous": "Previous", "submit": "Submit", "restart": "<PERSON><PERSON>"}}, "embed": {"initializing": "Initializing...", "loading": "Loading page", "pageNotFound": "The requested page '{page}' could not be found.", "paramError": "Failed to load page due to a parameter error.", "errorTitle": "An Error Occurred"}, "biddingChat": {"showProcess": "Show Analysis Process", "hideProcess": "Hide Analysis Process", "generatingReport": "Generating Final Report...", "scrollToBottom": "Scroll to bottom", "finalReportTitle": "Final Analysis Report", "viewFullReport": "View Full Report", "summaryTitle": "1. Analysis Summary", "backgroundAnalysisTitle": "2. Background Analysis"}, "reportPage": {"defaultTitle": "Report Page", "reportTitle": "Embedded Report", "reportDescription": "This is a simplified report page demonstrating iframe embed functionality.", "parametersTitle": "URL Parameters"}, "resultsPage": {"pageTitle": "Results Page", "resultsTitle": "Analysis Results", "description": "Here are the analysis results and related data.", "parametersTitle": "URL Parameters", "sampleReport1": "Sample Report 1", "sampleReport2": "Sample Report 2"}, "table": {"title": "Title", "status": {"completed": "Completed", "pending": "Pending", "running": "Running", "failed": "Failed"}, "date": "Date", "noData": "No data available"}, "dashboardPage": {"welcomeTitle": "Welcome", "welcomeMessage": "Welcome, {name}!", "dashboardTitle": "Embedded Dashboard", "dashboardDescription": "This is a simplified dashboard page demonstrating iframe embed functionality.", "parametersTitle": "URL Parameters"}, "ErrorPage": {"title": "Something went wrong", "subtitle": "The application encountered an unexpected error. We are working to resolve it.", "errorDetails": "<PERSON><PERSON><PERSON>", "errorDetailsDescription": "The following information may help the support team diagnose the issue.", "errorMessage": "Error Message", "unknownError": "Unknown Error", "errorId": "Error ID", "occurrenceTime": "Time of Occurrence", "retry": "Retry", "backToHome": "Back to Home", "refreshPage": "Refresh Page", "troubleshootingTitle": "Troubleshooting Suggestions", "troubleshooting1": "Refresh the page or try again later.", "troubleshooting2": "Check if your network connection is stable.", "troubleshooting3": "Clear your browser's cache and cookies.", "troubleshooting4": "Try accessing the site with a different browser.", "stillHavingIssuesTitle": "Still having issues?", "stillHavingIssuesDescription": "If the error persists, please contact our technical support team. Provide the error details above for a quick diagnosis.", "sendReport": "Send Error Report", "sending": "Sending...", "reportSent": "Report Sent", "resend": "Resend", "sendingReport": "Sending error report...", "sendReportFailed": "An exception occurred while sending the error report. Please try again later.", "sendFailed": "Failed to send. Please try again later.", "footerCode": "Error Code: 500 | Internal Server Error", "footerMessage": "We have automatically logged this error, and our technical team will address it as soon as possible."}, "AnalysisReportsTable": {"taskName": "Task Name", "newTitle": "New Title", "status": "Status", "progress": "Progress", "createdAt": "Created At", "updatedAt": "Updated At", "actions": "Actions", "completed": "Completed", "running": "Running", "failed": "Failed", "pending": "Pending", "cancelled": "Cancelled"}}