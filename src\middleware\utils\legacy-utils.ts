/**
 * 传统中间件工具函数
 * 从原有 middleware.ts 迁移的工具函数
 */

import { NextRequest, NextResponse } from 'next/server'

/**
 * 中间件工具函数集合
 * 保持与原有代码的兼容性
 */
export const middlewareUtils = {
  /**
   * 检查请求是否来自移动设备
   */
  isMobileDevice(request: NextRequest): boolean {
    const userAgent = request.headers.get('user-agent') || ''
    return /Mobile|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent)
  },

  /**
   * 获取客户端IP地址
   */
  getClientIP(request: NextRequest): string {
    const forwarded = request.headers.get('x-forwarded-for')
    const realIP = request.headers.get('x-real-ip')
    const cfConnectingIP = request.headers.get('cf-connecting-ip')

    if (forwarded) {
      return forwarded.split(',')[0].trim()
    }

    if (realIP) {
      return realIP
    }

    if (cfConnectingIP) {
      return cfConnectingIP
    }

    return 'unknown'
  },

  /**
   * 检查请求是否来自搜索引擎爬虫
   */
  isBot(request: NextRequest): boolean {
    const userAgent = request.headers.get('user-agent') || ''
    return /bot|crawler|spider|crawling/i.test(userAgent)
  },

  /**
   * 获取请求的地理位置信息（如果可用）
   */
  getGeoLocation(request: NextRequest): {
    country?: string
    region?: string
    city?: string
  } {
    const country = request.headers.get('cf-ipcountry')
    const region = request.headers.get('cf-region')
    const city = request.headers.get('cf-city')

    const result: { country?: string; region?: string; city?: string } = {}
    if (country) result.country = country
    if (region) result.region = region
    if (city) result.city = city

    return result
  },

  /**
   * 创建带有追踪信息的响应
   */
  createTrackedResponse(
    response: NextResponse,
    trackingData: Record<string, string>
  ): NextResponse {
    Object.entries(trackingData).forEach(([key, value]) => {
      response.headers.set(`x-track-${key}`, value)
    })
    return response
  },
}
