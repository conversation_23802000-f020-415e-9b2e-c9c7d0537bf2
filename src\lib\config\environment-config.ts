/**
 * 环境配置抽象层 - 支持Mock/真实环境无缝切换
 * 
 * 🎯 设计目标：
 * 1. 开发阶段使用Mock系统进行快速开发和测试
 * 2. 后端完成后，一键切换到真实WebSocket连接
 * 3. 零代码修改，零心智负担
 * 4. 统一的API接口，透明的环境切换
 */

// 环境类型定义
export type EnvironmentMode = 'mock' | 'development' | 'production'

// 连接配置接口
export interface ConnectionEnvironmentConfig {
  mode: EnvironmentMode
  
  // Mock模式配置
  mock?: {
    enabled: boolean
    autoReply: boolean
    responseDelay: number
    defaultScenario: string
  } | undefined

  // WebSocket模式配置
  websocket?: {
    url: string
    reconnectInterval: number
    maxReconnectAttempts: number
    heartbeatInterval: number
    timeout: number
  } | undefined
  
  // 通用配置
  features?: {
    enablePerformanceMonitoring: boolean
    enableDataFlowLogging: boolean
    enableVirtualization: boolean
  } | undefined
}

// 默认配置
const DEFAULT_MOCK_CONFIG: NonNullable<ConnectionEnvironmentConfig['mock']> = {
  enabled: true,
  autoReply: true,
  responseDelay: 500,
  defaultScenario: 'streaming-fast',
}

const DEFAULT_WEBSOCKET_CONFIG: NonNullable<ConnectionEnvironmentConfig['websocket']> = {
  url: process.env.NEXT_PUBLIC_WEBSOCKET_URL || 'ws://localhost:8000/ws',
  reconnectInterval: 3000,
  maxReconnectAttempts: 5,
  heartbeatInterval: 30000,
  timeout: 10000,
}

const DEFAULT_FEATURES_CONFIG: NonNullable<ConnectionEnvironmentConfig['features']> = {
  enablePerformanceMonitoring: process.env.NODE_ENV === 'development',
  enableDataFlowLogging: process.env.NODE_ENV === 'development',
  enableVirtualization: false,
}

// 🎯 环境配置管理器
export class EnvironmentConfigManager {
  private static instance: EnvironmentConfigManager
  private currentConfig: ConnectionEnvironmentConfig

  private constructor() {
    // 🎯 根据环境变量或配置文件确定默认模式
    const mode = this.determineEnvironmentMode()
    
    this.currentConfig = {
      mode,
      mock: DEFAULT_MOCK_CONFIG,
      websocket: DEFAULT_WEBSOCKET_CONFIG,
      features: DEFAULT_FEATURES_CONFIG,
    }
    
    console.log(`🌍 Environment initialized: ${mode}`, {
      config: this.currentConfig,
      timestamp: new Date().toISOString(),
    })
  }

  public static getInstance(): EnvironmentConfigManager {
    if (!EnvironmentConfigManager.instance) {
      EnvironmentConfigManager.instance = new EnvironmentConfigManager()
    }
    return EnvironmentConfigManager.instance
  }

  /**
   * 🎯 智能环境模式判断
   */
  private determineEnvironmentMode(): EnvironmentMode {
    // 1. 检查环境变量强制设置
    const forceMode = process.env.NEXT_PUBLIC_CONNECTION_MODE as EnvironmentMode
    if (forceMode && ['mock', 'development', 'production'].includes(forceMode)) {
      console.log(`🔧 强制环境模式: ${forceMode}`)
      return forceMode
    }

    // 2. 检查WebSocket URL是否配置
    const websocketUrl = process.env.NEXT_PUBLIC_WEBSOCKET_URL
    if (websocketUrl && websocketUrl !== 'mock') {
      console.log(`🔗 检测到WebSocket URL，使用真实连接模式`)
      return process.env.NODE_ENV === 'production' ? 'production' : 'development'
    }

    // 3. 默认使用Mock模式（便于开发）
    console.log(`🎭 默认使用Mock模式进行开发`)
    return 'mock'
  }

  /**
   * 🎯 获取当前环境配置
   */
  public getConfig(): ConnectionEnvironmentConfig {
    return { ...this.currentConfig }
  }

  /**
   * 🎯 获取当前环境模式
   */
  public getMode(): EnvironmentMode {
    return this.currentConfig.mode
  }

  /**
   * 🎯 判断是否为Mock模式
   */
  public isMockMode(): boolean {
    return this.currentConfig.mode === 'mock'
  }

  /**
   * 🎯 判断是否为开发模式
   */
  public isDevelopmentMode(): boolean {
    return this.currentConfig.mode === 'development'
  }

  /**
   * 🎯 判断是否为生产模式
   */
  public isProductionMode(): boolean {
    return this.currentConfig.mode === 'production'
  }

  /**
   * 🎯 获取连接类型（用于连接管理器选择）
   */
  public getConnectionType(): 'mock' | 'websocket' {
    return this.isMockMode() ? 'mock' : 'websocket'
  }

  /**
   * 🎯 获取Mock配置
   */
  public getMockConfig(): NonNullable<ConnectionEnvironmentConfig['mock']> {
    return this.currentConfig.mock || DEFAULT_MOCK_CONFIG
  }

  /**
   * 🎯 获取WebSocket配置
   */
  public getWebSocketConfig(): NonNullable<ConnectionEnvironmentConfig['websocket']> {
    return this.currentConfig.websocket || DEFAULT_WEBSOCKET_CONFIG
  }

  /**
   * 🎯 获取功能配置
   */
  public getFeaturesConfig(): NonNullable<ConnectionEnvironmentConfig['features']> {
    return this.currentConfig.features || DEFAULT_FEATURES_CONFIG
  }

  /**
   * 🎯 运行时切换环境模式（开发时使用）
   * 注意：生产环境不应该使用此方法
   */
  public switchMode(mode: EnvironmentMode, reason?: string): void {
    if (process.env.NODE_ENV === 'production') {
      console.warn('⚠️ 生产环境不允许运行时切换模式')
      return
    }

    const oldMode = this.currentConfig.mode
    this.currentConfig.mode = mode
    
    console.log(`🔄 环境模式切换: ${oldMode} → ${mode}`, {
      reason: reason || '手动切换',
      timestamp: new Date().toISOString(),
      newConfig: this.getConfig(),
    })
  }

  /**
   * 🎯 更新配置（深度合并）
   */
  public updateConfig(partialConfig: Partial<ConnectionEnvironmentConfig>): void {
    this.currentConfig = {
      ...this.currentConfig,
      ...partialConfig,
      mock: partialConfig.mock 
        ? { ...this.currentConfig.mock, ...partialConfig.mock }
        : this.currentConfig.mock,
      websocket: partialConfig.websocket
        ? { ...this.currentConfig.websocket, ...partialConfig.websocket }
        : this.currentConfig.websocket,
      features: partialConfig.features
        ? { ...this.currentConfig.features, ...partialConfig.features }
        : this.currentConfig.features,
    }

    console.log('🔧 配置已更新:', this.currentConfig)
  }

  /**
   * 🎯 获取用户友好的环境描述
   */
  public getEnvironmentDescription(): string {
    switch (this.currentConfig.mode) {
      case 'mock':
        return '🎭 Mock模式 - 本地模拟，快速开发'
      case 'development':
        return '🔧 开发模式 - 连接开发服务器'
      case 'production':
        return '🚀 生产模式 - 连接生产服务器'
      default:
        return '❓ 未知模式'
    }
  }

  /**
   * 🎯 验证当前配置是否有效
   */
  public validateConfig(): { valid: boolean; errors: string[] } {
    const errors: string[] = []

    // Mock模式验证
    if (this.isMockMode()) {
      const mockConfig = this.getMockConfig()
      if (mockConfig.responseDelay < 0) {
        errors.push('Mock响应延迟不能为负数')
      }
    }

    // WebSocket模式验证
    if (!this.isMockMode()) {
      const wsConfig = this.getWebSocketConfig()
      if (!wsConfig.url) {
        errors.push('WebSocket URL不能为空')
      }
      if (wsConfig.reconnectInterval < 1000) {
        errors.push('重连间隔不能小于1秒')
      }
      if (wsConfig.maxReconnectAttempts < 1) {
        errors.push('最大重连次数不能小于1')
      }
    }

    return {
      valid: errors.length === 0,
      errors,
    }
  }

  /**
   * 🎯 生成诊断报告
   */
  public generateDiagnosticReport(): {
    mode: EnvironmentMode
    description: string
    config: ConnectionEnvironmentConfig
    validation: { valid: boolean; errors: string[] }
    environment: {
      nodeEnv: string
      websocketUrl?: string | undefined
      forcedMode?: string | undefined
    }
  } {
    return {
      mode: this.getMode(),
      description: this.getEnvironmentDescription(),
      config: this.getConfig(),
      validation: this.validateConfig(),
      environment: {
        nodeEnv: process.env.NODE_ENV || 'unknown',
        ...(process.env.NEXT_PUBLIC_WEBSOCKET_URL && { websocketUrl: process.env.NEXT_PUBLIC_WEBSOCKET_URL }),
        ...(process.env.NEXT_PUBLIC_CONNECTION_MODE && { forcedMode: process.env.NEXT_PUBLIC_CONNECTION_MODE }),
      }
    }
  }
}

// 🎯 全局单例实例
export const environmentConfig = EnvironmentConfigManager.getInstance()

// 🎯 便捷的Hook风格接口
export const useEnvironmentConfig = () => {
  return {
    mode: environmentConfig.getMode(),
    isMockMode: environmentConfig.isMockMode(),
    isDevelopmentMode: environmentConfig.isDevelopmentMode(),
    isProductionMode: environmentConfig.isProductionMode(),
    connectionType: environmentConfig.getConnectionType(),
    mockConfig: environmentConfig.getMockConfig(),
    websocketConfig: environmentConfig.getWebSocketConfig(),
    featuresConfig: environmentConfig.getFeaturesConfig(),
    description: environmentConfig.getEnvironmentDescription(),
    switchMode: environmentConfig.switchMode.bind(environmentConfig),
    updateConfig: environmentConfig.updateConfig.bind(environmentConfig),
    validateConfig: environmentConfig.validateConfig.bind(environmentConfig),
    diagnosticReport: environmentConfig.generateDiagnosticReport.bind(environmentConfig),
  }
}

// 🎯 类型已在上面直接导出，无需重复导出