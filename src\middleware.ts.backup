/**
 * Next.js 中间件
 * 实现路由保护和基本安全功能
 */

import { NextRequest, NextResponse } from 'next/server'

/**
 * 主中间件函数
 * @param request Next.js请求对象
 * @returns Next.js响应对象
 */
export async function middleware(request: NextRequest): Promise<NextResponse> {
  const { pathname } = request.nextUrl

  // 开发环境日志
  if (process.env.NODE_ENV === 'development') {
    console.log(`[Middleware] Processing: ${pathname}`)
  }

  // 跳过静态资源和API路由
  if (shouldSkipMiddleware(pathname)) {
    return NextResponse.next()
  }

  try {
    // 🔧 路由权限控制
    const authResponse = await handleRouteProtection(request, pathname)
    if (authResponse) {
      return authResponse
    }

    // 简化的中间件处理
    const response = NextResponse.next()

    // 为非嵌入页面添加标准安全头
    if (!pathname.startsWith('/embed')) {
      addSecurityHeaders(response)
    }

    return response
  } catch (error) {
    console.error('[Middleware] Error:', error)

    // 发生错误时，允许请求继续
    return NextResponse.next()
  }
}

/**
 * 检查是否应该跳过中间件处理
 * @param pathname 路径名
 * @returns 是否跳过
 */
function shouldSkipMiddleware(pathname: string): boolean {
  const skipPatterns = [
    // 静态资源
    '/_next/static',
    '/_next/image',
    '/favicon.ico',
    '/robots.txt',
    '/sitemap.xml',

    // 图片和媒体文件
    /\.(jpg|jpeg|png|gif|svg|ico|webp|avif)$/,
    /\.(mp4|webm|ogg|mp3|wav|flac|aac)$/,
    /\.(woff|woff2|eot|ttf|otf)$/,

    // API路由（如果需要特殊处理）
    '/api/public',
    '/api/health',
    '/api/status',

    // 开发工具
    '/__nextjs',
    '/_vercel',

    // 特殊路径
    '/manifest.json',
    '/sw.js',
    '/workbox-',
  ]

  return skipPatterns.some(pattern => {
    if (typeof pattern === 'string') {
      return pathname.startsWith(pattern)
    }
    if (pattern instanceof RegExp) {
      return pattern.test(pathname)
    }
    return false
  })
}

/**
 * 添加安全头
 * @param response Next.js响应对象
 */
function addSecurityHeaders(response: NextResponse): void {
  // 防止点击劫持
  response.headers.set('X-Frame-Options', 'DENY')

  // 防止MIME类型嗅探
  response.headers.set('X-Content-Type-Options', 'nosniff')

  // XSS保护
  response.headers.set('X-XSS-Protection', '1; mode=block')

  // 引用者策略
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')

  // 权限策略
  response.headers.set(
    'Permissions-Policy',
    'camera=(), microphone=(), geolocation=(), interest-cohort=()'
  )
}

/**
 * 中间件配置
 * 定义哪些路径需要经过中间件处理
 * 使用Next.js官方推荐的matcher模式
 */
export const config = {
  matcher: [
    /*
     * 匹配所有路径，除了：
     * - api路由（/api开头）
     * - _next（Next.js内部路由）
     * - 静态资源文件（包含.的文件）
     */
    '/((?!api|_next|.*\\..*).*)',
  ],
}

/**
 * 路由权限配置
 */
const ROUTE_CONFIG = {
  // 所有人都可以访问的页面
  public: [],
  // 仅未登录用户可以访问的页面
  guestOnly: ['/login', '/register'],
  // 需要登录才能访问的页面 - 包含根路径
  protected: ['/chat', '/poll'],
} as const

/**
 * 检查用户是否已认证（基于cookie）
 * @param request Next.js请求对象
 * @returns 是否已认证
 */
function isAuthenticated(request: NextRequest): boolean {
  // 优先检查主cookie: better-auth.session_token
  let sessionCookie = request.cookies.get('better-auth.session_token')

  // 备用cookie检查: auth.session_token
  if (!sessionCookie?.value) {
    sessionCookie = request.cookies.get('auth.session_token')
  }

  if (!sessionCookie?.value) {
    return false
  }

  // Better Auth token格式验证: sessionId.encryptedData
  const parts = sessionCookie.value.split('.')
  if (parts.length !== 2) {
    return false
  }

  // 基础长度检查
  const [sessionId, encryptedData] = parts
  return sessionId.length > 10 && encryptedData.length > 10
}

/**
 * 获取用户session数据（包含角色信息）
 * @param request Next.js请求对象
 * @returns 用户session数据或null
 */
async function getUserSession(request: NextRequest): Promise<any> {
  try {
    // 检查是否有session cookie
    if (!isAuthenticated(request)) {
      return null
    }

    // 构建Better Auth API URL
    const baseUrl = request.nextUrl.origin
    const sessionUrl = `${baseUrl}/api/auth/get-session`

    // 转发所有cookies到Better Auth API
    const cookieHeader = request.headers.get('cookie') || ''

    const response = await fetch(sessionUrl, {
      method: 'GET',
      headers: {
        Cookie: cookieHeader,
        'Content-Type': 'application/json',
      },
      cache: 'no-store',
    })

    if (!response.ok) {
      return null
    }

    const sessionData = await response.json()
    return sessionData
  } catch (error) {
    console.error('[Middleware] Failed to get user session:', error)
    return null
  }
}

/**
 * 检查用户是否为管理员
 * @param sessionData 用户session数据
 * @returns 是否为管理员
 */
function isAdminUser(sessionData: any): boolean {
  const userRole = sessionData?.user?.role
  return userRole === 'admin' || userRole === 'super_admin'
}

/**
 * 路由权限控制函数
 * @param request Next.js请求对象
 * @param pathname 路径名
 * @returns 重定向响应或null
 */
async function handleRouteProtection(
  request: NextRequest,
  pathname: string
): Promise<NextResponse | null> {
  try {
    // 获取用户认证状态
    const isAuth = isAuthenticated(request)

    // 🎯 管理员权限检查 - 优先级最高
    if (isAuth) {
      const sessionData = await getUserSession(request)
      if (sessionData && isAdminUser(sessionData)) {
        if (process.env.NODE_ENV === 'development') {
          console.log(`[Auth] Admin user detected, allowing access to: ${pathname}`)
        }
        // 管理员可以访问任何页面，直接返回null
        return null
      }
    }

    // 路由分类
    const isPublic = ROUTE_CONFIG.public.some(route => pathname.startsWith(route))
    const isGuestOnly = ROUTE_CONFIG.guestOnly.some(route => pathname === route)
    const isProtected = ROUTE_CONFIG.protected.some(route => pathname.startsWith(route))

    if (process.env.NODE_ENV === 'development') {
      console.log(
        `[Auth] Route: ${pathname}, Authenticated: ${isAuth}, Type: ${
          isPublic ? 'public' : isGuestOnly ? 'guest-only' : isProtected ? 'protected' : 'unknown'
        }`
      )
    }

    // 公开页面，所有人都可以访问
    if (isPublic) {
      return null
    }

    // 受保护页面，需要登录
    if (isProtected && !isAuth) {
      if (process.env.NODE_ENV === 'development') {
        console.log(`[Auth] Redirecting unauthenticated user to login`)
      }
      return NextResponse.redirect(new URL('/login', request.url))
    }

    // 仅访客页面，已登录用户重定向到dashboard
    if (isGuestOnly && isAuth) {
      if (process.env.NODE_ENV === 'development') {
        console.log(`[Auth] Redirecting authenticated user to dashboard`)
      }
      return NextResponse.redirect(new URL('/chat', request.url))
    }

    // 🎯 默认情况：未知路由且未认证时重定向到login
    if (!isAuth && !isPublic && !isGuestOnly) {
      if (process.env.NODE_ENV === 'development') {
        console.log(`[Auth] Unknown route for unauthenticated user, redirecting to login`)
      }
      return NextResponse.redirect(new URL('/login', request.url))
    }

    // 其他情况正常访问
    return null
  } catch (error) {
    console.error('[Auth] Route protection error:', error)
    // 发生错误时，重定向到安全页面
    return NextResponse.redirect(new URL('/login', request.url))
  }
}

/**
 * 中间件工具函数
 */
export const middlewareUtils = {
  /**
   * 检查请求是否来自移动设备
   */
  isMobileDevice(request: NextRequest): boolean {
    const userAgent = request.headers.get('user-agent') || ''
    return /Mobile|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent)
  },

  /**
   * 获取客户端IP地址
   */
  getClientIP(request: NextRequest): string {
    const forwarded = request.headers.get('x-forwarded-for')
    const realIP = request.headers.get('x-real-ip')
    const cfConnectingIP = request.headers.get('cf-connecting-ip')

    if (forwarded) {
      return forwarded.split(',')[0].trim()
    }

    if (realIP) {
      return realIP
    }

    if (cfConnectingIP) {
      return cfConnectingIP
    }

    return 'unknown'
  },

  /**
   * 检查请求是否来自搜索引擎爬虫
   */
  isBot(request: NextRequest): boolean {
    const userAgent = request.headers.get('user-agent') || ''
    return /bot|crawler|spider|crawling/i.test(userAgent)
  },

  /**
   * 获取请求的地理位置信息（如果可用）
   */
  getGeoLocation(request: NextRequest): {
    country?: string
    region?: string
    city?: string
  } {
    const country = request.headers.get('cf-ipcountry')
    const region = request.headers.get('cf-region')
    const city = request.headers.get('cf-city')

    const result: { country?: string; region?: string; city?: string } = {}
    if (country) result.country = country
    if (region) result.region = region
    if (city) result.city = city

    return result
  },

  /**
   * 创建带有追踪信息的响应
   */
  createTrackedResponse(
    response: NextResponse,
    trackingData: Record<string, string>
  ): NextResponse {
    Object.entries(trackingData).forEach(([key, value]) => {
      response.headers.set(`x-track-${key}`, value)
    })
    return response
  },
}
