/**
 * Mock数据生成器
 * 用于测试前端对所有message type的卡片渲染
 */

import { nanoid } from 'nanoid'

export interface MockChatMessage {
  id: string
  content: string
  sender: 'user' | 'assistant' | 'system'
  timestamp: Date
  hasReport?: boolean
  reportId?: string
  isHighlighted?: boolean
  hasOptions?: boolean
  options?: string[]
  htmlContent?: string | undefined
  messageType?: string
  payload?: any
}

export class MockDataGenerator {
  private static instance: MockDataGenerator | null = null

  static getInstance(): MockDataGenerator {
    if (!MockDataGenerator.instance) {
      MockDataGenerator.instance = new MockDataGenerator()
    }
    return MockDataGenerator.instance
  }

  /**
   * 生成流式消息
   */
  generateStreamingMessage(isComplete: boolean = false): MockChatMessage {
    const content = isComplete
      ? '这是一个完整的AI回复。我已经分析了您的问题，并提供了详细的解答。'
      : '正在生成回复中...'

    return {
      id: nanoid(),
      content,
      sender: 'assistant',
      timestamp: new Date(),
      messageType: 'streaming',
      payload: {
        type: 'streaming',
        delta: isComplete ? '' : '正在思考...',
        accumulated: content,
        isComplete,
      },
    }
  }

  /**
   * 生成检查点消息（表单）
   */
  generateCheckpointMessage(): MockChatMessage {
    return {
      id: nanoid(),
      content: '请填写以下信息以继续分析：',
      sender: 'assistant',
      timestamp: new Date(),
      hasOptions: true,
      options: ['公司名称', '行业类型', '分析时间范围', '关注指标'],
      messageType: 'checkpoint',
      payload: {
        type: 'checkpoint',
        fields: [
          {
            id: 'company_name',
            label: '公司名称',
            type: 'text',
            required: true,
            placeholder: '请输入公司名称',
          },
          {
            id: 'industry',
            label: '行业类型',
            type: 'select',
            required: true,
            options: ['科技', '金融', '制造业', '服务业', '其他'],
          },
          {
            id: 'time_range',
            label: '分析时间范围',
            type: 'select',
            required: true,
            options: ['最近1个月', '最近3个月', '最近6个月', '最近1年'],
          },
          {
            id: 'metrics',
            label: '关注指标',
            type: 'multiselect',
            required: false,
            options: ['营收增长', '市场份额', '用户增长', '成本控制', '盈利能力'],
          },
        ],
      },
    }
  }

  /**
   * 生成报告消息
   */
  generateReportMessage(): MockChatMessage {
    const reportContent = `# 📊 市场分析报告

## 🎯 执行摘要
基于您提供的信息，我们完成了全面的市场分析。以下是关键发现：

### 📈 主要发现
- **市场趋势**: 整体呈现上升趋势，增长率达到15%
- **竞争格局**: 市场集中度适中，存在差异化机会
- **用户需求**: 对创新产品的需求持续增长

### 💡 建议
1. **短期策略**: 加强产品差异化
2. **中期规划**: 扩大市场份额
3. **长期愿景**: 建立行业领导地位

### 📊 数据支撑
- 样本量: 10,000+
- 置信度: 95%
- 误差范围: ±3%

---
*报告生成时间: ${new Date().toLocaleString()}*`

    return {
      id: nanoid(),
      content: '📋 分析报告已生成完成',
      sender: 'assistant',
      timestamp: new Date(),
      hasReport: true,
      reportId: `report-${nanoid()}`,
      htmlContent: reportContent,
      messageType: 'report',
      payload: {
        type: 'report',
        content: reportContent,
      },
    }
  }

  /**
   * 生成错误消息
   */
  generateErrorMessage(
    errorType: 'network' | 'server' | 'validation' | 'unknown' = 'unknown'
  ): MockChatMessage {
    const errorMessages = {
      network: '网络连接出现问题，请检查您的网络连接后重试',
      server: '服务器暂时无法处理您的请求，请稍后重试',
      validation: '输入内容格式不正确，请检查后重新提交',
      unknown: '发生未知错误，请稍后重试',
    }

    const errorCodes = {
      network: 'NETWORK_ERROR',
      server: 'SERVER_ERROR',
      validation: 'VALIDATION_ERROR',
      unknown: 'UNKNOWN_ERROR',
    }

    return {
      id: nanoid(),
      content: `❌ ${errorMessages[errorType]}`,
      sender: 'system',
      timestamp: new Date(),
      messageType: 'error',
      payload: {
        type: 'error',
        code: errorCodes[errorType],
        message: errorMessages[errorType],
      },
    }
  }

  /**
   * 生成文件消息
   */
  generateFileMessage(): MockChatMessage {
    return {
      id: nanoid(),
      content: '📁 已为您生成以下文件：',
      sender: 'assistant',
      timestamp: new Date(),
      messageType: 'file',
      payload: {
        type: 'file',
        files: [
          {
            id: 'file-1',
            name: '市场分析报告.pdf',
            size: 2048576, // 2MB
            type: 'application/pdf',
            url: '/mock/files/market-analysis.pdf',
          },
          {
            id: 'file-2',
            name: '数据图表.xlsx',
            size: 1024000, // 1MB
            type: 'application/vnd.ms-excel',
            url: '/mock/files/data-charts.xlsx',
          },
          {
            id: 'file-3',
            name: '分析结果.json',
            size: 51200, // 50KB
            type: 'application/json',
            url: '/mock/files/analysis-results.json',
          },
        ],
      },
    }
  }

  /**
   * 生成用户消息
   */
  generateUserMessage(content: string): MockChatMessage {
    return {
      id: nanoid(),
      content,
      sender: 'user',
      timestamp: new Date(),
    }
  }

  /**
   * 生成完整的对话流程
   */
  generateFullConversation(): MockChatMessage[] {
    return [
      this.generateUserMessage('你好，我需要进行市场分析'),
      this.generateStreamingMessage(true),
      this.generateCheckpointMessage(),
      this.generateUserMessage('公司名称：TechCorp，行业：科技，时间范围：最近6个月'),
      this.generateStreamingMessage(false),
      this.generateStreamingMessage(true),
      this.generateReportMessage(),
      this.generateFileMessage(),
      this.generateUserMessage('能否提供更详细的竞争分析？'),
      this.generateErrorMessage('network'),
      this.generateStreamingMessage(true),
    ]
  }

  /**
   * 生成特定类型的消息集合
   */
  generateMessagesByType(
    type: 'streaming' | 'checkpoint' | 'report' | 'error' | 'file'
  ): MockChatMessage[] {
    switch (type) {
      case 'streaming':
        return [this.generateStreamingMessage(false), this.generateStreamingMessage(true)]
      case 'checkpoint':
        return [this.generateCheckpointMessage()]
      case 'report':
        return [this.generateReportMessage()]
      case 'error':
        return [
          this.generateErrorMessage('network'),
          this.generateErrorMessage('server'),
          this.generateErrorMessage('validation'),
          this.generateErrorMessage('unknown'),
        ]
      case 'file':
        return [this.generateFileMessage()]
      default:
        return []
    }
  }
}

// 导出单例实例
export const mockDataGenerator = MockDataGenerator.getInstance()

// 导出便捷方法
export const generateMockConversation = () => mockDataGenerator.generateFullConversation()
export const generateMockMessagesByType = (
  type: 'streaming' | 'checkpoint' | 'report' | 'error' | 'file'
) => mockDataGenerator.generateMessagesByType(type)
