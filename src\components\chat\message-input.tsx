/**
 * 消息输入组件 - 专门负责用户输入处理
 *
 * 功能：文本输入、功能控制、键盘快捷键支持
 * 依赖：useInteractionState UI store
 * 性能：优化了输入响应和自动调整高度
 *
 * 数据流：User Input -> MessageInput -> Store -> Send Action
 */

'use client'

import React, { useState, useEffect, useRef, forwardRef } from 'react'
import { Lightbulb, Globe, Paperclip, Mic } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { cn } from '@/lib/utils'

// UI Store
import { useInteractionState } from '@/stores/ui-store'

// ============================================================================
// 组件接口定义
// ============================================================================

export interface MessageInputProps {
  /** 输入值和处理 */
  value: string
  onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void
  onSubmit: (e?: React.FormEvent) => void
  onKeyDown?: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void

  /** UI配置 */
  placeholder?: string
  disabled?: boolean
  className?: string
  maxRows?: number
  autoFocus?: boolean

  /** 功能开关 */
  features?: {
    enableFileUpload?: boolean
    enableVoiceInput?: boolean
    enableThinkMode?: boolean
    enableDeepSearch?: boolean
  }

  /** 事件回调 */
  onAttachClick?: () => void
  onMicClick?: () => void
}

// 默认配置
const DEFAULT_FEATURES = {
  enableFileUpload: false,
  enableVoiceInput: false,
  enableThinkMode: false,
  enableDeepSearch: false,
}

// ============================================================================
// 主要组件
// ============================================================================

export const MessageInput = forwardRef<HTMLTextAreaElement, MessageInputProps>(
  (
    {
      value,
      onChange,
      onSubmit,
      onKeyDown,
      placeholder = '输入消息...',
      disabled = false,
      className,
      maxRows = 5,
      autoFocus = false,
      features = DEFAULT_FEATURES,
      onAttachClick,
      onMicClick,
    },
    ref
  ) => {
    // ============================================================================
    // Store状态和本地状态
    // ============================================================================

    const { interactionState, setThinkMode, setDeepSearch } = useInteractionState()
    const { thinkMode, deepSearch } = interactionState

    const [isActive, setIsActive] = useState(false)
    const textareaRef = useRef<HTMLTextAreaElement>(null)

    // 合并外部ref和内部ref
    React.useImperativeHandle(ref, () => textareaRef.current!, [])

    // ============================================================================
    // 功能配置
    // ============================================================================

    const config = {
      ...DEFAULT_FEATURES,
      ...features,
    }

    const showControls = Object.values(config).some(Boolean)
    const showExpandedControls = (isActive || value) && showControls

    // ============================================================================
    // 副作用处理
    // ============================================================================

    // 自动聚焦
    useEffect(() => {
      if (autoFocus && textareaRef.current) {
        textareaRef.current.focus()
      }
    }, [autoFocus])

    // 自动调整textarea高度
    useEffect(() => {
      if (textareaRef.current) {
        const textarea = textareaRef.current
        textarea.style.height = 'auto' // 重置高度以重新计算
        const scrollHeight = textarea.scrollHeight

        if (maxRows) {
          // 合理的行高近似值
          const lineHeight = parseFloat(window.getComputedStyle(textarea).lineHeight)
          const maxHeight = lineHeight * maxRows

          if (scrollHeight > maxHeight) {
            textarea.style.height = `${maxHeight}px`
            textarea.style.overflowY = 'auto'
          } else {
            textarea.style.height = `${scrollHeight}px`
            textarea.style.overflowY = 'hidden'
          }
        } else {
          textarea.style.height = `${scrollHeight}px`
          textarea.style.overflowY = 'hidden'
        }
      }
    }, [value, maxRows])

    // ============================================================================
    // 事件处理
    // ============================================================================

    const handleFocus = () => {
      setIsActive(true)
    }

    const handleBlur = () => {
      setIsActive(false)
    }

    const handleClick = (e: React.MouseEvent<HTMLTextAreaElement>) => {
      // 阻止事件冒泡
      e.stopPropagation()

      if (!disabled) {
        e.currentTarget.focus()
      }
    }

    const handleAttachClick = () => {
      if (config.enableFileUpload && onAttachClick) {
        onAttachClick()
      }
    }

    const handleMicClick = () => {
      if (config.enableVoiceInput && onMicClick) {
        onMicClick()
      }
    }

    const handleThinkToggle = () => {
      if (config.enableThinkMode) {
        setThinkMode(!thinkMode)
      }
    }

    const handleDeepSearchToggle = () => {
      if (config.enableDeepSearch) {
        setDeepSearch(!deepSearch)
      }
    }

    // ============================================================================
    // 动画配置
    // ============================================================================

    const containerVariants = {
      collapsed: {
        height: 68,
        boxShadow: '0 2px 8px 0 rgba(0,0,0,0.08)',
        transition: { type: 'spring', stiffness: 120, damping: 18 },
      },
      expanded: {
        height: 'auto',
        boxShadow: '0 8px 32px 0 rgba(0,0,0,0.16)',
        transition: { type: 'spring', stiffness: 120, damping: 18 },
      },
    }

    // ============================================================================
    // 渲染
    // ============================================================================

    return (
      <motion.div
        className={cn(
          'w-full max-w-4xl bg-white rounded-2xl sm:rounded-3xl overflow-hidden',
          className
        )}
        variants={containerVariants}
        animate={isActive || value ? 'expanded' : 'collapsed'}
        initial="collapsed"
      >
        <div className="flex flex-col items-stretch w-full h-full p-2 sm:p-3">
          {/* 输入行 */}
          <div className="flex items-start gap-1 sm:gap-2 w-full">
            {/* 附件按钮 */}
            {config.enableFileUpload && (
              <button
                className="p-2 sm:p-3 rounded-full hover:bg-gray-100 transition-colors disabled:opacity-50 min-w-[44px] min-h-[44px] flex items-center justify-center"
                title="附加文件"
                type="button"
                onClick={handleAttachClick}
                disabled={disabled}
              >
                <Paperclip size={18} className="sm:w-5 sm:h-5" />
              </button>
            )}

            {/* 文本输入区域 */}
            <div className="relative flex-1 pl-1 sm:pl-2">
              <textarea
                ref={textareaRef}
                value={value}
                onChange={onChange}
                onKeyDown={onKeyDown}
                onFocus={handleFocus}
                onBlur={handleBlur}
                onClick={handleClick}
                placeholder={placeholder}
                disabled={disabled}
                rows={1}
                className="message-input-textarea flex-1 border-0 outline-0 rounded-md text-base bg-transparent w-full font-normal resize-none pr-20"
                style={{
                  boxSizing: 'border-box',
                  overflowY: 'hidden',
                  pointerEvents: 'auto',
                  position: 'relative',
                  zIndex: 10,
                }}
              />
            </div>

            {/* 语音按钮 */}
            {config.enableVoiceInput && (
              <button
                className="p-2 sm:p-3 rounded-full hover:bg-gray-100 transition-colors disabled:opacity-50 min-w-[44px] min-h-[44px] flex items-center justify-center"
                title="语音输入"
                type="button"
                onClick={handleMicClick}
                disabled={disabled}
              >
                <Mic size={18} className="sm:w-5 sm:h-5" />
              </button>
            )}
          </div>

          {/* 扩展控制面板 */}
          <AnimatePresence>
            {showExpandedControls && (
              <motion.div
                className="w-full flex justify-start px-2 sm:px-4 items-center text-xs sm:text-sm mt-2"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0, transition: { duration: 0.3, delay: 0.1 } }}
                exit={{ opacity: 0, y: 10, transition: { duration: 0.2 } }}
              >
                <div className="flex gap-2 sm:gap-3 items-center">
                  {/* 思考模式切换 */}
                  {config.enableThinkMode && (
                    <button
                      className={cn(
                        'flex items-center gap-1 px-3 sm:px-4 py-2 rounded-full transition-all font-medium group text-xs sm:text-sm min-h-[36px]',
                        thinkMode
                          ? 'bg-blue-600/10 outline outline-blue-600/60 text-blue-950'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      )}
                      title="思考模式"
                      type="button"
                      onClick={handleThinkToggle}
                    >
                      <Lightbulb
                        className={cn(
                          'transition-all',
                          thinkMode ? 'fill-blue-500' : 'group-hover:fill-yellow-300'
                        )}
                        size={18}
                      />
                      思考模式
                    </button>
                  )}

                  {/* 深度搜索切换 */}
                  {config.enableDeepSearch && (
                    <button
                      className={cn(
                        'flex items-center gap-1 px-3 sm:px-4 py-2 rounded-full transition-all font-medium text-xs sm:text-sm min-h-[36px]',
                        deepSearch
                          ? 'bg-purple-600/10 outline outline-purple-600/60 text-purple-950'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      )}
                      title="深度搜索"
                      type="button"
                      onClick={handleDeepSearchToggle}
                    >
                      <Globe
                        className={cn('transition-all', deepSearch ? 'text-purple-600' : '')}
                        size={18}
                      />
                      深度搜索
                    </button>
                  )}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* 自定义样式 */}
        <style jsx>{`
          .message-input-textarea::-webkit-scrollbar {
            width: 6px;
            height: 6px;
          }
          .message-input-textarea::-webkit-scrollbar-track {
            background: transparent;
          }
          .message-input-textarea::-webkit-scrollbar-thumb {
            background-color: var(--border);
            border-radius: 3px;
          }
          .message-input-textarea::-webkit-scrollbar-thumb:hover {
            background-color: var(--muted-foreground);
          }
          .message-input-textarea {
            scrollbar-width: thin;
            scrollbar-color: var(--border) transparent;
            pointer-events: auto !important;
            cursor: text;
          }
          .message-input-textarea:disabled {
            pointer-events: none;
            cursor: not-allowed;
            opacity: 0.6;
          }
        `}</style>
      </motion.div>
    )
  }
)

MessageInput.displayName = 'MessageInput'

// ============================================================================
// 输入状态Hook
// ============================================================================

export const useMessageInputState = () => {
  const { interactionState, setThinkMode, setDeepSearch } = useInteractionState()

  return {
    thinkMode: interactionState.thinkMode,
    deepSearch: interactionState.deepSearch,
    setThinkMode,
    setDeepSearch,
  }
}

export default MessageInput
