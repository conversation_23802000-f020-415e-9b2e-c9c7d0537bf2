/**
 * 全局详情面板组件
 * 提供类似Vue slot插槽的概念，支持插入任意React组件内容
 * 真实占用容器空间，不是浮层组件
 * 支持从右侧滑入/滑出的动画效果
 */

'use client'

import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { X } from 'lucide-react'
import { cn } from '@/lib/utils'

interface GlobalDetailPanelProps {
  /** 详情面板标题 */
  title: string
  /** 是否打开 */
  isOpen: boolean
  /** 关闭回调 */
  onClose: () => void
  /** 打开回调 */
  onOpen?: () => void
  /** 插槽内容 */
  children: React.ReactNode
  /** 自定义样式类名 */
  className?: string
  /** 详情框宽度 */
  width?: string | number
  /** 是否显示关闭按钮 */
  showCloseButton?: boolean
  /** 是否显示头部 */
  showHeader?: boolean
}

/**
 * GlobalDetailPanel 组件
 *
 * 使用示例:
 * ```tsx
 * <GlobalDetailPanel
 *   title="报告详情"
 *   isOpen={isDetailOpen}
 *   onClose={() => setIsDetailOpen(false)}
 *   width={400}
 * >
 *   <ReportViewer report={selectedReport} />
 * </GlobalDetailPanel>
 * ```
 */
export function GlobalDetailPanel({
  title,
  isOpen,
  onClose,
  onOpen,
  children,
  className,
  width = 516,
  showCloseButton = true,
  showHeader = true,
}: GlobalDetailPanelProps) {
  // 处理宽度值
  const panelWidth = typeof width === 'number' ? `${width}px` : width

  // 动画配置
  const panelVariants = {
    closed: {
      width: 0,
      opacity: 0,
      transition: {
        duration: 0.4,
        ease: [0.4, 0, 0.2, 1],
      },
    },
    open: {
      width: panelWidth,
      opacity: 1,
      transition: {
        duration: 0.4,
        ease: [0.4, 0, 0.2, 1],
      },
    },
  }

  const contentVariants = {
    closed: {
      opacity: 0,
      x: 30,
      scale: 0.95,
      transition: {
        duration: 0.25,
        ease: [0.4, 0, 0.2, 1],
      },
    },
    open: {
      opacity: 1,
      x: 0,
      scale: 1,
      transition: {
        duration: 0.4,
        delay: 0.15,
        ease: [0.4, 0, 0.2, 1],
      },
    },
  }

  return (
    <AnimatePresence mode="wait">
      {isOpen && (
        <motion.div
          initial="closed"
          animate="open"
          exit="closed"
          variants={panelVariants}
          className={cn('flex-shrink-0 h-full', className)}
          style={{ width: panelWidth }}
        >
          <motion.div variants={contentVariants} className="h-full p-4">
            {/* 完整高度容器：确保与父容器高度一致，保持所有圆角 */}
            <div
              className="w-full h-full bg-white rounded-3xl border border-stone-300 flex flex-col shadow-lg"
              style={{ overflow: 'hidden' }}
            >
              {/* 头部 */}
              {showHeader && (
                <div className="p-6 border-b border-stone-200 flex-shrink-0">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
                    {showCloseButton && (
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={onClose}
                        className="h-8 w-8 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full transition-all duration-200"
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                </div>
              )}

              {/* 内容区域 - 使用插槽，确保滚动正常工作 */}
              <div className="flex-1 min-h-0">
                <ScrollArea className="h-full w-full">
                  <div className="p-0">{children}</div>
                </ScrollArea>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

/**
 * 简化版本的详情面板，不带动画
 * 适用于性能要求较高的场景
 */
export function SimpleDetailPanel({
  title,
  isOpen,
  onClose,
  children,
  className,
  width = 516,
  showCloseButton = true,
  showHeader = true,
}: GlobalDetailPanelProps) {
  if (!isOpen) return null

  const panelWidth = typeof width === 'number' ? `${width}px` : width

  return (
    <div
      className={cn('flex-shrink-0 h-full transition-all duration-300', className)}
      style={{ width: panelWidth }}
    >
      <div className="h-full p-4">
        {/* 完整高度容器：确保与父容器高度一致，保持所有圆角 */}
        <div
          className="w-full h-full bg-white rounded-3xl border border-stone-300 flex flex-col shadow-lg"
          style={{ overflow: 'hidden' }}
        >
          {/* 头部 */}
          {showHeader && (
            <div className="p-6 border-b border-stone-200 flex-shrink-0">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
                {showCloseButton && (
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={onClose}
                    className="h-8 w-8 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full transition-all duration-200"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                )}
              </div>
            </div>
          )}

          {/* 内容区域 - 使用插槽，确保滚动正常工作 */}
          <div className="flex-1 min-h-0">
            <ScrollArea className="h-full w-full">
              <div className="p-0">{children}</div>
            </ScrollArea>
          </div>
        </div>
      </div>
    </div>
  )
}

export default GlobalDetailPanel
