'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'
import type { ProcessedError, ErrorRecoveryAction } from '@/lib/websocket/error-processor'

// Shadcn UI 组件
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import {
  AlertTriangle,
  XCircle,
  AlertCircle,
  RefreshCw,
  ChevronDown,
  ChevronRight,
  Clock,
  Info,
} from 'lucide-react'

export interface ErrorDisplayProps {
  /** 错误信息 */
  error: ProcessedError
  /** 恢复操作 */
  recoveryActions?: ErrorRecoveryAction[]
  /** 是否显示技术详情 */
  showTechnicalDetails?: boolean
  /** 是否可折叠 */
  collapsible?: boolean
  /** 自定义样式 */
  className?: string
  /** 错误处理回调 */
  onRetry?: () => void | Promise<void>
  onDismiss?: () => void
}

// 错误严重程度对应的图标和颜色
const getSeverityConfig = (severity: ProcessedError['severity']) => {
  switch (severity) {
    case 'critical':
      return {
        icon: XCircle,
        variant: 'destructive' as const,
        bgColor: 'bg-destructive/10',
        textColor: 'text-destructive',
      }
    case 'high':
      return {
        icon: AlertTriangle,
        variant: 'destructive' as const,
        bgColor: 'bg-orange-50 dark:bg-orange-950/20',
        textColor: 'text-orange-600 dark:text-orange-400',
      }
    case 'medium':
      return {
        icon: AlertCircle,
        variant: 'default' as const,
        bgColor: 'bg-yellow-50 dark:bg-yellow-950/20',
        textColor: 'text-yellow-600 dark:text-yellow-400',
      }
    case 'low':
      return {
        icon: Info,
        variant: 'default' as const,
        bgColor: 'bg-blue-50 dark:bg-blue-950/20',
        textColor: 'text-blue-600 dark:text-blue-400',
      }
    default:
      return {
        icon: AlertCircle,
        variant: 'default' as const,
        bgColor: 'bg-gray-50 dark:bg-gray-950/20',
        textColor: 'text-gray-600 dark:text-gray-400',
      }
  }
}

export const ErrorDisplay = ({
  error,
  recoveryActions = [],
  showTechnicalDetails = false,
  collapsible = true,
  className,
  onRetry,
  onDismiss,
}: ErrorDisplayProps) => {
  const [isExpanded, setIsExpanded] = useState(!collapsible)
  const [isRetrying, setIsRetrying] = useState(false)

  const severityConfig = getSeverityConfig(error.severity)
  const Icon = severityConfig.icon

  // 处理重试操作
  const handleRetry = async () => {
    if (!onRetry) return

    setIsRetrying(true)
    try {
      await onRetry()
    } finally {
      setIsRetrying(false)
    }
  }

  // 处理恢复操作
  const handleRecoveryAction = async (action: ErrorRecoveryAction): Promise<void> => {
    if (action.isRetry && onRetry) {
      await handleRetry()
    } else if (action.action) {
      await action.action()
    }
    // 确保所有代码路径都有返回值
    return Promise.resolve()
  }

  const formatTimestamp = (timestamp: Date) => {
    const now = new Date()
    const diff = now.getTime() - timestamp.getTime()

    if (diff < 60000) {
      // 1分钟内
      return '刚刚'
    } else if (diff < 3600000) {
      // 1小时内
      return `${Math.floor(diff / 60000)}分钟前`
    } else {
      return timestamp.toLocaleTimeString()
    }
  }

  const errorContent = (
    <div className="space-y-4">
      {/* 错误主要信息 */}
      <Alert variant={severityConfig.variant} className={cn(severityConfig.bgColor)}>
        <Icon className="h-4 w-4" />
        <AlertTitle className="flex items-center justify-between">
          <span>{error.userMessage}</span>
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className={severityConfig.textColor}>
              {error.severity?.toUpperCase()}
            </Badge>
            <Badge variant="secondary" className="text-xs">
              {error.category}
            </Badge>
          </div>
        </AlertTitle>
        {error.code !== 'UNKNOWN_ERROR' && (
          <AlertDescription className="mt-2">
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <Clock className="w-3 h-3" />
              <span>发生时间: {formatTimestamp(error.timestamp || new Date())}</span>
              <span>•</span>
              <span>错误代码: {error.code}</span>
            </div>
          </AlertDescription>
        )}
      </Alert>

      {/* 恢复操作 */}
      {(error.isRetryable || recoveryActions.length > 0) && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium">建议操作:</h4>
          <div className="flex flex-wrap gap-2">
            {error.isRetryable && (
              <Button
                size="sm"
                variant="outline"
                onClick={handleRetry}
                disabled={isRetrying}
                className="h-8"
              >
                {isRetrying ? (
                  <>
                    <RefreshCw className="w-3 h-3 mr-1 animate-spin" />
                    重试中...
                  </>
                ) : (
                  <>
                    <RefreshCw className="w-3 h-3 mr-1" />
                    重试 ({error.retryCount}/{error.maxRetries})
                  </>
                )}
              </Button>
            )}

            {recoveryActions.map((action, index) => (
              <Button
                key={index}
                size="sm"
                variant="outline"
                onClick={() => handleRecoveryAction(action)}
                className="h-8"
              >
                {action.label}
              </Button>
            ))}

            {onDismiss && (
              <Button
                size="sm"
                variant="ghost"
                onClick={onDismiss}
                className="h-8 text-muted-foreground"
              >
                忽略
              </Button>
            )}
          </div>
        </div>
      )}

      {/* 恢复建议 */}
      {error.recoveryActions && error.recoveryActions.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium">解决建议:</h4>
          <ul className="space-y-1">
            {error.recoveryActions.map((action, index) => (
              <li key={index} className="text-sm text-muted-foreground flex items-start space-x-2">
                <span className="w-4 h-4 rounded-full bg-primary/10 text-primary text-xs flex items-center justify-center mt-0.5 flex-shrink-0">
                  {index + 1}
                </span>
                <span>{action}</span>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* 技术详情 */}
      {showTechnicalDetails && error.technicalDetails && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium">技术详情:</h4>
          <div className="p-3 bg-muted rounded-md">
            <code className="text-xs text-muted-foreground break-all">
              {error.technicalDetails}
            </code>
          </div>
        </div>
      )}
    </div>
  )

  if (!collapsible) {
    return (
      <motion.div
        className={cn('w-full', className)}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -10 }}
      >
        <Card>
          <CardContent className="p-4">{errorContent}</CardContent>
        </Card>
      </motion.div>
    )
  }

  return (
    <motion.div
      className={cn('w-full', className)}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
    >
      <Card>
        <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
          <CollapsibleTrigger asChild>
            <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
              <CardTitle className="text-sm flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Icon className={cn('w-4 h-4', severityConfig.textColor)} />
                  <span>{error.userMessage}</span>
                  <Badge variant="outline" className={cn('text-xs', severityConfig.textColor)}>
                    {error.severity}
                  </Badge>
                </div>
                {isExpanded ? (
                  <ChevronDown className="w-4 h-4" />
                ) : (
                  <ChevronRight className="w-4 h-4" />
                )}
              </CardTitle>
              <CardDescription className="text-left">
                {formatTimestamp(error.timestamp || new Date())} • {error.code}
              </CardDescription>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent className="pt-0">{errorContent}</CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>
    </motion.div>
  )
}

// 使用 ErrorProcessor 的 Hook
import { useEffect, useState as useStateHook } from 'react'

export interface UseErrorDisplayProps {
  /** 错误信息 */
  error?: ProcessedError
  /** 自动消失时间（毫秒），0表示不自动消失 */
  autoHideMs?: number
}

export const useErrorDisplay = ({ error, autoHideMs = 0 }: UseErrorDisplayProps = {}) => {
  const [isVisible, setIsVisible] = useStateHook(false)
  const [currentError, setCurrentError] = useStateHook<ProcessedError | null>(null)

  useEffect(() => {
    if (error) {
      setCurrentError(error)
      setIsVisible(true)

      if (autoHideMs > 0) {
        const timer = setTimeout(() => {
          setIsVisible(false)
        }, autoHideMs)
        return () => clearTimeout(timer)
      }
    }
    // 确保所有路径都有正确的返回值
    return undefined
  }, [error, autoHideMs, setCurrentError, setIsVisible])

  const hideError = () => {
    setIsVisible(false)
  }

  const showError = () => {
    setIsVisible(true)
  }

  return {
    isVisible,
    currentError,
    hideError,
    showError,
  }
}
