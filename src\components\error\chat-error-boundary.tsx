'use client'

import React, { Component, ReactNode } from 'react'
import { useTranslations } from 'next-intl'

// 🔧 错误边界状态类型
interface ErrorInfo {
  error: Error
  errorCode?: string
  errorType: 'connection' | 'security' | 'render' | 'unknown'
  timestamp: Date
  userAgent: string | undefined
  url: string | undefined
}

interface ErrorBoundaryState {
  hasError: boolean
  errorInfo?: ErrorInfo
  retryCount: number
}

interface ChatErrorBoundaryProps {
  children: ReactNode
  fallback?: (errorInfo: ErrorInfo, retry: () => void) => ReactNode
  onError?: (errorInfo: ErrorInfo) => void
  maxRetries?: number
}

/**
 * 聊天系统专用错误边界组件
 * 提供统一的错误处理、用户友好的错误界面和自动恢复机制
 */
class ChatErrorBoundaryClass extends Component<ChatErrorBoundaryProps, ErrorBoundaryState> {
  private retryTimer?: NodeJS.Timeout

  constructor(props: ChatErrorBoundaryProps) {
    super(props)
    this.state = {
      hasError: false,
      retryCount: 0
    }
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    // 分析错误类型
    const errorType = ChatErrorBoundaryClass.categorizeError(error)
    
    const errorInfo: ErrorInfo = {
      error,
      errorType,
      timestamp: new Date(),
      userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : undefined,
      url: typeof window !== 'undefined' ? window.location.href : undefined,
    }

    return {
      hasError: true,
      errorInfo
    }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // 记录错误详情
    console.error('🔥 ChatErrorBoundary caught an error:', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      errorBoundary: 'ChatErrorBoundary'
    })

    // 通知外部错误处理器
    if (this.state.errorInfo) {
      this.props.onError?.(this.state.errorInfo)
    }

    // 发送错误报告（生产环境）
    if (process.env.NODE_ENV === 'production') {
      this.reportError(error, errorInfo)
    }
  }

  // 🔧 错误分类
  private static categorizeError(error: Error): ErrorInfo['errorType'] {
    const message = error.message.toLowerCase()
    const stack = error.stack?.toLowerCase() || ''

    // 连接相关错误
    if (message.includes('network') || 
        message.includes('connection') || 
        message.includes('timeout') ||
        message.includes('fetch')) {
      return 'connection'
    }

    // 安全相关错误
    if (message.includes('permission') || 
        message.includes('unauthorized') || 
        message.includes('forbidden') ||
        message.includes('security')) {
      return 'security'
    }

    // 渲染相关错误
    if (message.includes('render') || 
        message.includes('component') || 
        stack.includes('react')) {
      return 'render'
    }

    return 'unknown'
  }

  // 🔧 错误报告
  private reportError = (error: Error, errorInfo: React.ErrorInfo) => {
    // 这里可以集成错误监控服务（如 Sentry）
    try {
      // 准备错误数据
      const errorData = {
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        userId: 'unknown', // 可以从 session 中获取
        errorType: this.state.errorInfo?.errorType
      }

      // 发送到错误监控服务
      console.log('📊 Error report prepared:', errorData)
      // 实际实现中应该发送到监控服务
    } catch (reportError) {
      console.error('❌ Failed to report error:', reportError)
    }
  }

  // 🔧 重试机制
  private handleRetry = () => {
    const maxRetries = this.props.maxRetries ?? 3
    
    if (this.state.retryCount >= maxRetries) {
      console.warn('⚠️ 已达到最大重试次数')
      return
    }

    console.log(`🔄 尝试恢复... (${this.state.retryCount + 1}/${maxRetries})`)

    this.setState(prevState => ({
      hasError: false,
      retryCount: prevState.retryCount + 1
    }))

    // 延迟一秒后重试，给组件恢复的时间
    this.retryTimer = setTimeout(() => {
      // 如果重试后又出错，会重新触发错误边界
      this.forceUpdate()
    }, 1000)
  }

  // 🔧 重置错误状态
  private handleReset = () => {
    this.setState({
      hasError: false,
      retryCount: 0
    })
  }

  componentWillUnmount() {
    if (this.retryTimer) {
      clearTimeout(this.retryTimer)
    }
  }

  render() {
    if (this.state.hasError && this.state.errorInfo) {
      // 使用自定义 fallback 或默认错误界面
      if (this.props.fallback) {
        return this.props.fallback(this.state.errorInfo, this.handleRetry)
      }

      return (
        <ChatErrorFallback 
          errorInfo={this.state.errorInfo}
          retryCount={this.state.retryCount}
          maxRetries={this.props.maxRetries ?? 3}
          onRetry={this.handleRetry}
          onReset={this.handleReset}
        />
      )
    }

    return this.props.children
  }
}

// 🎨 默认错误界面组件
interface ChatErrorFallbackProps {
  errorInfo: ErrorInfo
  retryCount: number
  maxRetries: number
  onRetry: () => void
  onReset: () => void
}

function ChatErrorFallback({ 
  errorInfo, 
  retryCount, 
  maxRetries, 
  onRetry, 
  onReset 
}: ChatErrorFallbackProps) {
  const t = useTranslations('chat')

  // 根据错误类型选择图标和消息
  const getErrorIcon = () => {
    switch (errorInfo.errorType) {
      case 'connection':
        return (
          <svg className="mx-auto h-16 w-16 text-orange-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0" />
          </svg>
        )
      case 'security':
        return (
          <svg className="mx-auto h-16 w-16 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
        )
      case 'render':
        return (
          <svg className="mx-auto h-16 w-16 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
        )
      default:
        return (
          <svg className="mx-auto h-16 w-16 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        )
    }
  }

  const getErrorTitle = () => {
    switch (errorInfo.errorType) {
      case 'connection':
        return t('error.boundary.connection.title')
      case 'security':
        return t('error.boundary.security.title')
      case 'render':
        return t('error.boundary.render.title')
      default:
        return t('error.boundary.unknown.title')
    }
  }

  const getErrorMessage = () => {
    switch (errorInfo.errorType) {
      case 'connection':
        return t('error.boundary.connection.message')
      case 'security':
        return t('error.boundary.security.message')
      case 'render':
        return t('error.boundary.render.message')
      default:
        return t('error.boundary.unknown.message')
    }
  }

  const canRetry = retryCount < maxRetries
  const isConnectionError = errorInfo.errorType === 'connection'

  return (
    <div className="flex items-center justify-center h-full bg-gray-50">
      <div className="text-center p-8 max-w-md mx-auto">
        {/* 错误图标 */}
        <div className="mb-6">
          {getErrorIcon()}
        </div>

        {/* 错误标题 */}
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          {getErrorTitle()}
        </h2>

        {/* 错误描述 */}
        <p className="text-sm text-gray-600 mb-6">
          {getErrorMessage()}
        </p>

        {/* 技术详情（开发环境） */}
        {process.env.NODE_ENV === 'development' && (
          <details className="mb-6 text-left">
            <summary className="cursor-pointer text-sm font-medium text-gray-700 mb-2">
              {t('error.boundary.technicalDetails')}
            </summary>
            <div className="bg-gray-100 p-3 rounded text-xs font-mono text-gray-800 overflow-auto max-h-32">
              <div><strong>Error:</strong> {errorInfo.error.message}</div>
              <div><strong>Type:</strong> {errorInfo.errorType}</div>
              <div><strong>Time:</strong> {errorInfo.timestamp.toLocaleString()}</div>
              {errorInfo.error.stack && (
                <div><strong>Stack:</strong> {errorInfo.error.stack.slice(0, 200)}...</div>
              )}
            </div>
          </details>
        )}

        {/* 操作按钮 */}
        <div className="space-y-3">
          {canRetry && (
            <button
              onClick={onRetry}
              disabled={!canRetry}
              className="w-full px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isConnectionError 
                ? t('error.boundary.retry.connection') 
                : t('error.boundary.retry.general')
              }
              {retryCount > 0 && ` (${retryCount}/${maxRetries})`}
            </button>
          )}
          
          <button
            onClick={onReset}
            className="w-full px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors"
          >
            {t('error.boundary.reset')}
          </button>

          <button
            onClick={() => window.location.reload()}
            className="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
          >
            {t('error.boundary.refresh')}
          </button>
        </div>

        {/* 重试次数提示 */}
        {!canRetry && (
          <p className="mt-4 text-xs text-gray-500">
            {t('error.boundary.maxRetriesReached')}
          </p>
        )}
      </div>
    </div>
  )
}

// 🔧 包装函数，提供更好的 TypeScript 支持
export function ChatErrorBoundary(props: ChatErrorBoundaryProps) {
  return <ChatErrorBoundaryClass {...props} />
}

// 导出类型
export type { ErrorInfo, ChatErrorBoundaryProps }