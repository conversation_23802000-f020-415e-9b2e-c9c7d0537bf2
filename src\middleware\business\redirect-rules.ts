/**
 * 重定向规则定义
 * 统一管理所有业务重定向逻辑，消除重定向链条
 */

import { NextRequest } from 'next/server'
import { MiddlewareContext } from '../utils/types'
import { RouteClassifier, RouteRedirectDecider } from '../utils/route-matcher'

/**
 * 重定向决策结果
 */
export interface RedirectDecision {
  /** 是否需要重定向 */
  shouldRedirect: boolean
  /** 重定向目标 */
  target?: string
  /** 重定向原因（用于调试） */
  reason?: string
  /** 重定向优先级 */
  priority: 'high' | 'medium' | 'low'
}

/**
 * 业务上下文类型扩展
 */
interface BusinessContext extends MiddlewareContext {
  hasCompanyProfile?: boolean
  organizationRole?: string
}

/**
 * 根路径重定向规则
 * 这是解决多次重定向问题的关键
 */
export function getRootPathRedirect(
  request: NextRequest,
  context: BusinessContext
): RedirectDecision {
  const { isAuthenticated, isAdmin, hasCompanyProfile } = context

  // 未认证用户 -> 登录页
  if (!isAuthenticated) {
    return {
      shouldRedirect: true,
      target: '/login',
      reason: '用户未认证',
      priority: 'high',
    }
  }

  // 管理员用户直接进入聊天页
  if (isAdmin) {
    return {
      shouldRedirect: true,
      target: '/chat',
      reason: '管理员用户跳过问卷',
      priority: 'high',
    }
  }

  // 普通用户根据公司资料完成状态决定
  if (hasCompanyProfile === false) {
    return {
      shouldRedirect: true,
      target: '/poll',
      reason: '需要完善公司资料',
      priority: 'high',
    }
  }

  // 资料已完成或状态未知时，进入聊天页
  return {
    shouldRedirect: true,
    target: '/chat',
    reason: '正常业务流程',
    priority: 'high',
  }
}

/**
 * 受保护页面访问规则
 */
export function getProtectedRouteRedirect(
  request: NextRequest,
  context: BusinessContext
): RedirectDecision {
  const { pathname, isAuthenticated, isAdmin, hasCompanyProfile } = context

  // 未认证用户 -> 登录页
  if (!isAuthenticated) {
    return {
      shouldRedirect: true,
      target: '/login',
      reason: '访问受保护页面需要登录',
      priority: 'high',
    }
  }

  // 管理员可以访问所有受保护页面
  if (isAdmin) {
    return {
      shouldRedirect: false,
      reason: '管理员权限通过',
      priority: 'low',
    }
  }

  // 普通用户访问聊天页面但没有完善资料
  if (pathname.startsWith('/chat') && hasCompanyProfile === false) {
    return {
      shouldRedirect: true,
      target: '/poll',
      reason: '访问聊天页面需要先完善公司资料',
      priority: 'high',
    }
  }

  // 普通用户访问问卷页面但已经完善了资料
  if (pathname.startsWith('/poll') && hasCompanyProfile === true) {
    return {
      shouldRedirect: true,
      target: '/chat',
      reason: '资料已完善，跳转到聊天页面',
      priority: 'medium',
    }
  }

  // 其他情况允许访问
  return {
    shouldRedirect: false,
    reason: '受保护页面访问权限验证通过',
    priority: 'low',
  }
}

/**
 * 仅访客页面访问规则
 */
export function getGuestOnlyRouteRedirect(
  request: NextRequest,
  context: BusinessContext
): RedirectDecision {
  const { isAuthenticated, isAdmin, hasCompanyProfile } = context

  // 未认证用户可以正常访问
  if (!isAuthenticated) {
    return {
      shouldRedirect: false,
      reason: '未认证用户可以访问访客页面',
      priority: 'low',
    }
  }

  // 已认证用户需要重定向到业务页面
  // 这里使用与根路径相同的逻辑，确保一致性
  if (isAdmin) {
    return {
      shouldRedirect: true,
      target: '/chat',
      reason: '管理员用户重定向到聊天页面',
      priority: 'high',
    }
  }

  if (hasCompanyProfile === false) {
    return {
      shouldRedirect: true,
      target: '/poll',
      reason: '已认证用户需要完善资料',
      priority: 'high',
    }
  }

  return {
    shouldRedirect: true,
    target: '/chat',
    reason: '已认证用户重定向到聊天页面',
    priority: 'high',
  }
}

/**
 * 管理员页面访问规则
 */
export function getAdminRouteRedirect(
  request: NextRequest,
  context: BusinessContext
): RedirectDecision {
  const { isAuthenticated, isAdmin } = context

  // 未认证用户 -> 登录页
  if (!isAuthenticated) {
    return {
      shouldRedirect: true,
      target: '/login',
      reason: '访问管理页面需要登录',
      priority: 'high',
    }
  }

  // 非管理员用户 -> 聊天页面
  if (!isAdmin) {
    return {
      shouldRedirect: true,
      target: '/chat',
      reason: '非管理员用户无权访问管理页面',
      priority: 'high',
    }
  }

  // 管理员可以访问
  return {
    shouldRedirect: false,
    reason: '管理员权限验证通过',
    priority: 'low',
  }
}

/**
 * API 路由访问规则
 */
export function getApiRouteRedirect(
  request: NextRequest,
  context: BusinessContext
): RedirectDecision {
  // API 路由不处理重定向，让它们正常执行
  return {
    shouldRedirect: false,
    reason: 'API 路由跳过重定向处理',
    priority: 'low',
  }
}

/**
 * 嵌入页面访问规则
 */
export function getEmbedRouteRedirect(
  request: NextRequest,
  context: BusinessContext
): RedirectDecision {
  // 嵌入页面有自己的认证逻辑，不在中间件层处理
  return {
    shouldRedirect: false,
    reason: '嵌入页面跳过中间件重定向',
    priority: 'low',
  }
}

/**
 * 主重定向决策函数
 * 根据路由类型选择对应的重定向规则
 */
export function getRedirectDecision(
  request: NextRequest,
  context: BusinessContext
): RedirectDecision {
  const { pathname } = context

  // 根据路由类型应用不同的规则
  if (RouteClassifier.isRootPath(pathname)) {
    return getRootPathRedirect(request, context)
  }

  if (RouteClassifier.isGuestOnlyRoute(pathname)) {
    return getGuestOnlyRouteRedirect(request, context)
  }

  if (RouteClassifier.isProtectedRoute(pathname)) {
    return getProtectedRouteRedirect(request, context)
  }

  if (RouteClassifier.isAdminOnlyRoute(pathname)) {
    return getAdminRouteRedirect(request, context)
  }

  if (RouteClassifier.isApiRoute(pathname)) {
    return getApiRouteRedirect(request, context)
  }

  if (RouteClassifier.isEmbedRoute(pathname)) {
    return getEmbedRouteRedirect(request, context)
  }

  if (RouteClassifier.isPublicRoute(pathname)) {
    return {
      shouldRedirect: false,
      reason: '公开页面无需重定向',
      priority: 'low',
    }
  }

  // 未知路由类型 - 根据认证状态决定
  return getUnknownRouteRedirect(request, context)
}

/**
 * 未知路由处理规则
 * 对于未明确分类的路由，采用保守策略
 */
function getUnknownRouteRedirect(request: NextRequest, context: BusinessContext): RedirectDecision {
  const { isAuthenticated } = context

  // 未认证用户重定向到登录页
  if (!isAuthenticated) {
    return {
      shouldRedirect: true,
      target: '/login',
      reason: '未知路由，未认证用户重定向到登录页',
      priority: 'medium',
    }
  }

  // 已认证用户允许访问未知路由
  return {
    shouldRedirect: false,
    reason: '已认证用户可以访问未知路由',
    priority: 'low',
  }
}

/**
 * 重定向规则验证器
 * 检查重定向规则是否会造成循环重定向
 */
export function validateRedirectDecision(
  decision: RedirectDecision,
  currentPath: string
): RedirectDecision {
  // 检查是否会造成自己重定向到自己
  if (decision.shouldRedirect && decision.target === currentPath) {
    console.warn(`[Redirect Rules] 检测到循环重定向: ${currentPath} -> ${decision.target}`)
    return {
      shouldRedirect: false,
      reason: '防止循环重定向',
      priority: 'high',
    }
  }

  // 检查常见的重定向循环模式
  const cyclicPatterns = [
    { from: '/login', to: '/login' },
    { from: '/chat', to: '/poll' },
    { from: '/poll', to: '/chat' },
  ]

  for (const pattern of cyclicPatterns) {
    if (currentPath === pattern.from && decision.target === pattern.to) {
      // 这种情况需要更仔细的分析，暂时允许但记录警告
      console.warn(`[Redirect Rules] 潜在重定向循环: ${pattern.from} -> ${pattern.to}`)
    }
  }

  return decision
}

/**
 * 重定向日志记录
 */
export function logRedirectDecision(
  decision: RedirectDecision,
  context: { pathname: string; isAuthenticated: boolean }
): void {
  if (process.env.NODE_ENV === 'development') {
    console.log(`[Business Redirect]`, {
      path: context.pathname,
      shouldRedirect: decision.shouldRedirect,
      target: decision.target,
      reason: decision.reason,
      priority: decision.priority,
      authenticated: context.isAuthenticated,
    })
  }
}
