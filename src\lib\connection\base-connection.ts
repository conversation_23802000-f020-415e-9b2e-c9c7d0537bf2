/**
 * 连接管理器抽象基类
 * 提供通用的连接管理功能实现
 */

import {
  ConnectionStatus,
  ConnectionType,
  type IConnectionManager,
  type ConnectionConfig,
  type ConnectionResult,
  type ConnectionStats,
  type MessageHandler,
  type StatusHandler,
  type <PERSON>rrorHandler,
} from './types'
import type { BaseWebSocketMessage } from '@/types/websocket-event-type'

export abstract class BaseConnection implements IConnectionManager {
  protected status: ConnectionStatus = ConnectionStatus.DISCONNECTED
  protected config: ConnectionConfig = {}
  protected stats: ConnectionStats = {
    messagesSent: 0,
    messagesReceived: 0,
    reconnectCount: 0,
    errors: 0,
  }

  // 事件处理器集合
  protected messageHandlers = new Set<MessageHandler>()
  protected statusHandlers = new Set<StatusHandler>()
  protected errorHandlers = new Set<ErrorHandler>()

  // 内部状态
  protected isDestroyed = false

  // ============================================================================
  // 抽象方法 - 子类必须实现
  // ============================================================================

  abstract connect(config?: ConnectionConfig): Promise<ConnectionResult>
  abstract disconnect(): Promise<void>
  abstract sendMessage(message: BaseWebSocketMessage): Promise<void>
  abstract getType(): ConnectionType

  // ============================================================================
  // 事件监听管理
  // ============================================================================

  onMessage(handler: MessageHandler): () => void {
    this.messageHandlers.add(handler)

    // 🔥 关键调试：记录handler注册
    console.log('🔗 [BaseConnection] Message handler registered:', {
      connectionType: this.getType(),
      totalHandlers: this.messageHandlers.size,
      handlerRegistered: true,
      timestamp: new Date().toISOString(),
    })

    return () => {
      this.messageHandlers.delete(handler)
      console.log('🔗 [BaseConnection] Message handler unregistered:', {
        connectionType: this.getType(),
        remainingHandlers: this.messageHandlers.size,
      })
    }
  }

  onStatus(handler: StatusHandler): () => void {
    this.statusHandlers.add(handler)
    return () => this.statusHandlers.delete(handler)
  }

  onError(handler: ErrorHandler): () => void {
    this.errorHandlers.add(handler)
    return () => this.errorHandlers.delete(handler)
  }

  // ============================================================================
  // 状态查询
  // ============================================================================

  getStatus(): ConnectionStatus {
    return this.status
  }

  getStats(): ConnectionStats {
    return { ...this.stats }
  }

  isConnected(): boolean {
    return this.status === ConnectionStatus.CONNECTED
  }

  // ============================================================================
  // 受保护的工具方法
  // ============================================================================

  protected setStatus(newStatus: ConnectionStatus): void {
    if (this.status !== newStatus && !this.isDestroyed) {
      const oldStatus = this.status
      this.status = newStatus

      console.log(`🔄 [${this.getType()}] Status: ${oldStatus} → ${newStatus}`)

      // 触发状态变化事件
      this.statusHandlers.forEach(handler => {
        try {
          handler(newStatus)
        } catch (error) {
          console.error('❌ Status handler error:', error)
        }
      })
    }
  }

  protected emitMessage(message: BaseWebSocketMessage): void {
    if (this.isDestroyed) return

    this.stats.messagesReceived++
    this.stats.lastMessageAt = new Date()

    // 🔥 关键调试：详细记录消息发送过程
    console.log('🔥 [BaseConnection] Emitting message:', {
      messageId: message.id,
      userId: message.userId,
      sessionId: message.sessionId,
      payloadType: message.payload?.type,
      handlersCount: this.messageHandlers.size,
      connectionType: this.getType(),
      isDestroyed: this.isDestroyed,
    })

    this.messageHandlers.forEach((handler, index) => {
      try {
        handler(message)
      } catch (error) {
        this.emitError(new Error(`Message handler failed: ${error}`))
      }
    })

    console.log('🏁 [BaseConnection] All message handlers completed')
  }

  protected emitError(error: Error): void {
    if (this.isDestroyed) return

    this.stats.errors++
    console.error(`❌ [${this.getType()}] Connection error:`, error)

    this.errorHandlers.forEach(handler => {
      try {
        handler(error)
      } catch (handlerError) {
        console.error('❌ Error handler failed:', handlerError)
      }
    })
  }

  protected incrementMessagesSent(): void {
    this.stats.messagesSent++
  }

  protected recordConnection(): void {
    this.stats.connectedAt = new Date()
    this.stats.lastMessageAt = new Date()
  }

  protected recordReconnect(): void {
    this.stats.reconnectCount++
  }

  // ============================================================================
  // 资源清理
  // ============================================================================

  destroy(): void {
    if (this.isDestroyed) return

    console.log(`🗑️ [${this.getType()}] Destroying connection...`)

    this.isDestroyed = true

    // 清理事件处理器
    this.messageHandlers.clear()
    this.statusHandlers.clear()
    this.errorHandlers.clear()

    // 断开连接
    this.disconnect().catch(error => {
      console.error('❌ Error during disconnect:', error)
    })

    // 重置状态
    this.setStatus(ConnectionStatus.DISCONNECTED)
  }

  // ============================================================================
  // 工具方法
  // ============================================================================

  protected validateMessage(message: BaseWebSocketMessage): boolean {
    if (!message || typeof message !== 'object') {
      return false
    }

    const required = [
      'groupChatId',
      'sessionId',
      'userId',
      'organizationId',
      'payload',
      'timestamp',
    ]
    return required.every(
      field => field in message && message[field as keyof BaseWebSocketMessage] != null
    )
  }

  protected generateId(): string {
    return `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  protected createBaseMessage(
    groupChatId: string,
    sessionId: string,
    userId: string,
    organizationId: string
  ): Omit<BaseWebSocketMessage, 'payload'> {
    return {
      id: this.generateId(),
      groupChatId,
      sessionId,
      userId,
      organizationId,
      timestamp: new Date().toISOString(),
    }
  }
}
