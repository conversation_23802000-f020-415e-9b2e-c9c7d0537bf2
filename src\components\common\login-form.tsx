'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useTranslations } from 'next-intl'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { authClient, authUtils } from '@/lib/auth/auth-client'
import { FrontendLanguageSwitch } from '@/components/common/frontend-language-switch'

interface LoginFormProps extends React.ComponentProps<'div'> {}

export function LoginForm({ className, ...props }: LoginFormProps) {
  const router = useRouter()
  const t = useTranslations('auth.login')
  const tCommon = useTranslations('common')
  const tValidation = useTranslations('auth.validation')

  // 表单状态管理
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  })

  // 加载和错误状态
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string>('')

  // 表单验证
  const validateForm = () => {
    if (!formData.email || !formData.password) {
      setError(tValidation('allFieldsRequired'))
      return false
    }

    if (!/\S+@\S+\.\S+/.test(formData.email)) {
      setError(tValidation('emailFormat'))
      return false
    }

    if (formData.password.length < 6) {
      setError(tValidation('passwordLength'))
      return false
    }

    return true
  }

  // 处理邮箱登录
  const handleEmailLogin = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) return

    setIsLoading(true)
    setError('')

    try {
      const result = await authClient.signIn.email({
        email: formData.email,
        password: formData.password,
      })

      if (result.error) {
        // 使用 Better Auth 的错误处理
        const errorMessage = authUtils.getErrorMessage(result.error)
        setError(errorMessage)
        console.error('登录失败:', result.error)
      } else {
        // 登录成功，获取会话信息
        console.log('登录成功:', result.data)
        const redirectTo = '/chat'
        router.push(redirectTo)

        // 获取最新的会话信息
        // try {
        //   const sessionResult = await authClient.getSession()
        //   if (sessionResult.data) {
        //     console.log('会话信息:', sessionResult.data)
        //     console.log('用户信息:', sessionResult.data.user)
        //     console.log('会话详情:', sessionResult.data.session)

        //     // 重定向到dashboard或之前的页面
        //     const redirectTo = '/chat'
        //     router.push(redirectTo)
        //   } else {
        //     console.warn('获取会话信息失败')
        //   }
        // } catch (sessionError) {
        //   console.error('获取会话信息异常:', sessionError)
        // }
      }
    } catch (error) {
      console.error('登录异常:', error)
      setError(tValidation('loginError'))
    } finally {
      setIsLoading(false)
    }
  }

  // 处理第三方登录（暂时只是占位符，保持样式）
  const handleOAuthLogin = async (provider: 'google' | 'apple') => {
    const data = await authClient.signIn.social(
      {
        provider: provider,
      },
      {
        onSuccess: data => {
          console.log(data)
          if (data.data.success && data.data.data.url) {
            window.location.href = data.data.data.url
          }
        },
        onError: error => {
          console.log(error)
        },
      }
    )
    console.log(data)
    console.log(`${provider} 登录功能暂未实现`)
    // 暂时不实现功能，只保留样式
  }

  // 处理输入变化
  const handleInputChange =
    (field: 'email' | 'password') => (e: React.ChangeEvent<HTMLInputElement>) => {
      setFormData(prev => ({
        ...prev,
        [field]: e.target.value,
      }))

      // 清除错误信息
      if (error) setError('')
    }

  // 处理注册跳转
  const handleRegisterClick = () => {
    router.push('/register')
  }

  return (
    <div className={cn('flex flex-col gap-6', className)} {...props}>
      <Card>
        <CardHeader className="text-center">
          <CardTitle className="text-xl">{t('title')}</CardTitle>
          {/* <CardDescription>{t('subtitle')}</CardDescription> */}
        </CardHeader>
        <CardContent className="p-4 sm:p-6">
          <form onSubmit={handleEmailLogin}>
            <div className="grid gap-4 sm:gap-6">
              {/* <div className="flex flex-col gap-4">
                <Button
                  type="button"
                  variant="outline"
                  className="w-full"
                  onClick={() => handleOAuthLogin('apple')}
                  disabled={isLoading}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <path
                      d="M12.152 6.896c-.948 0-2.415-1.078-3.96-1.04-2.04.027-3.91 1.183-4.961 3.014-2.117 3.675-.546 9.103 1.519 12.09 1.013 1.454 2.208 3.09 3.792 3.039 1.52-.065 2.09-.987 3.935-.987 1.831 0 2.35.987 3.96.948 1.637-.026 2.676-1.48 3.676-2.948 1.156-1.688 1.636-3.325 1.662-3.415-.039-.013-3.182-1.221-3.22-4.857-.026-3.04 2.48-4.494 2.597-4.559-1.429-2.09-3.623-2.324-4.39-2.376-2-.156-3.675 1.09-4.61 1.09zM15.53 3.83c.843-1.012 1.4-2.427 1.245-3.83-1.207.052-2.662.805-3.532 1.818-.78.896-1.454 2.338-1.273 3.714 1.338.104 2.715-.688 3.559-1.701"
                      fill="currentColor"
                    />
                  </svg>
                  {t('loginWithApple')}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  className="w-full"
                  onClick={() => handleOAuthLogin('google')}
                  disabled={isLoading}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <path
                      d="M12.48 10.92v3.28h7.84c-.24 1.84-.853 3.187-1.787 4.133-1.147 1.147-2.933 2.4-6.053 2.4-4.827 0-8.6-3.893-8.6-8.72s3.773-8.72 8.6-8.72c2.6 0 4.507 1.027 5.907 2.347l2.307-2.307C18.747 1.44 16.133 0 12.48 0 5.867 0 .307 5.387.307 12s5.56 12 12.173 12c3.573 0 6.267-1.173 8.373-3.36 2.16-2.16 2.84-5.213 2.84-7.667 0-.76-.053-1.467-.173-2.053H12.48z"
                      fill="currentColor"
                    />
                  </svg>
                  {t('loginWithGoogle')}
                </Button>
              </div>
              <div className="after:border-border relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t">
                <span className="bg-card text-muted-foreground relative z-10 px-2">
                  {t('orLoginWith')}
                </span>
              </div> */}

              <div className="grid gap-4 sm:gap-6">
                <div className="grid gap-2 sm:gap-3">
                  <Label htmlFor="email">{t('username')}</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder={t('usernamePlaceholder')}
                    value={formData.email}
                    onChange={handleInputChange('email')}
                    required
                    disabled={isLoading}
                  />
                </div>
                <div className="grid gap-2 sm:gap-3">
                  <div className="flex items-center">
                    <Label htmlFor="password">{t('password')}</Label>
                    <a href="#" className="ml-auto text-sm underline-offset-4 hover:underline">
                      {t('forgotPassword')}
                    </a>
                  </div>
                  <Input
                    id="password"
                    type="password"
                    placeholder={t('passwordPlaceholder')}
                    value={formData.password}
                    onChange={handleInputChange('password')}
                    required
                    disabled={isLoading}
                  />
                </div>
                <Button type="submit" className="w-full" disabled={isLoading}>
                  {isLoading ? t('loggingIn') : t('loginButton')}
                </Button>
              </div>
              <div className="text-center text-sm">
                {t('noAccount')}{' '}
                <button
                  type="button"
                  onClick={handleRegisterClick}
                  className="underline underline-offset-4"
                >
                  {t('registerNow')}
                </button>
              </div>

              {/* 语言切换组件 */}
              <div className="flex justify-center mt-4 sm:mt-6 pt-4 sm:pt-6 border-t">
                <FrontendLanguageSwitch variant="ghost" size="sm" />
              </div>
            </div>
          </form>
        </CardContent>
      </Card>
      <div className="text-muted-foreground *:[a]:hover:text-primary text-center text-xs text-balance *:[a]:underline *:[a]:underline-offset-4">
        {t('termsText')} <a href="#">{t('termsOfService')}</a> {t('and')}{' '}
        <a href="#">{t('privacyPolicy')}</a>
        {t('period')}
      </div>
    </div>
  )
}
