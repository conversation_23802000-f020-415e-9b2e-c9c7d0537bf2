# 新闻页面业务文档

## 1. 系统架构概览

### 1.1 核心组件关系
```
AppSidebar (侧边栏)
├── ContentWidget (资讯组件) ──────┐
└── ContentWidget (招投标组件)      │
                                  │
                                  ▼ 路由跳转
NewsPage (新闻主页面)
├── NewsList (新闻列表)
│   └── NewsDetail (新闻详情)
└── RightPanel (右侧详情面板)
    └── GlobalDetailPanel
```

### 1.2 文件结构
- **主页面**: `src/app/(dashboard)/news/page.tsx`
- **侧边栏**: `src/components/common/app-sidebar.tsx`
- **资讯组件**: `src/components/common/content-widget.tsx`
- **新闻列表**: `src/components/news/NewsList.tsx`
- **新闻详情**: `src/components/news/NewsDetail.tsx`
- **右侧面板**: `src/components/news/RightPanel.tsx`
- **API服务**: `src/lib/news-service.ts`
- **类型定义**: `src/types/news.ts`

## 2. 侧边栏业务逻辑 (ContentWidget)

### 2.1 组件配置
```typescript
const WIDGET_CONFIG = {
  news: {
    title: '每日资讯榜 Top 10',
    route: '/news',
    tooltipTitle: '每日资讯榜TOP10'
  },
  tender: {
    title: '每日招投标榜 Top 10',
    route: '/tender', 
    tooltipTitle: '每日招投标榜TOP10'
  }
}
```

### 2.2 话题管理功能

#### 2.2.1 话题列表加载
- **新闻话题**: 调用 `fetchSubscribedTopics()` API获取用户订阅的话题
- **招投标话题**: 使用占位符数据（目前为静态数据）
- **数据格式**: `{[topic_id: string]: topic_name: string}`

#### 2.2.2 话题操作
1. **查看话题**: 点击话题名称 → 跳转到 `/news?topicId=${topicId}`
2. **添加话题**: 
   - 点击"+"按钮 → 打开 `UnifiedDialog`
   - 输入话题描述 → 调用 `createTopic()` API
   - 创建成功后自动跳转到新话题页面
3. **删除话题**: 
   - 右键点击话题 → 显示上下文菜单
   - 点击删除 → 调用 `unsubscribeTopic()` API
   - 删除后自动跳转到第一个剩余话题

### 2.3 侧边栏状态管理

#### 2.3.1 展开状态 (isCollapsed: false)
- 显示完整的话题列表
- 支持话题的增删操作
- 显示话题的完整名称

#### 2.3.2 收起状态 (isCollapsed: true)
- 只显示Logo图标
- 话题显示为首字母图标
- 鼠标悬停显示完整话题名称

## 3. 新闻主页面逻辑

### 3.1 URL参数处理
```typescript
const topicId = searchParams.get('topicId')
const category = searchParams.get('category') // 向后兼容
```

### 3.2 页面状态管理
```typescript
// 核心状态
const [selectedMainArticleId, setSelectedMainArticleId] = useState<string | null>(null)
const [isRightPanelOpen, setIsRightPanelOpen] = useState(false)
const [newsArticles, setNewsArticles] = useState<NewsItem[]>([])
const [currentTopicName, setCurrentTopicName] = useState<string>('')
```

### 3.3 数据加载流程
1. **话题列表加载**: `fetchSubscribedTopics()` → 获取话题名称映射
2. **新闻数据加载**: `fetchTopicNewsList({topic_id, page, page_size})` → 获取话题下的新闻列表
3. **话题名称设置**: 根据topicId从话题列表中匹配显示名称

### 3.4 页面布局适配
```typescript
// 根据右侧面板状态调整主内容区域宽度
className={`mx-auto w-full h-full flex flex-col transition-all duration-300 ease-in-out ${
  isRightPanelOpen ? 'max-w-4xl' : 'max-w-6xl'
}`}
```

## 4. 文章ID选择和切换机制

### 4.1 文章选择逻辑 (NewsList组件)
```typescript
const handleArticleClick = (articleId: string) => {
  // 如果点击的是当前选中的新闻，则收起详情（传递null）
  // 否则选中新的新闻
  const newSelectedId = selectedArticleId === articleId ? null : articleId
  onArticleClick(newSelectedId)
}
```

### 4.2 文章ID获取
```typescript
// 从NewsItem中获取唯一标识符
const getArticleId = (article: NewsItem): string => {
  return article._id  // 使用MongoDB的_id作为唯一标识
}
```

### 4.3 选中状态管理
- **视觉反馈**: 选中的文章背景色变为 `bg-gray-100`
- **详情展示**: 选中文章下方展开显示 `NewsDetail` 组件
- **右侧面板**: 选中文章时自动打开右侧面板显示原文详情

### 4.4 文章切换动画
```typescript
// 详情展开/收起动画
className={cn(
  "overflow-hidden transition-all duration-300 ease-in-out",
  isSelected ? "max-h-[1000px] opacity-100" : "max-h-0 opacity-0"
)}
```

## 5. 组件间交互流程

### 5.1 侧边栏 → 新闻页面
```
ContentWidget (点击话题)
    ↓ router.push(`/news?topicId=${topicId}`)
NewsPage (接收topicId参数)
    ↓ useEffect监听topicId变化
loadNews() (调用API获取新闻列表)
```

### 5.2 新闻列表 → 详情展示
```
NewsList (点击文章)
    ↓ onArticleClick(articleId)
NewsPage (更新selectedMainArticleId)
    ↓ 传递articleId给子组件
NewsDetail + RightPanel (同时显示详情)
```

### 5.3 右侧面板控制
```
NewsPage
├── selectedMainArticleId ─────→ RightPanel (文章ID)
├── isRightPanelOpen ──────────→ RightPanel (显示状态)
├── newsArticles ─────────────→ RightPanel (文章数据)
└── handleRightPanelClose ────→ RightPanel (关闭回调)
```

## 6. API接口说明

### 6.1 核心接口

#### 获取订阅话题列表
```typescript
GET /information_news/subscribed_topics
响应格式: {
  code: 200,
  success: true,
  message: "success",
  data: {
    "1": "人工智能发展",
    "3": "新能源汽车",
    // ...
  }
}
```

#### 获取话题新闻列表
```typescript
POST /information_news/topic_news_list
请求参数: {
  topic_id: number,
  page?: number,
  page_size?: number
}
响应格式: {
  code: 200,
  success: true,
  data: {
    news_list: NewsItem[],
    total: number,
    page: number,
    page_size: number,
    total_pages: number
  }
}
```

#### 创建新话题
```typescript
POST /information_news/create_topic
请求参数: {
  description: string,
  input_text: string
}
响应格式: {
  code: 200,
  success: true,
  data: {
    topic_id: string,
    topic_name: string,
    message?: string
  }
}
```

#### 取消订阅话题
```typescript
DELETE /information_news/unsubscribe_topic/{topic_id}
响应格式: {
  code: 200,
  success: true,
  message: "success"
}
```

### 6.2 数据模型

#### NewsItem (新闻项)
```typescript
interface NewsItem {
  _id: string                    // MongoDB唯一标识
  url: string                    // 原文链接
  country: string                // 国家/地区
  title: string                  // 新闻标题
  web_site: string              // 来源网站
  language: string               // 语言
  key_points: string[]           // 关键点列表
  summary: string                // 摘要
  content: string                // 正文内容
  news_type: string              // 新闻类型
  first_level_industry: string   // 一级行业
  second_level_industry: string  // 二级行业
  publish_date: string           // 发布日期
  create_date: string            // 创建日期
  update_date: string            // 更新日期
}
```

## 7. 路由和状态管理

### 7.1 URL参数映射
- `/news` - 显示新闻主页，没有选择话题
- `/news?topicId=1` - 显示话题ID为1的新闻列表
- `/news?category=1` - 向后兼容的参数名（已废弃）

### 7.2 状态同步机制
```typescript
// URL参数变化 → 重新加载新闻数据
useEffect(() => {
  loadNews()
}, [topicId, category])

// 话题数据变化 → 更新话题名称显示
useEffect(() => {
  if (topicId || category) {
    const currentTopicId = topicId || category
    const topicName = topics[currentTopicId]
    setCurrentTopicName(topicName || `话题 ${currentTopicId}`)
  }
}, [topicId, category, topics])
```

### 7.3 右侧面板状态持久化
```typescript
// 保持最后选中的文章ID，即使面板关闭也记住内容
const [lastSelectedArticleId, setLastSelectedArticleId] = useState<string | null>(null)

useEffect(() => {
  if (articleId) {
    setLastSelectedArticleId(articleId)
  }
}, [articleId])

// 使用最后选中的文章ID来显示内容
const effectiveArticleId = articleId || lastSelectedArticleId
```

## 8. 用户交互优化

### 8.1 加载状态处理
- **侧边栏话题加载**: 显示"加载中..."文本
- **新闻列表加载**: 显示加载指示器
- **创建话题**: 按钮显示loading状态，禁用交互

### 8.2 错误处理
- **API调用失败**: 显示错误信息，提供重试机制
- **无数据状态**: 显示"请选择一个话题查看新闻"提示
- **话题创建失败**: 显示具体错误信息

### 8.3 响应式设计
- **侧边栏收起**: 话题显示为首字母图标
- **主内容区域**: 根据右侧面板状态动态调整宽度
- **移动端适配**: 保持良好的触摸交互体验

## 9. 技术要点

### 9.1 性能优化
- 使用 `React.useCallback` 缓存API调用函数
- 条件渲染减少不必要的组件更新
- 图片懒加载和尺寸优化

### 9.2 类型安全
- 严格的TypeScript类型定义
- API响应数据验证和类型转换
- 组件Props接口约束

### 9.3 用户体验
- 平滑的过渡动画效果
- 直观的视觉反馈
- 一致的交互模式

---

*文档版本: v1.0*  
*更新日期: 2025-07-25*  
*维护者: 开发团队*