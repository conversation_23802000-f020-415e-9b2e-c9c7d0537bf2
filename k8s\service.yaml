apiVersion: v1
kind: Service
metadata:
  name: specific-ai-ui-service
  namespace: ovs
  labels:
    app: specific-ai-ui
    app.kubernetes.io/name: specific-ai-ui
    app.kubernetes.io/component: service
    app.kubernetes.io/version: '1.0.0'
    app.kubernetes.io/managed-by: 'kubectl'
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: 'nlb' # 如果使用 AWS
spec:
  type: ClusterIP
  ports:
    - port: 8230
      targetPort: 8230
      protocol: TCP
      name: http
  selector:
    app: specific-ai-ui
  sessionAffinity: None # 明确指定会话亲和性

---
apiVersion: v1
kind: Service
metadata:
  name: specific-ai-ui-nodeport
  namespace: ovs
  labels:
    app: specific-ai-ui
    app.kubernetes.io/name: specific-ai-ui
    app.kubernetes.io/component: service
    app.kubernetes.io/version: '1.0.0'
    app.kubernetes.io/managed-by: 'kubectl'
  annotations:
    service.beta.kubernetes.io/external-traffic: 'OnlyLocal' # 优化外部流量处理
spec:
  type: NodePort
  ports:
    - port: 8230
      targetPort: 8230
      nodePort: 30989
      protocol: TCP
      name: http
  selector:
    app: specific-ai-ui
  sessionAffinity: None
  externalTrafficPolicy: Local # 保持源 IP

