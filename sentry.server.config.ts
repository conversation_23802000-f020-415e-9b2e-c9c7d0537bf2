// This file configures the initialization of Sentry on the server.
// The config you add here will be used whenever the server handles a request.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import * as Sentry from '@sentry/nextjs'

const SENTRY_DSN = process.env.SENTRY_DSN || process.env.NEXT_PUBLIC_SENTRY_DSN

if (SENTRY_DSN) {
  Sentry.init({
    // dsn: 'https://<EMAIL>/4509616428089349',
    dsn: SENTRY_DSN,

    // Define how likely traces are sampled. Adjust this value in production, or use tracesSampler for greater control.
    tracesSampleRate: process.env.NODE_ENV === 'development' ? 1.0 : 0.2,

    // Setting this option to true will print useful information to the console while you're setting up Sentry.
    debug: process.env.NODE_ENV === 'development',

    // Optionally, add context to all captured events.
    initialScope: {
      tags: {
        'next.runtime': 'nodejs',
      },
    },

    // Do not send events in the dev environment
    beforeSend(event) {
      if (process.env.NEXT_PUBLIC_APP_ENV === 'dev') {
        return null
      }
      return event
    },
  })
}
