/**
 * UI状态管理 Store
 * 
 * 功能：用户输入状态、界面交互状态、视觉反馈管理
 * 依赖：无外部依赖，纯UI状态管理
 * 性能：轻量级状态管理，专注于UI交互优化
 * 
 * 数据流：User Interaction -> UI Store -> UI Components
 */

import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'
import { subscribeWithSelector } from 'zustand/middleware'

// ============================================================================
// UI State Interfaces
// ============================================================================

export interface UserInput {
  content: string
  isSubmitting: boolean
  isTyping: boolean
  lastUpdated: Date
}

export interface MessageUIState {
  id: string
  status: 'sending' | 'sent' | 'delivered' | 'failed'
  isVisible: boolean
  isHighlighted: boolean
  accumulatedText?: string
  lastUpdated: Date
}

export interface ScrollState {
  isAtBottom: boolean
  unreadCount: number
  lastScrollPosition: number
  autoScrollEnabled: boolean
}

export interface InteractionState {
  // 键盘和输入状态
  isInputFocused: boolean
  selectedMessageId: string | undefined
  
  // 对话框和弹窗状态
  showShortcutsHelp: boolean
  showSettings: boolean
  showConnectionStatus: boolean
  
  // 功能开关状态
  thinkMode: boolean
  deepSearch: boolean
  
  // 移动端状态
  isMobileView: boolean
  isKeyboardVisible: boolean
  viewportHeight: number
}

export interface NotificationState {
  // 系统通知
  notifications: Array<{
    id: string
    type: 'info' | 'success' | 'warning' | 'error'
    title: string
    message: string
    timestamp: Date
    autoClose?: boolean
    duration?: number
  }>
  
  // Toast消息
  toasts: Array<{
    id: string
    type: 'success' | 'error' | 'info'
    message: string
    timestamp: Date
    duration: number
  }>
}

// ============================================================================
// UI State Interface
// ============================================================================

export interface UIState {
  // 用户输入状态
  userInput: UserInput
  
  // 消息UI状态映射
  messageUIStates: Record<string, MessageUIState>
  
  // 滚动状态
  scrollState: ScrollState
  
  // 交互状态
  interactionState: InteractionState
  
  // 通知状态
  notificationState: NotificationState
}

// ============================================================================
// UI Actions Interface
// ============================================================================

export interface UIActions {
  // 用户输入管理
  updateUserInput: (content: string) => void
  setIsSubmitting: (isSubmitting: boolean) => void
  setIsTyping: (isTyping: boolean) => void
  clearUserInput: () => void
  setUserInput: (input: Partial<UserInput>) => void

  // 消息UI状态管理
  updateMessageUIState: (messageId: string, updates: Partial<MessageUIState>) => void
  setMessageStatus: (messageId: string, status: MessageUIState['status']) => void
  highlightMessage: (messageId: string, highlight: boolean) => void
  setMessageVisibility: (messageId: string, visible: boolean) => void
  clearMessageUIState: (messageId: string) => void

  // 滚动状态管理
  updateScrollState: (updates: Partial<ScrollState>) => void
  setIsAtBottom: (isAtBottom: boolean) => void
  setUnreadCount: (count: number) => void
  setAutoScrollEnabled: (enabled: boolean) => void

  // 交互状态管理
  setInputFocused: (focused: boolean) => void
  setSelectedMessage: (messageId: string | undefined) => void
  toggleShortcutsHelp: () => void
  toggleSettings: () => void
  toggleConnectionStatus: () => void
  setThinkMode: (enabled: boolean) => void
  setDeepSearch: (enabled: boolean) => void

  // 移动端状态管理
  setMobileView: (isMobile: boolean) => void
  setKeyboardVisible: (visible: boolean) => void
  setViewportHeight: (height: number) => void

  // 通知管理
  addNotification: (notification: Omit<NotificationState['notifications'][0], 'id' | 'timestamp'>) => string
  removeNotification: (id: string) => void
  clearNotifications: () => void
  addToast: (toast: Omit<NotificationState['toasts'][0], 'id' | 'timestamp'>) => string
  removeToast: (id: string) => void
  clearToasts: () => void

  // 批量操作
  resetUIState: () => void
  getUISnapshot: () => UIState
}

export type UIStore = UIState & UIActions

// ============================================================================
// Utility Functions
// ============================================================================

/**
 * 生成唯一ID
 */
const generateId = (): string => {
  return `ui-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
}

/**
 * 创建初始用户输入状态
 */
const createInitialUserInput = (): UserInput => ({
  content: '',
  isSubmitting: false,
  isTyping: false,
  lastUpdated: new Date(),
})

/**
 * 创建初始滚动状态
 */
const createInitialScrollState = (): ScrollState => ({
  isAtBottom: true,
  unreadCount: 0,
  lastScrollPosition: 0,
  autoScrollEnabled: true,
})

/**
 * 创建初始交互状态
 */
const createInitialInteractionState = (): InteractionState => ({
  isInputFocused: false,
  selectedMessageId: undefined,
  showShortcutsHelp: false,
  showSettings: false,
  showConnectionStatus: false,
  thinkMode: false,
  deepSearch: false,
  isMobileView: false,
  isKeyboardVisible: false,
  viewportHeight: typeof window !== 'undefined' ? window.innerHeight : 800,
})

/**
 * 创建初始通知状态
 */
const createInitialNotificationState = (): NotificationState => ({
  notifications: [],
  toasts: [],
})

// ============================================================================
// UI Store Implementation
// ============================================================================

export const useUIStore = create<UIStore>()(
  subscribeWithSelector(
    immer((set, get) => ({
      // ============================================================================
      // 初始状态
      // ============================================================================
      userInput: createInitialUserInput(),
      messageUIStates: {},
      scrollState: createInitialScrollState(),
      interactionState: createInitialInteractionState(),
      notificationState: createInitialNotificationState(),

      // ============================================================================
      // 用户输入管理
      // ============================================================================

      updateUserInput: (content: string) => {
        set(state => {
          state.userInput.content = content
          state.userInput.lastUpdated = new Date()
          
          // 如果内容变化，设置为正在输入
          if (content.length > 0 && !state.userInput.isTyping) {
            state.userInput.isTyping = true
          }
        })
      },

      setIsSubmitting: (isSubmitting: boolean) => {
        set(state => {
          state.userInput.isSubmitting = isSubmitting
          state.userInput.lastUpdated = new Date()
          
          // 提交时停止输入状态
          if (isSubmitting) {
            state.userInput.isTyping = false
          }
        })
      },

      setIsTyping: (isTyping: boolean) => {
        set(state => {
          state.userInput.isTyping = isTyping
          state.userInput.lastUpdated = new Date()
        })
      },

      clearUserInput: () => {
        set(state => {
          state.userInput = createInitialUserInput()
        })
      },

      setUserInput: (input: Partial<UserInput>) => {
        set(state => {
          Object.assign(state.userInput, input)
          state.userInput.lastUpdated = new Date()
        })
      },

      // ============================================================================
      // 消息UI状态管理
      // ============================================================================

      updateMessageUIState: (messageId: string, updates: Partial<MessageUIState>) => {
        set(state => {
          const existing = state.messageUIStates[messageId]
          if (existing) {
            Object.assign(existing, updates)
            existing.lastUpdated = new Date()
          } else {
            state.messageUIStates[messageId] = {
              id: messageId,
              status: 'sent',
              isVisible: true,
              isHighlighted: false,
              lastUpdated: new Date(),
              ...updates,
            }
          }
        })
      },

      setMessageStatus: (messageId: string, status: MessageUIState['status']) => {
        get().updateMessageUIState(messageId, { status })
      },

      highlightMessage: (messageId: string, highlight: boolean) => {
        get().updateMessageUIState(messageId, { isHighlighted: highlight })
      },

      setMessageVisibility: (messageId: string, visible: boolean) => {
        get().updateMessageUIState(messageId, { isVisible: visible })
      },

      clearMessageUIState: (messageId: string) => {
        set(state => {
          delete state.messageUIStates[messageId]
        })
      },

      // ============================================================================
      // 滚动状态管理
      // ============================================================================

      updateScrollState: (updates: Partial<ScrollState>) => {
        set(state => {
          Object.assign(state.scrollState, updates)
        })
      },

      setIsAtBottom: (isAtBottom: boolean) => {
        set(state => {
          state.scrollState.isAtBottom = isAtBottom
          // 如果滚动到底部，清零未读计数
          if (isAtBottom) {
            state.scrollState.unreadCount = 0
          }
        })
      },

      setUnreadCount: (count: number) => {
        set(state => {
          state.scrollState.unreadCount = Math.max(0, count)
        })
      },

      setAutoScrollEnabled: (enabled: boolean) => {
        set(state => {
          state.scrollState.autoScrollEnabled = enabled
        })
      },

      // ============================================================================
      // 交互状态管理
      // ============================================================================

      setInputFocused: (focused: boolean) => {
        set(state => {
          state.interactionState.isInputFocused = focused
        })
      },

      setSelectedMessage: (messageId: string | undefined) => {
        set(state => {
          state.interactionState.selectedMessageId = messageId
        })
      },

      toggleShortcutsHelp: () => {
        set(state => {
          state.interactionState.showShortcutsHelp = !state.interactionState.showShortcutsHelp
        })
      },

      toggleSettings: () => {
        set(state => {
          state.interactionState.showSettings = !state.interactionState.showSettings
        })
      },

      toggleConnectionStatus: () => {
        set(state => {
          state.interactionState.showConnectionStatus = !state.interactionState.showConnectionStatus
        })
      },

      setThinkMode: (enabled: boolean) => {
        set(state => {
          state.interactionState.thinkMode = enabled
        })
      },

      setDeepSearch: (enabled: boolean) => {
        set(state => {
          state.interactionState.deepSearch = enabled
        })
      },

      // ============================================================================
      // 移动端状态管理
      // ============================================================================

      setMobileView: (isMobile: boolean) => {
        set(state => {
          state.interactionState.isMobileView = isMobile
        })
      },

      setKeyboardVisible: (visible: boolean) => {
        set(state => {
          state.interactionState.isKeyboardVisible = visible
        })
      },

      setViewportHeight: (height: number) => {
        set(state => {
          state.interactionState.viewportHeight = height
        })
      },

      // ============================================================================
      // 通知管理
      // ============================================================================

      addNotification: (notification: Omit<NotificationState['notifications'][0], 'id' | 'timestamp'>) => {
        const id = generateId()
        set(state => {
          state.notificationState.notifications.push({
            id,
            timestamp: new Date(),
            ...notification,
          })
        })
        
        // 自动清除通知
        if (notification.autoClose !== false) {
          const duration = notification.duration || 5000
          setTimeout(() => {
            get().removeNotification(id)
          }, duration)
        }
        
        return id
      },

      removeNotification: (id: string) => {
        set(state => {
          state.notificationState.notifications = state.notificationState.notifications.filter(
            n => n.id !== id
          )
        })
      },

      clearNotifications: () => {
        set(state => {
          state.notificationState.notifications = []
        })
      },

      addToast: (toast: Omit<NotificationState['toasts'][0], 'id' | 'timestamp'>) => {
        const id = generateId()
        set(state => {
          state.notificationState.toasts.push({
            id,
            timestamp: new Date(),
            ...toast,
          })
        })
        
        // 自动清除toast
        setTimeout(() => {
          get().removeToast(id)
        }, toast.duration || 3000)
        
        return id
      },

      removeToast: (id: string) => {
        set(state => {
          state.notificationState.toasts = state.notificationState.toasts.filter(
            t => t.id !== id
          )
        })
      },

      clearToasts: () => {
        set(state => {
          state.notificationState.toasts = []
        })
      },

      // ============================================================================
      // 批量操作
      // ============================================================================

      resetUIState: () => {
        set(state => {
          state.userInput = createInitialUserInput()
          state.messageUIStates = {}
          state.scrollState = createInitialScrollState()
          state.interactionState = createInitialInteractionState()
          state.notificationState = createInitialNotificationState()
        })
        console.log('🔄 UI State reset')
      },

      getUISnapshot: () => {
        return { ...get() }
      },
    }))
  )
)

// ============================================================================
// UI Hooks - 专门的UI状态管理Hooks
// ============================================================================

export const useUserInput = () => {
  const userInput = useUIStore(state => state.userInput)
  const updateUserInput = useUIStore(state => state.updateUserInput)
  const setIsSubmitting = useUIStore(state => state.setIsSubmitting)
  const setIsTyping = useUIStore(state => state.setIsTyping)
  const clearUserInput = useUIStore(state => state.clearUserInput)

  return {
    userInput,
    updateUserInput,
    setIsSubmitting,
    setIsTyping,
    clearUserInput,
  }
}

export const useScrollState = () => {
  const scrollState = useUIStore(state => state.scrollState)
  const updateScrollState = useUIStore(state => state.updateScrollState)
  const setIsAtBottom = useUIStore(state => state.setIsAtBottom)
  const setUnreadCount = useUIStore(state => state.setUnreadCount)
  const setAutoScrollEnabled = useUIStore(state => state.setAutoScrollEnabled)

  return {
    scrollState,
    updateScrollState,
    setIsAtBottom,
    setUnreadCount,
    setAutoScrollEnabled,
  }
}

export const useInteractionState = () => {
  const interactionState = useUIStore(state => state.interactionState)
  const setInputFocused = useUIStore(state => state.setInputFocused)
  const setSelectedMessage = useUIStore(state => state.setSelectedMessage)
  const toggleShortcutsHelp = useUIStore(state => state.toggleShortcutsHelp)
  const toggleSettings = useUIStore(state => state.toggleSettings)
  const setThinkMode = useUIStore(state => state.setThinkMode)
  const setDeepSearch = useUIStore(state => state.setDeepSearch)

  return {
    interactionState,
    setInputFocused,
    setSelectedMessage,
    toggleShortcutsHelp,
    toggleSettings,
    setThinkMode,
    setDeepSearch,
  }
}

export const useNotifications = () => {
  const notificationState = useUIStore(state => state.notificationState)
  const addNotification = useUIStore(state => state.addNotification)
  const removeNotification = useUIStore(state => state.removeNotification)
  const clearNotifications = useUIStore(state => state.clearNotifications)
  const addToast = useUIStore(state => state.addToast)
  const removeToast = useUIStore(state => state.removeToast)
  const clearToasts = useUIStore(state => state.clearToasts)

  return {
    notifications: notificationState.notifications,
    toasts: notificationState.toasts,
    addNotification,
    removeNotification,
    clearNotifications,
    addToast,
    removeToast,
    clearToasts,
  }
}

export const useMobileState = () => {
  const { isMobileView, isKeyboardVisible, viewportHeight } = useUIStore(state => state.interactionState)
  const setMobileView = useUIStore(state => state.setMobileView)
  const setKeyboardVisible = useUIStore(state => state.setKeyboardVisible)
  const setViewportHeight = useUIStore(state => state.setViewportHeight)

  return {
    isMobileView,
    isKeyboardVisible,
    viewportHeight,
    setMobileView,
    setKeyboardVisible,
    setViewportHeight,
  }
}

// 清理函数
export const cleanupUIStore = () => {
  const { resetUIState } = useUIStore.getState()
  resetUIState()
  console.log('🗑️ UI Store 清理完成')
}