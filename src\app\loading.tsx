'use client'

import React, { useState } from 'react'
import { GooeyText } from '@/components/ui/gooey-text-morphing'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'


export default function Loading({ className }: { className?: string }) {

  return (
    <div className={cn('min-h-screen bg-gray-50 flex flex-col items-center justify-center p-4', className)}>
      {/* 原有的加载文本 */}
      <div className="flex items-center justify-center gap-1 mb-8">
        <GooeyText
          texts={['SpecificAI', 'Day 1 Global']}
          morphTime={1}
          cooldownTime={0.25}
          className="font-bold"
        />
      </div>
    </div>
  )
}
