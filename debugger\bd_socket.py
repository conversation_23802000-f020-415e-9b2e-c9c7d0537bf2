import asyncio
import logging
from typing import Dict, Any
import socketio
from sqlalchemy.ext.asyncio import AsyncSession

from services.socketio_manager import socketio_manager
from services.socketio_client import socketio_client
from services.taskd_client import get_taskd_client
from common.postgresql import get_async_db
from schemas.websocket import BaseWebSocketMessage

logger = logging.getLogger(__name__)

class GroupChatHandler:
    def __init__(self):
        self.sio = socketio_manager.sio
        self.backend_client = socketio_client
        self._register_events()
        self._register_backend_handlers()
    
    def _register_events(self):
        """注册Socket.IO事件处理器"""
        
        @self.sio.event
        async def connect(sid, environ, auth):
            """处理客户端连接"""
            logger.info(f"收到新的连接请求 - 会话ID: {sid}")
            logger.info(f"客户端环境变量: {list(environ.keys()) if environ else '无'}")
            logger.info(f"客户端IP地址: {environ.get('REMOTE_ADDR', '未知') if environ else '未知'}")
            logger.info(f"收到的认证参数: {auth}")
            
            if not auth or not all(k in auth for k in ['user_id', 'group_chat_id', 'organization_id']):
                missing_params = []
                if not auth:
                    missing_params = ['user_id', 'group_chat_id', 'organization_id']
                else:
                    for k in ['user_id', 'group_chat_id', 'organization_id']:
                        if k not in auth:
                            missing_params.append(k)
                logger.error(f"客户端 {sid} 缺少必需的认证参数: {missing_params}")
                logger.error(f"提供的认证信息: {auth}")
                return False
            
            try:
                logger.info(f"开始为会话ID {sid} 处理连接")
                logger.info(f"用户ID: {auth['user_id']}, 群聊ID: {auth['group_chat_id']}, 组织ID: {auth['organization_id']}")
                
                # 获取数据库会话
                db_gen = get_async_db()
                db = await db_gen.__anext__()
                
                # 记录连接到数据库
                await socketio_manager.connect_with_db(
                    sid, 
                    auth['user_id'], 
                    auth['group_chat_id'], 
                    auth['organization_id'], 
                    db
                )
                
                # 存储会话信息
                await self.sio.save_session(sid, {
                    'user_id': auth['user_id'],
                    'group_chat_id': auth['group_chat_id'],
                    'organization_id': auth['organization_id']
                })
                
                # 加入房间
                await self.sio.enter_room(sid, auth['group_chat_id'])
         
                # 发送连接成功确认
                logger.debug(f"正在向会话ID {sid} 发送连接确认")
                await self.sio.emit('message', {
                    'status': 'connected',
                    'room': auth['group_chat_id'],
                    'user_id': auth['user_id']
                }, room=sid)
                logger.debug(f"连接确认已发送给会话ID {sid}")
                
                logger.info(f"✅ 客户端 {sid} 成功连接到房间 {auth['group_chat_id']} - 用户: {auth['user_id']}, 组织: {auth['organization_id']}")
                return True
                
            except Exception as e:
                logger.error(f"❌ 会话ID {sid} 连接失败: {type(e).__name__}: {e}")
                logger.error(f"失败时的认证数据: {auth}")
                import traceback
                logger.error(f"完整错误堆栈: {traceback.format_exc()}")
                return False
        
        @self.sio.event
        async def disconnect(sid):
            """处理客户端断开连接"""
            try:
                session = await self.sio.get_session(sid)
                if session:
                    user_id = session.get('user_id')
                    group_chat_id = session.get('group_chat_id')
                    
                    # 获取数据库会话并清理
                    db_gen = get_async_db()
                    db = await db_gen.__anext__()
                    await socketio_manager.disconnect_with_db(sid, db)
                    
                    logger.info(f"Client {sid} disconnected from room {group_chat_id}")
                    
            except Exception as e:
                logger.error(f"Disconnect cleanup failed for {sid}: {e}")
        
        @self.sio.event
        async def user_message(sid, data):
            """处理用户消息"""
            try:
                session = await self.sio.get_session(sid)
                if not session:
                    await self.sio.emit('error', {'message': 'Session not found'}, room=sid)
                    return
                
                # 验证消息格式
                if not isinstance(data, dict) or 'message' not in data:
                    await self.sio.emit('error', {'message': 'Invalid message format'}, room=sid)
                    return
                
                # 创建消息对象
                message = BaseWebSocketMessage(
                    group_chat_id=session['group_chat_id'],
                    session_id=data.get('session_id', sid),
                    status=True,
                    message=data['message'],
                    payload=data.get('payload', {})
                )
                
                # 开始处理流程
                await self._handle_message_flow(sid, message)
                
            except Exception as e:
                logger.error(f"Error handling user message from {sid}: {e}")
                await self.sio.emit('error', {'message': 'Message processing failed'}, room=sid)
        
        @self.sio.event
        async def user_interaction(sid, data):
            """处理用户交互响应"""
            try:
                session = await self.sio.get_session(sid)
                if not session:
                    await self.sio.emit('error', {'message': 'Session not found'}, room=sid)
                    return
                
                # 处理用户选择
                step = data.get('step')
                selection = data.get('selection')
                
                logger.info(f"User {session['user_id']} interaction: {step} -> {selection}")
                
                # 根据步骤处理用户选择
                if step == 'opportunity_dimension_selection':
                    await self._handle_opportunity_analysis(sid, session, selection)
                elif step == 'analysis_dimension_selection':
                    await self._handle_deep_analysis(sid, session, selection)
                else:
                    await self.sio.emit('error', {'message': f'Unknown interaction step: {step}'}, room=sid)
                
            except Exception as e:
                logger.error(f"Error handling user interaction from {sid}: {e}")
                await self.sio.emit('error', {'message': 'Interaction processing failed'}, room=sid)
    
    async def _handle_message_flow(self, sid: str, message: BaseWebSocketMessage):
        """处理消息流程"""
        try:
            # 步骤1: 意图识别（同步等待）
            await self.sio.emit('processing_status', {
                'step': 'intent_recognition',
                'message': '正在识别意图...',
                'status': 'processing'
            }, room=sid)
            
            intent = await self._recognize_intent(message)
            
            if intent == "failed":
                await self.sio.emit('error', {
                    'message': '意图识别失败，降级为闲聊模式'
                }, room=sid)
                await self._handle_casual_chat_flow(sid, message)
                return
            
            # 通知用户意图识别完成
            await self.sio.emit('processing_status', {
                'step': 'intent_recognition_completed',
                'message': f'意图识别完成: {intent}',
                'status': 'completed',
                'data': {'intent': intent}
            }, room=sid)
            
            # 步骤2: 根据意图启动相应业务流程
            await self.sio.emit('processing_status', {
                'step': 'business_service_connection',
                'message': '正在连接业务服务...',
                'status': 'processing'
            }, room=sid)
            
            # 根据意图启动相应流程
            await self._start_business_flow(sid, intent, message)
            
        except Exception as e:
            logger.error(f"Message flow error for {sid}: {e}")
            await self.sio.emit('error', {'message': 'Processing failed'}, room=sid)
    
    async def _recognize_intent(self, message: BaseWebSocketMessage) -> str:
        """识别用户意图（同步等待，但提供进度反馈）"""
        try:
            # 使用group_chat_id作为room发送进度更新
            room = message.group_chat_id
            
            # 发送详细的进度更新
            await self.sio.emit('processing_status', {
                'step': 'intent_analysis_start',
                'message': '正在分析消息内容...',
                'status': 'processing',
                'progress': 0.3
            }, room=room)
            
            taskd_client = await get_taskd_client()
            messages = [{'role': 'user', 'content': message.message}]
            
            # 发送调用API的进度
            await self.sio.emit('processing_status', {
                'step': 'intent_api_call',
                'message': '正在调用意图识别服务...',
                'status': 'processing',
                'progress': 0.7
            }, room=room)
            
            # 同步调用意图识别
            result = await taskd_client.recognize_intent(
                messages=messages,
                user_id=message.user_id,
                organization_id=message.organization_id
            )
            
            logger.info(f"Intent recognized: {result.intent} (confidence: {result.confidence})")
            return result.intent
            
        except Exception as e:
            logger.error(f"Intent recognition failed: {e}")
            return "failed"
    
    async def _start_business_flow(self, sid: str, intent: str, message: BaseWebSocketMessage):
        """根据意图启动相应的业务流程"""
        if intent == "business_news":
            await self._handle_business_news_flow(sid, message)
        elif intent == "bidding_analysis":
            await self._handle_bidding_analysis_flow(sid, message)
        elif intent == "chat_summary":
            await self._handle_chat_summary_flow(sid, message)
        else:
            await self._handle_casual_chat_flow(sid, message)
    
    async def _handle_business_news_flow(self, sid: str, message: BaseWebSocketMessage):
        """处理商机新闻流程 - 转发到后端服务"""
        try:
            # 确保与后端服务连接
            if not self.backend_client.is_connected():
                session = await self.sio.get_session(sid)
                auth_info = {
                    'user_id': session['user_id'],
                    'group_chat_id': session['group_chat_id'],
                    'organization_id': session['organization_id']
                }
                await self.backend_client.connect_to_backend(auth_info)
            
            # 转发消息到后端服务处理
            success = await self.backend_client.forward_message(message)
            
            if not success:
                await self.sio.emit('error', {
                    'message': '无法连接到后端分析服务，请稍后重试'
                }, room=sid)
                
        except Exception as e:
            logger.error(f"商机新闻流程处理错误 {sid}: {e}")
            await self.sio.emit('error', {
                'message': '商机新闻分析服务暂时不可用'
            }, room=sid)
    
    async def _handle_bidding_analysis_flow(self, sid: str, message: BaseWebSocketMessage):
        """处理招投标分析流程"""
        await self.sio.emit('processing_status', {
            'step': 'bidding_analysis',
            'message': '正在分析招投标信息...',
            'status': 'processing'
        }, room=sid)
        
        await asyncio.sleep(2)
        
        await self.sio.emit('analysis_result', {
            'step': 'bidding_analysis_complete',
            'message': '招投标分析完成',
            'status': 'completed',
            'data': {
                'analysis_type': 'bidding',
                'result': '找到3个相关的招投标项目'
            }
        }, room=sid)
    
    async def _handle_chat_summary_flow(self, sid: str, message: BaseWebSocketMessage):
        """处理聊天总结流程"""
        await self.sio.emit('processing_status', {
            'step': 'chat_summary',
            'message': '正在总结聊天内容...',
            'status': 'processing'
        }, room=sid)
        
        await asyncio.sleep(1)
        
        await self.sio.emit('summary_result', {
            'step': 'chat_summary_complete',
            'message': '聊天总结完成',
            'status': 'completed',
            'data': {
                'summary': '本次对话主要讨论了业务分析需求'
            }
        }, room=sid)
    
    async def _handle_casual_chat_flow(self, sid: str, message: BaseWebSocketMessage):
        """处理闲聊流程"""
        await self.sio.emit('chat_response', {
            'step': 'casual_response',
            'message': '您好！有什么可以帮助您的吗？',
            'status': 'completed'
        }, room=sid)
    
    async def _handle_opportunity_analysis(self, sid: str, session: Dict[str, Any], selection: str):
        """处理机会分析"""
        await self.sio.emit('processing_status', {
            'step': 'opportunity_analysis',
            'message': f'正在进行{selection}分析...',
            'status': 'processing'
        }, room=sid)
        
        await asyncio.sleep(2)
        
        # 需要用户选择分析维度
        await self.sio.emit('interaction_required', {
            'step': 'analysis_dimension_selection',
            'message': '请选择深度分析维度',
            'options': [
                {'id': 'strategic', 'name': '战略分析'},
                {'id': 'risk', 'name': '风险评估'},
                {'id': 'competitive', 'name': '竞争分析'}
            ]
        }, room=sid)
    
    async def _handle_deep_analysis(self, sid: str, session: Dict[str, Any], selection: str):
        """处理深度分析"""
        await self.sio.emit('processing_status', {
            'step': 'deep_analysis',
            'message': f'正在进行{selection}...',
            'status': 'processing'
        }, room=sid)
        
        await asyncio.sleep(3)
        
        await self.sio.emit('final_result', {
            'step': 'analysis_complete',
            'message': '分析完成',
            'status': 'completed',
            'data': {
                'analysis_type': selection,
                'report': '详细分析报告已生成'
            }
        }, room=sid)

    def _register_backend_handlers(self):
        """注册后端消息处理器"""
        # 注册处理状态更新的转发
        self.backend_client.register_message_handler(
            'processing_status', 
            self._forward_processing_status
        )
        
        # 注册分析结果的转发
        self.backend_client.register_message_handler(
            'analysis_result',
            self._forward_analysis_result
        )
        
        # 注册交互需求的转发
        self.backend_client.register_message_handler(
            'interaction_required',
            self._forward_interaction_required
        )
        
        # 注册最终结果的转发
        self.backend_client.register_message_handler(
            'final_result',
            self._forward_final_result
        )
        
        # 注册错误消息的转发
        self.backend_client.register_message_handler(
            'error',
            self._forward_error
        )
    
    async def _forward_processing_status(self, data: Dict[str, Any]):
        """转发处理状态到前端"""
        # 这里需要根据数据中的session信息找到对应的前端连接
        # 暂时广播到对应的房间
        if 'session_id' in data:
            await self.sio.emit('processing_status', data, room=data['session_id'])
    
    async def _forward_analysis_result(self, data: Dict[str, Any]):
        """转发分析结果到前端"""
        if 'session_id' in data:
            await self.sio.emit('analysis_result', data, room=data['session_id'])
    
    async def _forward_interaction_required(self, data: Dict[str, Any]):
        """转发交互需求到前端"""
        if 'session_id' in data:
            await self.sio.emit('interaction_required', data, room=data['session_id'])
    
    async def _forward_final_result(self, data: Dict[str, Any]):
        """转发最终结果到前端"""
        if 'session_id' in data:
            await self.sio.emit('final_result', data, room=data['session_id'])
    
    async def _forward_error(self, data: Dict[str, Any]):
        """转发错误消息到前端"""
        if 'session_id' in data:
            await self.sio.emit('error', data, room=data['session_id'])

# 创建处理器实例
group_chat_handler = GroupChatHandler()