'use client'

import * as React from 'react'
import {
  Menu
} from 'lucide-react'

import { NavUser } from '@/components/common/nav-user'

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
  useSidebar,
} from '@/components/ui/sidebar'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import ChatGroup, { defaultProps } from '@/components/chat/chat-group'
import { useGroupNotifications } from '@/stores/chat-group-store'
import { ContentWidget } from '@/components/common/content-widget'
import { useSession } from '@/components/providers/session-provider'
import smallLogo from '@/assets/small-logo.svg'
// 导航数据配置
const data = {
  // 默认用户数据，当session未加载时使用
  defaultUser: {
    name: 'SpecificAI',
    email: '<EMAIL>',
    avatar: smallLogo,
  },
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { toggleSidebar } = useSidebar()
  const { unreadCount, hasNotifications } = useGroupNotifications()
  const { user, loading: sessionLoading } = useSession()

  // 使用session中的用户信息，如果没有则使用默认值
  const userInfo = user || data.defaultUser

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader className="h-16 flex items-center">
        {/* <TeamSwitcher teams={data.teams} /> */}
        <div className="flex items-center justify-between w-full">
          <Button variant="ghost" size="icon" className="cursor-pointer" onClick={toggleSidebar}>
            <Menu className="w-5 h-5 text-gray-600" />
          </Button>
        </div>
      </SidebarHeader>
      <SidebarContent className="flex-1 overflow-y-auto overflow-x-hidden" style={{background: '#F1F5F9'}}>
        <div className="w-full flex flex-col gap-4" style={{background: '#F1F5F9'}}>
          {/* 资讯组件 */}
          <ContentWidget type="news" />
          {/* 招投标组件 */}
          <ContentWidget type="tender" />
          {/* 聊天群组 - 无分割，一体化显示 */}
          <ChatGroup {...defaultProps} />
        </div>
      </SidebarContent>
      <SidebarFooter className="w-full" style={{background: '#F1F5F9'}}>
        {/* User Profile */}
        <NavUser user={userInfo} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}
