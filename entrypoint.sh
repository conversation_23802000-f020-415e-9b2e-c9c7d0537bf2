#!/bin/sh
# entrypoint.sh - This script is executed when the Docker container starts.

# Exit immediately if a command exits with a non-zero status.
set -e

echo "Running entrypoint script..."

# Define the target directory for substitutions
# In a Next.js standalone build, client-side files are in .next/static
TARGET_DIR="./.next/static"

if [ -d "$TARGET_DIR" ]; then
    echo "Target directory $TARGET_DIR found. Starting placeholder substitution..."

    # Find all JavaScript files in the target directory and replace placeholders with actual environment variable values.
    # The '|' character is used as a separator in sed to avoid conflicts with URL slashes.
    
    echo "Replacing __NEXT_PUBLIC_API_BASE_URL_PLACEHOLDER__ with ${NEXT_PUBLIC_BASE_URL}"
    find "$TARGET_DIR" -type f -name "*.js" -exec sed -i "s|__NEXT_PUBLIC_API_BASE_URL_PLACEHOLDER__|${NEXT_PUBLIC_BASE_URL}|g" {} +
    
    echo "Replacing __NEXT_PUBLIC_WS_URL_PLACEHOLDER__ with ${NEXT_PUBLIC_WS_URL}"
    find "$TARGET_DIR" -type f -name "*.js" -exec sed -i "s|__NEXT_PUBLIC_WS_URL_PLACEHOLDER__|${NEXT_PUBLIC_WS_URL}|g" {} +
    
    echo "Placeholder substitution complete."
else
    echo "Warning: Target directory $TARGET_DIR not found. Skipping placeholder substitution."
fi


# Execute the command passed to the script (e.g., "node", "server.js")
# This runs the Next.js server.
exec "$@" 