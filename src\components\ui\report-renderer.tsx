'use client'

import { useState, useEffect, useMemo } from 'react'
import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'
import type { ParsedReport, TableOfContentsItem } from '@/lib/websocket/report-processor'

// Shadcn UI 组件
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import {
  FileText,
  Clock,
  BookOpen,
  ChevronDown,
  ChevronRight,
  ExternalLink,
  Copy,
  Check,
} from 'lucide-react'

// Markdown渲染相关
import { useMarkdown, markdownPresets } from '@/hooks/use-markdown'

export interface ReportRendererProps {
  /** Markdown内容 */
  content: string
  /** 解析后的报告数据 */
  report?: ParsedReport
  /** 是否显示目录 */
  showToc?: boolean
  /** 是否显示统计信息 */
  showStats?: boolean
  /** 链接点击回调 */
  onLinkClick?: (url: string) => void
  /** 自定义样式 */
  className?: string
  /** 报告标题 */
  title?: string
}

export const ReportRenderer = ({
  content,
  report,
  showToc = true,
  showStats = true,
  onLinkClick,
  className,
  title = '分析报告',
}: ReportRendererProps) => {
  const [activeHeading, setActiveHeading] = useState<string>('')
  const [isTocOpen, setIsTocOpen] = useState(true)
  const [copiedCode, setCopiedCode] = useState<string>('')

  // 使用markdown hook渲染内容
  const { renderMarkdown, extractHeadings } = useMarkdown(markdownPresets.full)
  const renderedHtml = useMemo(
    () => (content ? renderMarkdown(content) : ''),
    [content, renderMarkdown]
  )
  const isLoading = false // 现有hook是同步的

  // 处理代码复制
  const handleCodeCopy = async (code: string, blockId: string) => {
    try {
      await navigator.clipboard.writeText(code)
      setCopiedCode(blockId)
      setTimeout(() => setCopiedCode(''), 2000)
    } catch (error) {
      // 复制失败的处理
    }
  }

  // 处理锚点导航
  const handleTocClick = (anchor: string) => {
    const element = document.getElementById(anchor)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
      setActiveHeading(anchor)
    }
  }

  // 处理链接点击
  const handleLinkClick = (event: React.MouseEvent<HTMLDivElement>) => {
    const target = event.target as HTMLElement
    if (target.tagName === 'A') {
      event.preventDefault()
      const href = target.getAttribute('href')
      if (href && onLinkClick) {
        onLinkClick(href)
      }
    }
  }

  // 渲染目录项
  const renderTocItem = (item: TableOfContentsItem, level = 0) => (
    <div key={item.id} className={cn('space-y-1', level > 0 && 'ml-4')}>
      <Button
        variant="ghost"
        size="sm"
        className={cn(
          'w-full justify-start text-left h-auto py-1 px-2',
          activeHeading === item.anchor && 'bg-accent text-accent-foreground'
        )}
        onClick={() => handleTocClick(item.anchor)}
      >
        <span
          className={cn(
            'text-xs truncate',
            level === 0 && 'font-medium',
            level === 1 && 'text-muted-foreground',
            level >= 2 && 'text-muted-foreground/80'
          )}
        >
          {item.title}
        </span>
      </Button>
      {item.children.map(child => renderTocItem(child, level + 1))}
    </div>
  )

  // 后处理HTML，添加代码复制按钮等功能
  const processedHtml = useMemo(() => {
    if (!renderedHtml) return ''

    // 为代码块添加复制按钮
    return renderedHtml.replace(
      /<pre><code class="language-(\w+)">([\s\S]*?)<\/code><\/pre>/g,
      (match, language, code) => {
        const blockId = `code-${Math.random().toString(36).substr(2, 9)}`
        const decodedCode = code
          .replace(/&lt;/g, '<')
          .replace(/&gt;/g, '>')
          .replace(/&amp;/g, '&')
          .replace(/&quot;/g, '"')

        return `
          <div class="relative group">
            <div class="flex items-center justify-between bg-muted px-4 py-2 text-sm">
              <span class="text-muted-foreground">${language}</span>
              <button 
                onclick="handleCodeCopy('${encodeURIComponent(decodedCode)}', '${blockId}')"
                class="opacity-0 group-hover:opacity-100 transition-opacity p-1 hover:bg-background rounded"
                title="复制代码"
              >
                ${
                  copiedCode === blockId
                    ? '<svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>'
                    : '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path></svg>'
                }
              </button>
            </div>
            <pre class="!mt-0"><code class="language-${language}">${code}</code></pre>
          </div>
        `
      }
    )
  }, [renderedHtml, copiedCode])

  if (isLoading) {
    return (
      <Card className={cn('w-full', className)}>
        <CardContent className="p-6">
          <div className="flex items-center justify-center space-x-2">
            <motion.div
              className="w-2 h-2 bg-primary rounded-full"
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 0.6, repeat: Infinity, delay: 0 }}
            />
            <motion.div
              className="w-2 h-2 bg-primary rounded-full"
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 0.6, repeat: Infinity, delay: 0.2 }}
            />
            <motion.div
              className="w-2 h-2 bg-primary rounded-full"
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 0.6, repeat: Infinity, delay: 0.4 }}
            />
            <span className="text-sm text-muted-foreground ml-2">正在生成报告...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={cn('w-full space-y-4', className)}>
      {/* 报告头部信息 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="w-5 h-5 text-primary" />
            <span>{title}</span>
          </CardTitle>
          {showStats && report && (
            <CardDescription>
              <div className="flex items-center space-x-4 text-sm">
                <div className="flex items-center space-x-1">
                  <BookOpen className="w-4 h-4" />
                  <span>{report.wordCount} 字</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Clock className="w-4 h-4" />
                  <span>约 {report.readingTime} 分钟阅读</span>
                </div>
                {report.codeBlocks.length > 0 && (
                  <Badge variant="secondary">{report.codeBlocks.length} 个代码块</Badge>
                )}
                {report.tables.length > 0 && (
                  <Badge variant="secondary">{report.tables.length} 个表格</Badge>
                )}
              </div>
            </CardDescription>
          )}
        </CardHeader>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
        {/* 目录侧边栏 */}
        {showToc && report?.tableOfContents && report.tableOfContents.length > 0 && (
          <div className="lg:col-span-1">
            <Card className="sticky top-4">
              <Collapsible open={isTocOpen} onOpenChange={setIsTocOpen}>
                <CollapsibleTrigger asChild>
                  <CardHeader className="cursor-pointer">
                    <CardTitle className="text-sm flex items-center justify-between">
                      <span>目录</span>
                      {isTocOpen ? (
                        <ChevronDown className="w-4 h-4" />
                      ) : (
                        <ChevronRight className="w-4 h-4" />
                      )}
                    </CardTitle>
                  </CardHeader>
                </CollapsibleTrigger>
                <CollapsibleContent>
                  <CardContent className="pt-0">
                    <ScrollArea className="h-[400px]">
                      <div className="space-y-1">
                        {report.tableOfContents.map(item => renderTocItem(item))}
                      </div>
                    </ScrollArea>
                  </CardContent>
                </CollapsibleContent>
              </Collapsible>
            </Card>
          </div>
        )}

        {/* 主要内容区域 */}
        <div
          className={cn(
            showToc && report?.tableOfContents && report.tableOfContents.length > 0
              ? 'lg:col-span-3'
              : 'lg:col-span-4'
          )}
        >
          <Card>
            <CardContent className="p-6">
              <motion.div
                className="prose prose-sm max-w-none dark:prose-invert"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                onClick={handleLinkClick}
                dangerouslySetInnerHTML={{ __html: processedHtml }}
              />
            </CardContent>
          </Card>
        </div>
      </div>

      {/* 报告底部信息 */}
      {report && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <span>生成时间: {report.createdAt.toLocaleString()}</span>
              {report.links.length > 0 && (
                <div className="flex items-center space-x-2">
                  <ExternalLink className="w-3 h-3" />
                  <span>{report.links.length} 个外部链接</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

// 使用 ReportProcessor 的 Hook
import { useEffect as useEffectHook, useState as useStateHook } from 'react'

export interface UseReportRendererProps {
  /** 解析后的报告 */
  report?: ParsedReport
}

export const useReportRenderer = ({ report }: UseReportRendererProps = {}) => {
  const [content, setContent] = useStateHook('')
  const [isLoading, setIsLoading] = useStateHook(false)

  useEffectHook(() => {
    if (report) {
      setContent(report.rawContent)
      setIsLoading(false)
    } else {
      setIsLoading(true)
    }
  }, [report])

  return {
    content,
    isLoading,
    report,
  }
}
