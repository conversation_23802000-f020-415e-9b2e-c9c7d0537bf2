# 🛠️ 开发工具面板使用指南

## 概述

统一的开发工具面板整合了所有开发调试功能，包括Mock模式控制、性能监控和系统信息。

## 快速开始

### 访问面板

1. 在开发环境下访问聊天页面：`http://localhost:3001/chat/[id]`
2. 左上角会显示"开发工具面板"
3. 点击展开按钮查看完整功能

### 面板控制

- **展开/收起**: 点击向上/向下箭头
- **最小化**: 点击最小化图标，显示为小按钮
- **恢复**: 点击小按钮恢复完整面板

## 功能详解

### Mock标签页

#### 模式控制

- **启用Mock**: 切换到Mock模式，使用模拟数据
- **真实模式**: 切换到真实API模式

#### 快速测试

- **🚀 完整演示**: 加载包含所有消息类型的演示对话
- **🗑️ 清空对话**: 清除当前所有消息

#### 消息类型测试

- **⚡ 流式**: 生成流式AI回复消息
- **📝 表单**: 生成信息收集表单
- **📊 报告**: 生成分析报告消息
- **📁 文件**: 生成文件下载消息

#### 智能响应

在聊天输入框输入关键词，自动生成对应类型的响应：

| 关键词          | 生成内容            |
| --------------- | ------------------- |
| "报告"、"分析"  | 流式消息 + 分析报告 |
| "文件"、"下载"  | 流式消息 + 文件下载 |
| "表单"、"填写"  | 信息收集表单        |
| "错误"、"error" | 错误消息            |
| "测试"、"demo"  | 完整功能演示        |

### 性能标签页

#### 连接状态

- **Socket**: 显示WebSocket连接状态
- **请求数**: 显示已发送的请求数量

#### 系统资源

- **内存**: 显示当前内存使用量
- **会话**: 显示活跃会话数量

### 系统标签页

#### 环境信息

- **环境**: 当前运行环境（开发/生产）
- **版本**: 应用版本号
- **时间**: 当前时间

#### 错误模拟

测试各种错误场景：

- **⚠️ 网络**: 模拟网络连接错误
- **⚠️ 服务器**: 模拟服务器错误
- **⚠️ 验证**: 模拟数据验证错误
- **⚠️ 未知**: 模拟未知错误

## 技术细节

### 组件结构

```
DevToolsPanel
├── Mock控制器功能
├── 性能监控功能
└── 系统信息功能
```

### 配置选项

```typescript
interface DevToolsPanelConfig {
  defaultExpanded?: boolean // 默认是否展开
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
  // ... 其他配置
}
```

### 显示条件

- 仅在 `NODE_ENV === 'development'` 时显示
- 生产环境自动隐藏

## 最佳实践

### 开发流程

1. **启动开发**: 使用Mock模式进行前端开发
2. **功能测试**: 使用快速测试验证各种消息类型
3. **性能监控**: 观察系统性能指标
4. **错误测试**: 模拟各种错误场景
5. **真实测试**: 切换到真实模式验证API集成

### 调试技巧

- 使用"完整演示"快速查看所有功能
- 通过性能监控发现性能问题
- 使用错误模拟测试错误处理逻辑
- 智能响应功能快速生成测试数据

## 故障排除

### 面板不显示

- 确认当前为开发环境
- 检查浏览器控制台是否有错误
- 刷新页面重试

### Mock功能不工作

- 确认已启用Mock模式
- 检查网络请求是否被拦截
- 查看控制台日志

### 性能数据不准确

- 等待几秒让数据更新
- 检查WebSocket连接状态
- 重启开发服务器

## 更新日志

### v1.0.0 (2025-01-16)

- ✅ 整合Mock控制器和性能监控
- ✅ 实现标签页设计
- ✅ 添加智能响应功能
- ✅ 支持面板位置配置
- ✅ 移除独立的测试页面

---

**注意**: 此面板仅在开发环境下可用，生产环境会自动隐藏。
