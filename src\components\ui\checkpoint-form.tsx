'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'
import type { FormField, FormFieldOption } from '@/types/websocket-event-type'

// Shadcn UI 组件
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { AlertCircle, CheckCircle2, Loader2 } from 'lucide-react'

export interface CheckpointFormProps {
  /** 表单字段定义 */
  fields: FormField[]
  /** 表单提交回调 */
  onSubmit: (values: Record<string, any>) => void | Promise<void>
  /** 初始值 */
  initialValues?: Record<string, any>
  /** 表单标题 */
  title?: string
  /** 表单描述 */
  description?: string
  /** 是否正在提交 */
  isSubmitting?: boolean
  /** 自定义样式 */
  className?: string
  /** 是否禁用表单 */
  disabled?: boolean
}

export const CheckpointForm = ({
  fields,
  onSubmit,
  initialValues = {},
  title = '请完成以下信息',
  description,
  isSubmitting = false,
  className,
  disabled = false,
}: CheckpointFormProps) => {
  const [formErrors, setFormErrors] = useState<Record<string, string>>({})
  const [isValidating, setIsValidating] = useState(false)

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm({
    defaultValues: initialValues,
  })

  // 监听表单值变化
  const watchedValues = watch()

  // 验证单个字段
  const validateField = (field: FormField, value: any): string | null => {
    // 必填验证
    if (
      field.required &&
      (value === undefined ||
        value === null ||
        value === '' ||
        (Array.isArray(value) && value.length === 0))
    ) {
      return `${field.label}是必填项`
    }

    // 类型特定验证
    if (value !== undefined && value !== null && value !== '') {
      switch (field.type) {
        case 'number':
          if (isNaN(Number(value))) {
            return `${field.label}必须是有效数字`
          }
          break
        case 'date':
          if (!Date.parse(value)) {
            return `${field.label}必须是有效日期`
          }
          break
      }
    }

    return null
  }

  // 验证整个表单
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    fields.forEach(field => {
      const value = watchedValues[field.id]
      const error = validateField(field, value)
      if (error) {
        newErrors[field.id] = error
      }
    })

    setFormErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // 表单提交处理
  const handleFormSubmit = async (data: Record<string, any>) => {
    setIsValidating(true)

    if (!validateForm()) {
      setIsValidating(false)
      return
    }

    try {
      await onSubmit(data)
    } catch (error) {
      // 处理提交错误
    } finally {
      setIsValidating(false)
    }
  }

  // 渲染不同类型的表单字段
  const renderField = (field: FormField) => {
    const fieldError = formErrors[field.id] || errors[field.id]?.message
    const value = watchedValues[field.id]

    const fieldWrapper = (children: React.ReactNode) => (
      <motion.div
        key={field.id}
        className="space-y-2"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Label htmlFor={field.id} className="flex items-center space-x-2">
          <span>{field.label}</span>
          {field.required && (
            <Badge variant="destructive" className="text-xs">
              必填
            </Badge>
          )}
        </Label>
        {children}
        {fieldError && (
          <div className="flex items-center space-x-1 text-sm text-destructive">
            <AlertCircle className="w-4 h-4" />
            <span>
              {typeof fieldError === 'string'
                ? fieldError
                : (fieldError as any)?.message || '输入错误'}
            </span>
          </div>
        )}
      </motion.div>
    )

    switch (field.type) {
      case 'text':
        return fieldWrapper(
          <Input
            id={field.id}
            {...register(field.id, { required: field.required })}
            placeholder={`请输入${field.label}`}
            disabled={disabled || isSubmitting}
            className={cn(fieldError && 'border-destructive')}
          />
        )

      case 'textarea':
        return fieldWrapper(
          <Textarea
            id={field.id}
            {...register(field.id, { required: field.required })}
            placeholder={`请输入${field.label}`}
            disabled={disabled || isSubmitting}
            className={cn('min-h-[100px]', fieldError && 'border-destructive')}
          />
        )

      case 'number':
        return fieldWrapper(
          <Input
            id={field.id}
            type="number"
            {...register(field.id, {
              required: field.required,
              valueAsNumber: true,
            })}
            placeholder={`请输入${field.label}`}
            disabled={disabled || isSubmitting}
            className={cn(fieldError && 'border-destructive')}
          />
        )

      case 'date':
        return fieldWrapper(
          <Input
            id={field.id}
            type="date"
            {...register(field.id, { required: field.required })}
            disabled={disabled || isSubmitting}
            className={cn(fieldError && 'border-destructive')}
          />
        )

      case 'select':
        return fieldWrapper(
          <Select
            value={value || ''}
            onValueChange={selectedValue => setValue(field.id, selectedValue)}
            disabled={disabled || isSubmitting}
          >
            <SelectTrigger className={cn(fieldError && 'border-destructive')}>
              <SelectValue placeholder={`请选择${field.label}`} />
            </SelectTrigger>
            <SelectContent>
              {field.options?.map((option: FormFieldOption) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )

      case 'multiselect':
        return fieldWrapper(
          <div className="space-y-2">
            {field.options?.map((option: FormFieldOption) => {
              const isChecked = Array.isArray(value) && value.includes(option.value)
              return (
                <div key={option.value} className="flex items-center space-x-2">
                  <Checkbox
                    id={`${field.id}-${option.value}`}
                    checked={isChecked}
                    onCheckedChange={checked => {
                      const currentValues = Array.isArray(value) ? value : []
                      if (checked) {
                        setValue(field.id, [...currentValues, option.value])
                      } else {
                        setValue(
                          field.id,
                          currentValues.filter(v => v !== option.value)
                        )
                      }
                    }}
                    disabled={disabled || isSubmitting}
                  />
                  <Label
                    htmlFor={`${field.id}-${option.value}`}
                    className="text-sm font-normal cursor-pointer"
                  >
                    {option.label}
                  </Label>
                </div>
              )
            })}
          </div>
        )

      case 'radio':
        return fieldWrapper(
          <RadioGroup
            value={value || ''}
            onValueChange={selectedValue => setValue(field.id, selectedValue)}
            disabled={disabled || isSubmitting}
          >
            {field.options?.map((option: FormFieldOption) => (
              <div key={option.value} className="flex items-center space-x-2">
                <RadioGroupItem value={option.value} id={`${field.id}-${option.value}`} />
                <Label
                  htmlFor={`${field.id}-${option.value}`}
                  className="text-sm font-normal cursor-pointer"
                >
                  {option.label}
                </Label>
              </div>
            ))}
          </RadioGroup>
        )

      case 'checkbox':
        return fieldWrapper(
          <div className="flex items-center space-x-2">
            <Checkbox
              id={field.id}
              checked={Boolean(value)}
              onCheckedChange={checked => setValue(field.id, checked)}
              disabled={disabled || isSubmitting}
            />
            <Label htmlFor={field.id} className="text-sm font-normal cursor-pointer">
              我确认{field.label}
            </Label>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <Card className={cn('w-full max-w-2xl mx-auto', className)}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <CheckCircle2 className="w-5 h-5 text-primary" />
          <span>{title}</span>
        </CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>

      <CardContent>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {/* 渲染所有字段 */}
          <div className="space-y-4">{fields.map(renderField)}</div>

          {/* 提交按钮 */}
          <div className="flex justify-end space-x-2 pt-4 border-t">
            <Button
              type="submit"
              disabled={disabled || isSubmitting || isValidating}
              className="min-w-[120px]"
            >
              {isSubmitting || isValidating ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  提交中...
                </>
              ) : (
                '提交'
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}

// 使用 CheckpointProcessor 的 Hook
import { useEffect as useEffectHook, useState as useStateHook } from 'react'
import type { FormState } from '@/lib/websocket/checkpoint-processor'

export interface UseCheckpointFormProps {
  /** 表单状态 */
  formState?: FormState
  /** 提交回调 */
  onSubmit?: (formId: string, values: Record<string, any>) => void | Promise<void>
}

export const useCheckpointForm = ({ formState, onSubmit }: UseCheckpointFormProps = {}) => {
  const [fields, setFields] = useStateHook<FormField[]>([])
  const [isSubmitting, setIsSubmitting] = useStateHook(false)

  useEffectHook(() => {
    if (formState) {
      setFields(formState.fields)
      setIsSubmitting(formState.isSubmitting)
    }
  }, [formState])

  const handleSubmit = async (values: Record<string, any>) => {
    if (formState && onSubmit) {
      setIsSubmitting(true)
      try {
        await onSubmit(formState.id, values)
      } finally {
        setIsSubmitting(false)
      }
    }
  }

  return {
    fields,
    isSubmitting,
    handleSubmit,
    formState,
  }
}
