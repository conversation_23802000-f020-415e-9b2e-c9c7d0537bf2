'use client'

/**
 * UnifiedMessage - 统一的消息显示组件
 * 合并了MessageContainer和MessageBubble的所有功能
 * 支持所有消息类型：streaming、静态文本、表单、报告、错误
 * 提供一致的样式和交互体验
 */

import { useMemo, memo, useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { cn } from '@/lib/utils'
import type { BaseWebSocketMessage } from '@/types/websocket-event-type'
import {
  isStreamingPayload,
  isCheckpointPayload,
  isReportPayload,
  isErrorPayload,
} from '@/types/websocket-event-type'

// Mock AI助手ID常量
import { MOCK_AI_ASSISTANT_ID } from '@/lib/mock/mock-data-factory'

// 导入渲染组件
import { TextGenerateEffect } from './text-generate-effect'
import { CheckpointForm } from './checkpoint-form'
import { ReportRenderer } from './report-renderer'
import { ErrorDisplay } from './error-display'

// Shadcn UI 组件
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  User,
  Bot,
  Clock,
  Check,
  AlertCircle,
  Send,
  Loader2,
  Copy,
  MessageSquare,
  MoreHorizontal,
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useSession } from '@/components/providers/session-provider'

export type MessageRole = 'user' | 'assistant' | 'system'
export type MessageStatus = 'sending' | 'sent' | 'delivered' | 'failed'
export type RenderMode = 'bubble' | 'card' | 'system'

// 统一的消息数据接口
export interface UnifiedMessageData {
  id: string
  role: MessageRole
  content: string
  status: MessageStatus
  timestamp: Date
  isStreaming?: boolean
  showCursor?: boolean
}

export interface UnifiedMessageProps {
  /** 消息数据 - 支持BaseWebSocketMessage或简化格式 */
  message?: BaseWebSocketMessage | undefined
  data?: UnifiedMessageData | undefined

  /** 显示设置 */
  showAvatar?: boolean
  showTimestamp?: boolean
  showStatus?: boolean
  renderMode?: RenderMode

  /** 头像设置 */
  userAvatarUrl?: string | undefined
  assistantAvatarUrl?: string | undefined

  /** 交互功能 */
  enableCopy?: boolean
  enableMenu?: boolean

  /** 回调函数 */
  onCopy?: ((content: string) => void) | undefined
  onRetry?: ((messageId: string) => void) | undefined
  onDelete?: ((messageId: string) => void) | undefined
  onClick?: ((messageId: string) => void) | undefined
  onCheckpointSubmit?: ((formId: string, values: Record<string, any>) => void | Promise<void>) | undefined
  onErrorRetry?: ((errorId: string) => void | Promise<void>) | undefined
  onErrorDismiss?: ((errorId: string) => void) | undefined
  onReportLinkClick?: ((url: string) => void) | undefined

  /** 样式 */
  className?: string
}

const UnifiedMessageComponent = ({
  message,
  data,
  showAvatar = true,
  showTimestamp = true,
  showStatus = true,
  renderMode = 'card',
  userAvatarUrl,
  assistantAvatarUrl,
  enableCopy = false,
  enableMenu = false,
  onCopy,
  onRetry,
  onDelete,
  onClick,
  onCheckpointSubmit,
  onErrorRetry,
  onErrorDismiss,
  onReportLinkClick,
  className,
}: UnifiedMessageProps) => {
  const { user } = useSession()
  const [copied, setCopied] = useState(false)
  const [isHovered, setIsHovered] = useState(false)

  // 🎯 核心：统一消息数据处理
  const messageInfo = useMemo(() => {
    if (data) {
      // 使用简化数据格式
      return {
        id: data.id,
        role: data.role,
        content: data.content,
        status: data.status,
        timestamp: data.timestamp,
        isStreaming: data.isStreaming || false,
        showCursor: data.showCursor || false,
        messageType: 'simple' as const,
      }
    } else if (message) {
      // 使用BaseWebSocketMessage格式
      let role: MessageRole = 'assistant'

      // 🔍 角色判断调试信息
      if (process.env.NODE_ENV === 'development') {
        console.log('🔍 角色判断开始:', {
          messageId: message.id,
          messageUserId: message.userId,
          userFromSession: user?.id,
          payloadType: message.payload.type,
          isError: isErrorPayload(message.payload),
          MOCK_AI_ASSISTANT_ID,
          userIdEquals_MOCK_AI_ASSISTANT_ID: message.userId === MOCK_AI_ASSISTANT_ID,
          userIdEquals_SessionUser: message.userId === user?.id,
        })
      }

      // 🎯 简化的角色判断逻辑
      if (isErrorPayload(message.payload)) {
        role = 'system'
        if (process.env.NODE_ENV === 'development') {
          console.log('🚨 系统错误消息:', { role: 'system' })
        }
      } else if (message.userId === MOCK_AI_ASSISTANT_ID) {
        // 🎯 简化：直接匹配AI助手ID常量
        role = 'assistant'
        if (process.env.NODE_ENV === 'development') {
          console.log('🤖 AI助手消息:', { 
            userId: message.userId, 
            role: 'assistant',
            payloadType: message.payload.type
          })
        }
      } else if (message.userId && user && message.userId === user.id) {
        // 🎯 真实用户消息匹配
        role = 'user'
        if (process.env.NODE_ENV === 'development') {
          console.log('👤 用户消息:', { 
            userId: message.userId, 
            userID: user.id, 
            role: 'user',
            payloadType: message.payload.type
          })
        }
      } else {
        // 🎯 默认fallback：其他情况默认为AI助手
        role = 'assistant'
        if (process.env.NODE_ENV === 'development') {
          console.log('🔄 默认AI助手（Fallback）:', {
            userId: message.userId,
            userID: user?.id,
            role: 'assistant',
            payloadType: message.payload.type,
            reason: 'fallback'
          })
        }
      }

      if (process.env.NODE_ENV === 'development') {
        console.log('✅ 角色判断结果:', {
          messageId: message.id,
          finalRole: role,
          messageUserId: message.userId,
          userFromSession: user?.id,
          判断依据: role === 'system' ? '错误消息' : 
                   role === 'assistant' && message.userId === MOCK_AI_ASSISTANT_ID ? 'AI助手ID匹配' :
                   role === 'user' ? '用户ID匹配' : 'Fallback到助手'
        })
      }

      // 内容提取
      let content = ''
      let isStreaming = false
      let showCursor = false

      if (isStreamingPayload(message.payload)) {
        content = message.metadata?.streamingState?.accumulatedText || message.payload.delta || ''
        isStreaming = !message.payload.isComplete
        showCursor = !message.payload.isComplete
      } else if (isReportPayload(message.payload)) {
        content = message.payload.content
      } else if (isErrorPayload(message.payload)) {
        content = `错误: ${message.payload.message}`
      } else if (isCheckpointPayload(message.payload)) {
        content = '表单交互'
      }

      return {
        id: message.id,
        role,
        content,
        status: (message.status as MessageStatus) || 'delivered',
        timestamp: new Date(message.timestamp),
        isStreaming,
        showCursor,
        messageType: 'websocket' as const,
        payload: message.payload,
      }
    }

    throw new Error('UnifiedMessage requires either message or data prop')
  }, [message, data, user])

  // 获取头像信息
  const getAvatarInfo = () => {
    const { role } = messageInfo

    if (role === 'user') {
      return {
        name: '我',
        src: userAvatarUrl,
        fallback: 'U',
        icon: User,
        className: 'bg-primary text-primary-foreground',
      }
    }
    if (role === 'assistant') {
      return {
        name: 'AI助手',
        src: assistantAvatarUrl,
        fallback: 'AI',
        icon: Bot,
        className: 'bg-secondary text-secondary-foreground',
      }
    }
    return {
      name: '系统',
      src: undefined,
      fallback: 'S',
      icon: MessageSquare,
      className: 'bg-muted text-muted-foreground',
    }
  }

  // 复制功能
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(messageInfo.content)
      setCopied(true)
      onCopy?.(messageInfo.content)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      // Failed to copy text, ignore the error
    }
  }

  // 格式化时间戳
  const formatTimestamp = (timestamp: Date) => {
    const now = new Date()
    const diff = now.getTime() - timestamp.getTime()

    if (diff < 60000) {
      return '刚刚'
    } else if (diff < 3600000) {
      return `${Math.floor(diff / 60000)}分钟前`
    } else if (diff < 86400000) {
      return timestamp.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
    } else {
      return timestamp.toLocaleDateString('zh-CN', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      })
    }
  }

  // 获取状态图标
  const getStatusIcon = () => {
    const { status } = messageInfo

    switch (status) {
      case 'sending':
        return <Loader2 className="w-3 h-3 animate-spin" />
      case 'sent':
        return <Send className="w-3 h-3" />
      case 'delivered':
        return <Check className="w-3 h-3" />
      case 'failed':
        return <AlertCircle className="w-3 h-3 text-destructive" />
      default:
        return <Check className="w-3 h-3" />
    }
  }

  // 渲染消息内容
  const renderMessageContent = () => {
    const { messageType } = messageInfo

    if (messageType === 'websocket' && message) {
      const { payload } = message

      // 基于payload类型进行条件渲染
      if (isErrorPayload(payload)) {
        return (
          <ErrorDisplay
            error={{
              code: payload.code,
              message: payload.message,
              timestamp: messageInfo.timestamp,
              severity: 'error' as any,
            }}
            {...(onErrorRetry && { onRetry: () => onErrorRetry(messageInfo.id) })}
            {...(onErrorDismiss && { onDismiss: () => onErrorDismiss(messageInfo.id) })}
            showTechnicalDetails={true}
          />
        )
      }

      if (isCheckpointPayload(payload)) {
        return (
          <CheckpointForm
            fields={payload.fields}
            onSubmit={
              onCheckpointSubmit ? values => onCheckpointSubmit(messageInfo.id, values) : () => {}
            }
            isSubmitting={false}
            initialValues={{}}
          />
        )
      }

      if (isReportPayload(payload)) {
        return (
          <ReportRenderer
            content={payload.content}
            {...(onReportLinkClick && { onLinkClick: onReportLinkClick })}
          />
        )
      }

      if (isStreamingPayload(payload)) {
        const accumulatedText =
          message.metadata?.streamingState?.accumulatedText || payload.delta || ''

        if (!accumulatedText && !payload.isComplete) {
          return null
        }

        return (
          <TextGenerateEffect
            words={accumulatedText}
            isStreaming={messageInfo.isStreaming}
            showCursor={messageInfo.showCursor}
            characterDelay={30}
            filter={false}
            className="text-foreground"
          />
        )
      }
    }

    // 简单文本内容渲染
    const { content, isStreaming, showCursor } = messageInfo

    if (isStreaming) {
      return (
        <TextGenerateEffect
          words={content}
          isStreaming={isStreaming}
          showCursor={showCursor}
          characterDelay={30}
          filter={false}
          className="text-foreground"
        />
      )
    }

    return <div className="whitespace-pre-wrap break-words text-foreground">{content}</div>
  }

  const avatarInfo = getAvatarInfo()
  const { role, id } = messageInfo
  const isUser = role === 'user'
  const isSystem = role === 'system'

  // 系统消息的特殊渲染
  if (isSystem && renderMode === 'system') {
    return (
      <motion.div
        className={cn('flex justify-center py-3', className)}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Badge variant="outline" className="text-xs">
          {messageInfo.content}
        </Badge>
      </motion.div>
    )
  }

  // 气泡模式渲染
  if (renderMode === 'bubble') {
    return (
      <motion.div
        className={cn(
          'flex gap-3 max-w-[85%] group',
          isUser ? 'ml-auto flex-row-reverse' : 'mr-auto',
          className
        )}
        initial={{ opacity: 0, y: 20, scale: 0.95 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        transition={{ duration: 0.3 }}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* 头像 */}
        {showAvatar && (
          <Avatar className="flex-shrink-0 w-[32px] h-[32px] rounded-[9999px]">
            {avatarInfo.src && <AvatarImage src={avatarInfo.src} />}
            <AvatarFallback
              className={cn('bg-figma-user-avatar text-figma-text')}
              style={{
                backgroundColor: 'var(--figma-user-avatar, #DBEAFE)',
                color: 'var(--figma-text, #252525)',
                width: '32px',
                height: '32px',
                borderRadius: '9999px',
                fontFamily: 'Inter, sans-serif',
                fontWeight: 500,
                fontSize: '14px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              {avatarInfo.fallback}
            </AvatarFallback>
          </Avatar>
        )}

        <div className="flex flex-col gap-1 flex-1 min-w-0">
          {/* 消息头部信息 */}
          {(showTimestamp || showStatus) && (
            <div
              className={cn(
                'flex items-center gap-2 text-xs text-muted-foreground px-1',
                isUser && 'flex-row-reverse'
              )}
            >
              <span
                className="font-inter font-medium text-figma-username leading-[1.5em]"
                style={{
                  fontFamily: 'Inter, sans-serif',
                  fontWeight: 500,
                  fontSize: '16px',
                  lineHeight: '1.5em',
                  color: 'var(--figma-username, #252525)',
                }}
              >
                {isUser ? 'You' : role === 'assistant' ? 'AI助手' : 'System'}
              </span>
              {showTimestamp && (
                <span
                  className="font-inter font-normal text-figma-timestamp leading-[1.5em]"
                  style={{
                    fontFamily: 'Inter, sans-serif',
                    fontWeight: 400,
                    fontSize: '11px',
                    lineHeight: '1.5em',
                    color: 'var(--figma-timestamp, #9CA3AF)',
                  }}
                >
                  {messageInfo.timestamp.toLocaleTimeString([], {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: true,
                  })}
                </span>
              )}
              {showStatus && (
                <>
                  <span>•</span>
                  <div className="flex items-center gap-1">
                    {getStatusIcon()}
                    <Badge
                      variant={messageInfo.status === 'failed' ? 'destructive' : 'secondary'}
                      className="text-xs"
                    >
                      {messageInfo.status === 'sending' && '发送中'}
                      {messageInfo.status === 'sent' && '已发送'}
                      {messageInfo.status === 'delivered' && '已送达'}
                      {messageInfo.status === 'failed' && '发送失败'}
                    </Badge>
                  </div>
                </>
              )}
            </div>
          )}

          {/* 消息气泡 */}
          <div
            className={cn(
              'px-[22px] py-[14px] rounded-[26px] font-inter font-normal text-base leading-[1.5em]',
              'whitespace-pre-wrap break-words relative group/bubble',
              // 基于Figma设计的精确样式：Inter 400 16px 行高1.5em
              isUser
                ? 'bg-figma-user-bg text-figma-text max-w-[606px]'
                : 'bg-figma-assistant-bg text-figma-text max-w-[714px]',
              messageInfo.status === 'failed' && 'border border-destructive/50'
            )}
            style={{
              // CSS变量fallback，再fallback到直接颜色值
              backgroundColor: isUser
                ? 'var(--figma-user-bg, #F7F7F7)'
                : 'var(--figma-assistant-bg, #E7EEFE)',
              color: 'var(--figma-text, #252525)',
              fontFamily: 'Inter, sans-serif',
              fontWeight: 400,
              fontSize: '16px',
              lineHeight: '1.5em',
            }}
          >
            {renderMessageContent()}

            {/* 交互按钮 */}
            <AnimatePresence>
              {isHovered && (enableCopy || enableMenu) && (
                <motion.div
                  className={cn('absolute -bottom-8 flex gap-1', isUser ? 'left-0' : 'right-0')}
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.2 }}
                >
                  {enableCopy && (
                    <Button
                      size="sm"
                      variant="ghost"
                      className="h-6 w-6 p-0 bg-background shadow-sm border"
                      onClick={handleCopy}
                      title="复制"
                    >
                      {copied ? (
                        <Check className="w-3 h-3 text-green-500" />
                      ) : (
                        <Copy className="w-3 h-3" />
                      )}
                    </Button>
                  )}

                  {enableMenu && (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          size="sm"
                          variant="ghost"
                          className="h-6 w-6 p-0 bg-background shadow-sm border"
                          title="更多选项"
                        >
                          <MoreHorizontal className="w-3 h-3" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent side="top" className="w-32">
                        {messageInfo.status === 'failed' && onRetry && (
                          <DropdownMenuItem onClick={() => onRetry(id)}>重试发送</DropdownMenuItem>
                        )}
                        {onDelete && (
                          <DropdownMenuItem
                            onClick={() => onDelete(id)}
                            className="text-destructive focus:text-destructive"
                          >
                            删除消息
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </motion.div>
    )
  }

  // 卡片模式渲染（默认）
  return (
    <motion.div
      className={cn(
        'flex w-full gap-3 px-4 py-3',
        isUser && 'flex-row-reverse',
        isSystem && 'justify-center',
        className
      )}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* 头像 */}
      {showAvatar && !isSystem && (
        <Avatar className="flex-shrink-0 w-8 h-8">
          {avatarInfo.src && <AvatarImage src={avatarInfo.src} />}
          <AvatarFallback
            className={cn(isUser ? 'bg-figma-user-avatar text-figma-text' : avatarInfo.className)}
            style={
              isUser
                ? {
                    backgroundColor: 'var(--figma-user-avatar, #DBEAFE)',
                    color: 'var(--figma-text, #252525)',
                  }
                : {}
            }
          >
            {avatarInfo.fallback}
          </AvatarFallback>
        </Avatar>
      )}

      {/* 消息内容区域 */}
      <div
        className={cn(
          'flex flex-col flex-1 max-w-[80%]',
          isUser && 'items-end',
          isSystem && 'items-center max-w-md'
        )}
      >
        {/* 消息头部信息 */}
        {(showTimestamp || showStatus) && !isSystem && (
          <div
            className={cn(
              'flex items-center gap-2 mb-1 text-xs text-muted-foreground',
              isUser && 'flex-row-reverse'
            )}
          >
            <span className="text-base font-medium text-figma-text leading-6">
              {isUser ? 'You' : role === 'assistant' ? 'AI助手' : 'System'}
            </span>
            {showTimestamp && (
              <>
                <span>•</span>
                <span className="text-xs font-normal text-figma-timestamp leading-6">
                  {messageInfo.timestamp.toLocaleTimeString([], {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: true,
                  })}
                </span>
              </>
            )}
            {showStatus && (
              <>
                <span>•</span>
                <div className="flex items-center gap-1">
                  {getStatusIcon()}
                  <Badge
                    variant={messageInfo.status === 'failed' ? 'destructive' : 'secondary'}
                    className="text-xs"
                  >
                    {messageInfo.status === 'sending' && '发送中'}
                    {messageInfo.status === 'sent' && '已发送'}
                    {messageInfo.status === 'delivered' && '已送达'}
                    {messageInfo.status === 'failed' && '发送失败'}
                  </Badge>
                </div>
              </>
            )}
          </div>
        )}

        {/* 消息内容 */}
        <div
          className={cn('w-full', isSystem ? 'text-center' : isUser ? 'text-right' : 'text-left')}
        >
          {isSystem ? (
            <Badge variant="outline" className="text-xs">
              {messageInfo.content}
            </Badge>
          ) : (
            <div
              className={cn(
                'px-[22px] py-[14px] rounded-[26px] font-normal text-base leading-6',
                'whitespace-pre-wrap break-words',
                // 尝试使用Tailwind类，有fallback
                isUser
                  ? 'bg-figma-user-bg text-figma-text max-w-[606px]'
                  : 'bg-figma-assistant-bg text-figma-text max-w-[714px]'
              )}
              style={{
                // CSS变量fallback，再fallback到直接颜色值
                backgroundColor: isUser
                  ? 'var(--figma-user-bg, #F7F7F7)'
                  : 'var(--figma-assistant-bg, #E7EEFE)',
                color: 'var(--figma-text, #252525)',
              }}
            >
              {renderMessageContent()}
            </div>
          )}
        </div>
      </div>
    </motion.div>
  )
}

// 🎯 React.memo优化 - 自定义比较函数确保精准重渲染控制
export const UnifiedMessage = memo(UnifiedMessageComponent, (prevProps, nextProps) => {
  // 检查消息数据是否变化
  const messageChanged =
    prevProps.message !== nextProps.message || prevProps.data !== nextProps.data

  // 检查UI相关属性
  const uiPropsChanged =
    prevProps.showAvatar !== nextProps.showAvatar ||
    prevProps.showTimestamp !== nextProps.showTimestamp ||
    prevProps.showStatus !== nextProps.showStatus ||
    prevProps.renderMode !== nextProps.renderMode ||
    prevProps.className !== nextProps.className ||
    prevProps.userAvatarUrl !== nextProps.userAvatarUrl ||
    prevProps.assistantAvatarUrl !== nextProps.assistantAvatarUrl

  // 只有在关键内容变化时才重新渲染
  const shouldRerender = messageChanged || uiPropsChanged

  return !shouldRerender // memo返回true表示不重渲染
})

export default UnifiedMessage
