/**
 * 业务路由处理中间件
 * 统一处理所有业务重定向逻辑，消除多次重定向问题
 */

import { NextRequest, NextResponse } from 'next/server'
import { EnhancedMiddlewareFunction, MiddlewareContext, MiddlewareResult } from '../utils/types'
import { RouteClassifier } from '../utils/route-matcher'
import {
  getRedirectDecision,
  validateRedirectDecision,
  logRedirectDecision,
  RedirectDecision,
} from './redirect-rules'

/**
 * 业务路由中间件主函数
 * 这是解决重定向链条问题的核心中间件
 */
export const businessRouteMiddleware: EnhancedMiddlewareFunction = async (
  request: NextRequest,
  context: MiddlewareContext
): Promise<MiddlewareResult> => {
  const { pathname } = context

  // 跳过不需要业务逻辑处理的路由
  if (shouldSkipBusinessLogic(pathname)) {
    return null
  }

  try {
    // 记录业务逻辑开始时间
    const businessStartTime = Date.now()

    // 获取重定向决策
    const decision = getRedirectDecision(request, context as any)

    // 验证重定向决策，防止循环重定向
    const validatedDecision = validateRedirectDecision(decision, pathname)

    // 记录重定向决策
    logRedirectDecision(validatedDecision, {
      pathname,
      isAuthenticated: context.isAuthenticated || false,
    })

    // 记录业务逻辑耗时
    if (context.timing) {
      context.timing.business = Date.now() - businessStartTime
    }

    // 执行重定向
    if (validatedDecision.shouldRedirect && validatedDecision.target) {
      return createBusinessRedirect(request, validatedDecision.target, validatedDecision.reason)
    }

    // 不需要重定向，继续执行
    return null
  } catch (error) {
    console.error('[Business Route Middleware] 业务路由处理失败:', error)

    // 出错时的降级处理
    return handleBusinessError(request, context, error)
  }
}

/**
 * 聊天页面重定向优化中间件
 * 专门处理 /chat -> /chat/1 的重定向，移到中间件层处理
 */
export const chatRouteOptimizer: EnhancedMiddlewareFunction = async (
  request: NextRequest,
  context: MiddlewareContext
): Promise<MiddlewareResult> => {
  const { pathname } = context

  // 只处理精确的 /chat 路径
  if (pathname !== '/chat') {
    return null
  }

  // 检查用户是否有权限访问聊天页面
  const { isAuthenticated, isAdmin } = context
  const hasCompanyProfile = (context as any).hasCompanyProfile

  // 未认证用户交给业务路由中间件处理
  if (!isAuthenticated) {
    return null
  }

  // 需要完善资料的用户交给业务路由中间件处理
  if (!isAdmin && hasCompanyProfile === false) {
    return null
  }

  // 有权限的用户直接重定向到默认群聊，避免客户端重定向
  const defaultGroupId = '1'
  const targetPath = `/chat/${defaultGroupId}`
  const targetUrl = new URL(targetPath, request.url)

  logBusinessAction('聊天页面优化重定向', {
    from: pathname,
    to: targetPath,
    reason: '避免客户端重定向',
  })

  return NextResponse.redirect(targetUrl, 302)
}

/**
 * 条件业务中间件
 * 根据特定条件执行业务逻辑
 */
export const conditionalBusinessMiddleware = (
  condition: (request: NextRequest, context: MiddlewareContext) => boolean,
  businessLogic: EnhancedMiddlewareFunction
): EnhancedMiddlewareFunction => {
  return async (request: NextRequest, context: MiddlewareContext): Promise<MiddlewareResult> => {
    if (condition(request, context)) {
      return businessLogic(request, context)
    }
    return null
  }
}

/**
 * 轻量级业务中间件
 * 只处理最基本的重定向逻辑，适用于高性能场景
 */
export const lightweightBusinessMiddleware: EnhancedMiddlewareFunction = async (
  request: NextRequest,
  context: MiddlewareContext
): Promise<MiddlewareResult> => {
  const { pathname, isAuthenticated } = context

  // 只处理最基本的情况
  if (pathname === '/') {
    if (!isAuthenticated) {
      return createBusinessRedirect(request, '/login', '轻量级处理：未认证用户')
    }
    return createBusinessRedirect(request, '/chat', '轻量级处理：已认证用户')
  }

  // 基本的受保护页面检查
  if (RouteClassifier.isProtectedRoute(pathname) && !isAuthenticated) {
    return createBusinessRedirect(request, '/login', '轻量级处理：受保护页面需要登录')
  }

  return null
}

/**
 * 业务中间件组合器
 * 将多个业务中间件按优先级组合
 */
export function createBusinessMiddlewareChain(): EnhancedMiddlewareFunction[] {
  return [
    // 1. 聊天页面重定向优化（最高优先级）
    chatRouteOptimizer,

    // 2. 主要业务逻辑
    businessRouteMiddleware,

    // 3. 可以添加更多特定的业务中间件
    // 例如：A/B测试、特性开关等
  ]
}

/**
 * 判断是否应该跳过业务逻辑处理
 */
function shouldSkipBusinessLogic(pathname: string): boolean {
  return (
    // 静态资源
    RouteClassifier.shouldSkipMiddleware(pathname) ||
    // API 路由
    RouteClassifier.isApiRoute(pathname) ||
    // 嵌入页面
    RouteClassifier.isEmbedRoute(pathname) ||
    // 认证 API
    pathname.startsWith('/api/auth/')
  )
}

/**
 * 创建业务重定向响应
 */
function createBusinessRedirect(
  request: NextRequest,
  target: string,
  reason?: string
): NextResponse {
  const targetUrl = new URL(target, request.url)

  logBusinessAction('执行业务重定向', {
    from: request.nextUrl.pathname,
    to: target,
    reason: reason || '业务逻辑重定向',
  })

  return NextResponse.redirect(targetUrl, 302)
}

/**
 * 业务错误处理
 */
function handleBusinessError(
  request: NextRequest,
  context: MiddlewareContext,
  error: any
): MiddlewareResult {
  const { pathname, isAuthenticated } = context

  console.error('[Business Error]', {
    pathname,
    isAuthenticated,
    error: error instanceof Error ? error.message : 'Unknown error',
  })

  // 错误时的安全降级策略
  if (!isAuthenticated && !RouteClassifier.isPublicRoute(pathname)) {
    return createBusinessRedirect(request, '/login', '错误降级：重定向到登录页')
  }

  // 其他情况允许继续
  return null
}

/**
 * 业务动作日志记录
 */
function logBusinessAction(
  action: string,
  details: {
    from?: string
    to?: string
    reason?: string
    [key: string]: any
  }
): void {
  if (process.env.NODE_ENV === 'development') {
    console.log(`[Business Action] ${action}`, details)
  }
}

/**
 * 业务中间件工厂
 * 根据配置创建定制的业务中间件
 */
export function createBusinessMiddleware(
  options: {
    enableChatOptimization?: boolean
    enableLightweightMode?: boolean
    customRedirectRules?: Array<{
      condition: (pathname: string) => boolean
      target: string
      reason: string
    }>
  } = {}
): EnhancedMiddlewareFunction {
  const {
    enableChatOptimization = true,
    enableLightweightMode = false,
    customRedirectRules = [],
  } = options

  return async (request: NextRequest, context: MiddlewareContext): Promise<MiddlewareResult> => {
    // 轻量级模式
    if (enableLightweightMode) {
      return lightweightBusinessMiddleware(request, context)
    }

    // 自定义重定向规则
    for (const rule of customRedirectRules) {
      if (rule.condition(context.pathname)) {
        return createBusinessRedirect(request, rule.target, rule.reason)
      }
    }

    // 聊天页面优化
    if (enableChatOptimization) {
      const chatResult = await chatRouteOptimizer(request, context)
      if (chatResult) return chatResult
    }

    // 主要业务逻辑
    return businessRouteMiddleware(request, context)
  }
}

/**
 * 业务中间件性能监控
 */
export const businessPerformanceMonitor: EnhancedMiddlewareFunction = async (
  request: NextRequest,
  context: MiddlewareContext
): Promise<MiddlewareResult> => {
  const startTime = Date.now()

  // 执行业务逻辑
  const result = await businessRouteMiddleware(request, context)

  const duration = Date.now() - startTime

  // 记录性能指标
  if (duration > 50) {
    // 超过50ms记录警告
    console.warn(`[Business Performance] 业务逻辑执行耗时: ${duration}ms`, {
      pathname: context.pathname,
      hasRedirect: !!result,
    })
  }

  return result
}

/**
 * 导出默认的业务中间件配置
 */
export const defaultBusinessMiddleware = createBusinessMiddleware({
  enableChatOptimization: true,
  enableLightweightMode: false,
})
