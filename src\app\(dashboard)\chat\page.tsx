'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Loading from '@/app/loading'
/**
 * Chat Page - AI对话主页面
 *
 * 重构为动态路由，自动重定向到默认群聊
 */
export default function ChatPage() {
  const router = useRouter()

  useEffect(() => {
    // 重定向到默认群聊
    const defaultGroupId = '1'
    router.push(`/chat/${defaultGroupId}`)
  }, [router])

  return (
    <div className="flex items-center justify-center h-full">
      <Loading />
    </div>
  )
}
